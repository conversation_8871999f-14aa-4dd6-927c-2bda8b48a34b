# 聚类功能设置指南

## 概述

第三步聚类功能已经添加到原型系统中，包括：
1. 对比学习特征提取
2. UMAP降维 + HDBSCAN聚类分析

## 新增功能

### API端点

1. **提取对比学习特征**
   - 端点: `POST /api/unknown_traffic/extract_contrastive_features`
   - 输入: TSV文件（第二步的预测结果）
   - 输出: 包含高质量特征的TSV文件

2. **执行聚类分析**
   - 端点: `POST /api/unknown_traffic/perform_clustering`
   - 输入: 包含特征的TSV文件
   - 输出: 包含聚类标签的TSV文件
   - 参数: min_cluster_size=25, umap_neighbors=5

### 文件结构

```
prototype_system/backend/app/unknown_traffic_model/
├── contrastive_feature_extractor.py  # 对比学习特征提取
├── cluster_analyzer.py               # 聚类分析
├── pre-trained_model.bin             # ET-BERT预训练模型
├── encryptd_vocab.txt                # 词汇表
├── contrastive_bert_config.json      # 模型配置
└── requirements_clustering.txt       # 聚类依赖列表
```

## 安装步骤

### 1. 重新构建Docker镜像

由于添加了新的依赖（umap-learn, hdbscan），需要重新构建Docker镜像：

```bash
cd prototype_system
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 验证安装

检查容器日志确保依赖安装成功：

```bash
docker-compose logs backend
docker-compose logs worker
```

如果看到关于umap或hdbscan的错误，说明依赖安装失败。

## 使用流程

### 完整的三步流程

#### 后端API
1. **特征提取** (第一步)
   - API: `POST /api/unknown_traffic/extract_features`
   - 输入: ZIP文件（包含PCAP文件）
   - 输出: 包含SNI特征的TSV文件

2. **未知识别** (第二步)
   - API: `POST /api/unknown_traffic/predict`
   - 输入: 特征TSV文件
   - 输出: 包含预测结果的TSV文件

3. **聚类分析** (第三步)
   - 3a. API: `POST /api/unknown_traffic/extract_contrastive_features`
     - 输入: 预测结果TSV文件
     - 处理: 2轮对比学习微调 + 特征提取
     - 输出: 包含64维对比学习特征的TSV文件
   - 3b. API: `POST /api/unknown_traffic/perform_clustering`
     - 输入: 特征TSV文件
     - 输出: 包含聚类标签的TSV文件

#### 前端界面
访问 `http://localhost:3000` 进入原型系统，选择"未知识别与聚类分析"页面：

1. **第一步：特征提取**
   - 拖拽上传ZIP文件
   - 点击"开始提取特征"
   - 下载生成的特征文件

2. **第二步：模型分析**
   - 上传第一步生成的TSV文件
   - 点击"开始分析"
   - 查看识别结果和混淆矩阵

3. **第三步A：对比学习特征提取**
   - 上传第二步的预测结果文件
   - 点击"提取对比学习特征"
   - 下载64维特征文件

4. **第三步B：聚类分析**
   - 上传第三步A的特征文件
   - 点击"开始聚类分析"
   - 查看聚类结果和统计信息

### 聚类参数

- **min_cluster_size**: 25 (最小聚类大小)
- **umap_neighbors**: 5 (UMAP邻居数量)
- **umap_dims**: 64 (UMAP降维目标维度)

这些参数已经根据要求预设，无需手动调整。

## 故障排除

### 1. Celery Worker导入错误

如果看到类似 `ImportError: cannot import name 'str2tokenizer'` 的错误：

```bash
# 重新构建镜像
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 模型参数错误

如果看到 `AttributeError: 'Namespace' object has no attribute 'mask'` 错误：

这个问题已经修复。如果仍然遇到，请重启worker：

```bash
docker-compose restart worker
```

### 3. 模型尺寸不匹配错误

如果看到 `size mismatch for embedding.position_embedding.weight` 错误：

这个问题已经修复（序列长度已调整为512以匹配预训练模型）。如果仍然遇到，请重启worker：

```bash
docker-compose restart worker
```

### 4. JSON序列化错误

如果看到 `kombu.exceptions.EncodeError: keys must be str, int, float, bool or None, not numpy.int64` 错误：

这个问题已经修复（所有返回值已转换为Python原生类型）。如果仍然遇到，请重启worker：

```bash
docker-compose restart worker
```

### 2. 依赖安装失败

如果Docker构建时依赖安装失败，可能需要：

- 检查网络连接
- 增加Docker构建超时时间：`docker-compose build --build-arg TIMEOUT=3600`
- 手动进入容器安装依赖

### 3. 聚类库缺失

如果遇到 `umap` 或 `hdbscan` 导入错误：

```bash
# 检查依赖是否安装
docker exec -it prototype_system-backend-1 pip list | grep -E "(umap|hdbscan)"

# 手动安装（如果需要）
docker exec -it prototype_system-backend-1 pip install umap-learn hdbscan
```

### 4. 内存不足

聚类算法可能需要较多内存，如果遇到内存不足：

- 增加Docker容器内存限制
- 减少批处理大小
- 使用更小的数据集进行测试

### 5. GPU支持

特征提取使用GPU加速，确保：

- Docker支持GPU
- NVIDIA驱动正确安装
- docker-compose.yml中GPU配置正确

## 技术细节

### 模型架构

- 基于ET-BERT的对比学习模型
- 投影维度: 64
- 池化方式: first (CLS token)
- 序列长度: 512（与预训练模型匹配）

### 对比学习微调

- **微调轮数**: 2个epoch
- **学习率**: 5e-5
- **温度参数**: 0.1
- **优化器**: AdamW (weight_decay=0.01)
- **损失函数**: InfoNCE Loss
- **梯度裁剪**: max_norm=1.0

### 性能优化

- **批次大小**: 4 (与原始脚本一致)
- **序列长度**: 64 (优化性能)
- **位置嵌入**: 智能处理尺寸不匹配
- **进度显示**: 实时显示训练进度
- **内存优化**: 适配Docker环境限制

### 聚类算法

- UMAP降维: 保持64维（不降维）
- HDBSCAN聚类: 基于密度的聚类，在64维空间进行
- 噪声处理: 自动识别噪声点

### 数据格式兼容性

系统自动适配不同的输入格式：

**预测器输出格式**（第二步 → 第三步A）：
- `text_a`: 原始文本
- `ground_truth_name`: 真实标签
- `label`: 二分类标签
- `raw_sni`: SNI信息

**标准化输出格式**（第三步A → 第三步B）：
- `original_text_a`: 原始文本
- `original_ground_truth_name`: 真实标签
- `embedding`: 64维特征向量
- 其他原有列保持不变

## 系统封闭性

所有依赖都已包含在Docker镜像中，无需外部文件：

- ✅ 预训练模型已内置
- ✅ 词汇表已内置
- ✅ 配置文件已内置
- ✅ 聚类算法已内置

系统完全独立，不依赖任何外部资源。
