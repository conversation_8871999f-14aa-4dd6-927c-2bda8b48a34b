# DF模型批量测试功能说明

## 功能概述

为DF模型添加了批量测试功能，允许用户上传两个PKL文件进行模型评估：
- X_test.pkl: 包含测试集特征数据
- y_test.pkl: 包含测试集标签数据

## 前端界面

在DF模型页面中新增了"DF 模型 PKL 评估"标签页，包含：

1. **文件上传区域**：
   - 特征文件 (X_test.pkl) 上传
   - 标签文件 (y_test.pkl) 上传

2. **评估结果展示**：
   - 准确率、精确率、召回率、F1分数
   - 混淆矩阵可视化

## 后端API

新增API端点：`POST /api/evaluate/df_pkl`

### 请求参数
- `test_x_file`: 特征文件 (X_test.pkl)
- `test_y_file`: 标签文件 (y_test.pkl)

### 响应格式
```json
{
  "accuracy": "0.9500",
  "precision": "0.9450",
  "recall": "0.9400",
  "f1_score": "0.9425",
  "confusion_matrix": [[100, 2], [3, 95]],
  "class_names": ["勒索软件", "恶意文件", "扫描探测", "木马流量", "致瘫攻击", "良性", "隐蔽传输", "高危漏洞"],
  "summary": {
    "total_samples": 200,
    "correct_predictions": 190
  }
}
```

## 数据格式要求

### X_test.pkl
- 类型：numpy数组
- 形状：(n_samples, 5000) 或 (n_samples, 5000, 1)
- 内容：方向序列数据，每个样本包含5000个时间步的方向信息

### y_test.pkl
- 类型：numpy数组
- 形状：(n_samples,)
- 内容：对应的类别标签 (0-7)

## DF模型类别映射

```python
{
    0: "勒索软件",
    1: "恶意文件", 
    2: "扫描探测",
    3: "木马流量",
    4: "致瘫攻击",
    5: "良性",
    6: "隐蔽传输",
    7: "高危漏洞"
}
```

## 使用步骤

1. 访问DF模型页面
2. 切换到"DF 模型 PKL 评估"标签页
3. 上传X_test.pkl和y_test.pkl文件
4. 点击"开始评估"按钮
5. 等待评估完成，查看结果

## 技术实现

- 前端：React + Ant Design，使用Tabs组件分离单文件分析和批量评估
- 后端：FastAPI，同步处理PKL文件上传和模型评估
- 模型：使用与单文件分析相同的DF模型权重和架构
- 评估指标：使用sklearn计算准确率、精确率、召回率、F1分数和混淆矩阵
