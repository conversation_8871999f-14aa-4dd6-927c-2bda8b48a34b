# 原型系统移植指南

## 概述

本文档说明如何将网络流量分析原型系统从一台电脑移植到另一台电脑，以及需要注意的配置问题。

## 🚨 移植前的风险评估

### 已修复的问题
- ✅ **LEXNet配置文件路径问题**：已将硬编码的绝对路径改为相对路径

### 仍需注意的问题

#### 1. 模型权重文件路径
各模型的权重文件使用相对路径，移植时需确保文件存在：
- `backend/app/df_model/model_weights.pth`
- `backend/app/yatc_model/checkpoint-best.pth`
- `backend/app/lexnet_model/best_model.pth`
- 其他模型的权重文件

#### 2. 数据集路径
LEXNet模型的数据集路径已改为相对路径，但需要确保数据目录存在：
- `./data/pcap_dataset/` - PCAP文件目录
- `./data/MyAttackDataset_balanced_refactored/` - 生成的数据集目录

#### 3. 系统依赖
- Python环境和依赖包
- CUDA/GPU驱动（如果使用GPU）
- Docker环境（如果使用容器部署）

## 📋 移植步骤

### 1. 准备目标环境

#### 安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 安装Node.js依赖
```bash
cd frontend
npm install
```

### 2. 复制项目文件

#### 必须复制的文件/目录
```
prototype_system/
├── backend/
│   ├── app/
│   │   ├── df_model/model_weights.pth          # DF模型权重
│   │   ├── yatc_model/checkpoint-best.pth      # YaTC模型权重
│   │   ├── lexnet_model/best_model.pth         # LEXNet模型权重
│   │   ├── lexnet_model/data/scalers/          # LEXNet缩放器
│   │   ├── fa_net_model/                       # FA-Net模型文件
│   │   ├── fs_net_model/                       # FS-Net模型文件
│   │   ├── appscanner/                         # AppScanner模型文件
│   │   └── ...
│   └── requirements.txt
├── frontend/
│   ├── src/
│   ├── package.json
│   └── ...
└── docker-compose.yml
```

### 3. 配置文件检查

#### LEXNet配置文件
文件：`backend/app/lexnet_model/config.yml`

**已修复的配置**：
```yaml
data_preparation:
  pcap_root: './data/pcap_dataset/'              # 改为相对路径
  output_dir: './data/MyAttackDataset_balanced_refactored/'

data:
  pcap_dir: './data/pcap_dataset/'               # 改为相对路径
  output_dir_refactored: './data/MyAttackDataset_balanced_refactored/'
```

#### 其他配置文件
检查是否有其他硬编码路径：
- `backend/app/main.py` - API端点配置
- `docker-compose.yml` - 容器配置
- 各模型目录下的配置文件

### 4. 创建必要的目录结构

```bash
# 在项目根目录下创建数据目录
mkdir -p data/pcap_dataset
mkdir -p data/MyAttackDataset_balanced_refactored
mkdir -p shared_uploads
```

### 5. 环境变量配置（可选）

为了更好的可移植性，可以使用环境变量：

#### 创建 .env 文件
```bash
# 数据集根目录
LEXNET_DATA_ROOT=./data/pcap_dataset/
LEXNET_OUTPUT_DIR=./data/MyAttackDataset_balanced_refactored/

# 模型路径
DF_MODEL_PATH=./backend/app/df_model/model_weights.pth
YATC_MODEL_PATH=./backend/app/yatc_model/checkpoint-best.pth
```

### 6. 测试移植结果

#### 启动后端服务
```bash
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 启动前端服务
```bash
cd frontend
npm run dev
```

#### 验证功能
1. 访问前端界面
2. 测试各模型的单文件分析功能
3. 测试批量评估功能
4. 检查是否有路径相关错误

## ⚠️ 常见移植问题

### 1. 模型文件缺失
**错误**：`FileNotFoundError: model file not found`
**解决**：确保所有模型权重文件都已复制到正确位置

### 2. 数据目录不存在
**错误**：`No such file or directory: './data/pcap_dataset/'`
**解决**：创建必要的数据目录结构

### 3. 依赖包版本不兼容
**错误**：`ImportError` 或版本冲突
**解决**：使用虚拟环境，安装正确版本的依赖包

### 4. GPU/CUDA问题
**错误**：CUDA相关错误
**解决**：
- 检查目标机器是否有GPU
- 安装正确版本的CUDA驱动
- 或修改代码强制使用CPU

### 5. 端口冲突
**错误**：端口被占用
**解决**：修改端口配置或停止占用端口的服务

## 🔧 移植后的优化建议

### 1. 使用Docker部署
创建Docker镜像可以确保环境一致性：
```bash
docker-compose up -d
```

### 2. 配置文件模板化
将配置文件改为模板，使用环境变量替换：
```yaml
pcap_root: ${LEXNET_DATA_ROOT:-./data/pcap_dataset/}
```

### 3. 自动化部署脚本
创建部署脚本自动处理：
- 依赖安装
- 目录创建
- 配置文件生成
- 服务启动

## 📝 移植检查清单

- [ ] 复制所有项目文件
- [ ] 安装Python和Node.js依赖
- [ ] 检查模型权重文件是否存在
- [ ] 创建必要的数据目录
- [ ] 修改配置文件中的硬编码路径
- [ ] 测试后端API服务
- [ ] 测试前端界面
- [ ] 验证各模型功能正常
- [ ] 检查GPU/CUDA配置（如适用）
- [ ] 测试批量评估功能

## 总结

经过配置文件的修改，系统的可移植性已经大大提高。主要风险已经降低，但仍需要注意模型文件的完整性和依赖环境的配置。建议在移植前仔细检查上述清单，确保所有组件都能正常工作。
