# YaTC模型ZIP图像评估功能说明

## 功能概述

为YaTC模型添加了ZIP图像评估功能，允许用户直接上传包含分类图像的ZIP文件进行批量测试，跳过PCAP到图像的转换步骤。

## 前端界面

在YaTC模型页面中新增了第三个标签页"ZIP图像评估"，包含：

1. **文件上传区域**：
   - ZIP文件上传（包含分类图像目录）
   - 格式说明和要求

2. **评估结果展示**：
   - 准确率、精确率、召回率、F1分数
   - 混淆矩阵可视化

## 后端API

新增API端点：`POST /api/evaluate/yatc_images`

### 请求参数
- `zip_file`: ZIP文件，包含按类别组织的图像文件

### 响应格式
```json
{
  "accuracy": "0.9500",
  "precision": "0.9450", 
  "recall": "0.9400",
  "f1_score": "0.9425",
  "confusion_matrix": [[100, 2], [3, 95]],
  "class_names": ["勒索软件", "恶意文件", "扫描探测", "木马流量", "致瘫攻击", "良性", "隐蔽传输", "高危漏洞"],
  "summary": {
    "total_samples": 200,
    "correct_predictions": 190
  }
}
```

## ZIP文件格式要求

### 目录结构
```
test_images.zip
├── 勒索软件/
│   ├── image1.png
│   ├── image2.png
│   └── ...
├── 恶意文件/
│   ├── image1.png
│   ├── image2.png
│   └── ...
├── 扫描探测/
│   └── ...
└── 良性/
    └── ...
```

### 文件要求
- **图像格式**：支持PNG、JPG、JPEG格式
- **图像尺寸**：自动调整为40x40像素（与YaTC模型要求一致）
- **目录命名**：必须使用预定义的类别名称
- **图像预处理**：自动转换为灰度图像并标准化

## 支持的类别

YaTC模型支持以下8个类别：
1. 勒索软件
2. 恶意文件
3. 扫描探测
4. 木马流量
5. 致瘫攻击
6. 良性
7. 隐蔽传输
8. 高危漏洞

## 使用步骤

1. 访问YaTC模型页面
2. 切换到"ZIP图像评估"标签页
3. 准备ZIP文件，确保目录结构符合要求
4. 上传ZIP文件
5. 点击"开始评估"按钮
6. 等待评估完成，查看结果

## 技术实现

### 后端处理流程
1. **文件解压**：解压上传的ZIP文件到临时目录
2. **图像收集**：遍历目录结构，收集图像文件和对应标签
3. **图像预处理**：
   - 转换为灰度图像
   - 调整尺寸为40x40像素
   - 标准化处理（均值0.5，标准差0.5）
4. **批量推理**：使用YaTC模型进行批量预测
5. **结果计算**：计算各种评估指标

### 前端特性
- **文件验证**：确保上传的是ZIP文件
- **进度显示**：显示评估进度和状态
- **结果可视化**：清晰展示评估指标和混淆矩阵
- **错误处理**：完善的错误提示和异常处理

## 与现有功能的区别

| 功能 | 输入 | 处理方式 | 适用场景 |
|------|------|----------|----------|
| 单个文件分析 | PCAP文件 | PCAP→图像→分析 | 单个流量文件分析 |
| 批量文件评估 | 多个PCAP文件 | 批量PCAP→图像→分析 | 多个流量文件评估 |
| ZIP图像评估 | ZIP图像文件 | 直接图像→分析 | 已有图像数据集评估 |

## 优势

1. **跳过转换步骤**：直接使用图像数据，无需PCAP到图像的转换
2. **批量处理**：一次性处理大量图像文件
3. **标准化评估**：提供完整的分类评估指标
4. **灵活性**：支持用户自定义的图像数据集
5. **高效性**：GPU加速的批量推理
