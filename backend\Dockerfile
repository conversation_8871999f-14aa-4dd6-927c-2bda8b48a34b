FROM nvidia/cuda:11.6.2-cudnn8-runtime-ubuntu20.04

WORKDIR /app

# Switch to Aliyun APT mirror to avoid connection issues
RUN sed -i 's#archive.ubuntu.com#mirrors.aliyun.com#g' /etc/apt/sources.list && \
    sed -i 's#security.ubuntu.com#mirrors.aliyun.com#g' /etc/apt/sources.list

# 1. Install prerequisites and Wine
RUN apt-get update && apt-get install -y gnupg2 software-properties-common wget

# 2. Add WineHQ repository key and enable 32-bit architecture
RUN dpkg --add-architecture i386
RUN wget -O /tmp/winehq.key https://dl.winehq.org/wine-builds/winehq.key && \
    apt-key add /tmp/winehq.key && \
    apt-add-repository 'deb https://dl.winehq.org/wine-builds/ubuntu/ focal main'

# 3. Install Wine, Python, pip, and tshark
RUN apt-get update && \
    echo "wireshark-common wireshark-common/install-setuid boolean true" | debconf-set-selections && \
    apt-get install -y --install-recommends winehq-stable python3.8 python3-pip tshark && \
    rm -rf /var/lib/apt/lists/*

# Link python3 to python
RUN ln -s /usr/bin/python3 /usr/bin/python

RUN pip install --upgrade pip
ENV PIP_NO_CACHE_DIR=1

# --- 关键修正 2：安装 PyTorch 时，显式告知 pip 不要检查哈希 ---
# (虽然你的 requirements.txt 没有哈希，但这是为了防止 pip 意外进入哈希模式)
RUN pip install --timeout=600 torch==1.13.1+cu116 torchvision==0.14.1+cu116 --extra-index-url https://download.pytorch.org/whl/cu116

# 复制 requirements.txt
COPY ./requirements.txt /app/requirements.txt

# --- 关键修正 3：安装其他依赖时，也显式禁用哈希校验 ---
RUN pip install --timeout=600 -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com -r /app/requirements.txt

# 复制你的应用代码
COPY . /app

EXPOSE 8000

# 设置入口点以运行 Celery worker
CMD ["celery", "-A", "app.celery_worker:celery_app", "worker", "--loglevel=info"]