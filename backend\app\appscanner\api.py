from fastapi import APIRouter, UploadFile, File, HTTPException
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import numpy as np
import os
import io
import pickle
import joblib

router = APIRouter()

# --- 模型和设备配置 ---
# 注意: 模型路径是相对于 FastAPI 运行根目录的相对路径
MODEL_PATH = "app/appscanner/appscanner_model.pkl"


def load_appscanner_model():
    """加载用于评估的 AppScanner 模型"""
    if not os.path.exists(MODEL_PATH):
        raise HTTPException(status_code=500, detail=f"模型文件未找到: {MODEL_PATH}")
    
    try:
        # AppScanner 模型是 Scikit-learn 模型，使用 joblib 或 pickle 加载
        model = joblib.load(MODEL_PATH)
        return model
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型加载失败: {e}")


@router.post("/evaluate/appscanner_pkl")
async def evaluate_appscanner_from_pkl(pkl_file: UploadFile = File(...)):
    """
    从上传的 .pkl 缓存文件评估 AppScanner 模型。
    """
    try:
        contents = await pkl_file.read()
        buffer = io.BytesIO(contents)
        # 测试集文件是用 pickle 保存的
        X_test, y_test = pickle.load(buffer)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"无效或损坏的 .pkl 文件: {e}")

    model = load_appscanner_model()
    
    # --- 进行预测 ---
    y_pred = model.predict(X_test)

    # --- 计算评估指标 ---
    accuracy = accuracy_score(y_test, y_pred)
    
    # 获取所有唯一的类别标签
    class_names = sorted(list(np.unique(np.concatenate((y_test, y_pred)))))
    
    # 生成分类报告字符串
    report_str = classification_report(y_test, y_pred, target_names=class_names, digits=4)
    
    # 生成混淆矩阵
    cm = confusion_matrix(y_test, y_pred, labels=class_names)

    return {
        "accuracy": accuracy,
        "classification_report": report_str,
        "confusion_matrix": cm.tolist(),
        "class_names": class_names,
        "total_samples": len(y_test)
    } 