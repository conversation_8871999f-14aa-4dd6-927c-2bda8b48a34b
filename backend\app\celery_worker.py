from celery import Celery

def create_celery():
    # The 'broker' and 'backend' parameters are the connection URLs for Redis.
    # 'redis://redis:6379/0' points to the 'redis' service defined in docker-compose.yml.
    return Celery(
        'tasks',
        broker='redis://redis:6379/0',
        backend='redis://redis:6379/0',
        include=['app.tasks', 'app.unknown_traffic_model.tasks'] 
    )

celery_app = create_celery() 