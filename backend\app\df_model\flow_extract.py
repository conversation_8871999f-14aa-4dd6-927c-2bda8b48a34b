import os
from collections import Counter
from scapy.all import rdpcap, IP

# ------------------- 智能动态识别Observer IP的最终版本 -------------------

def extract_direction_sequence_for_df(pcap_file, target_length=5000):
    """
    从 pcap 文件中提取一个固定长度的数据包方向序列。
    此版本会自动识别文件中的主要通信IP作为观察者IP，无需手动指定。

    :param pcap_file: pcap文件的路径。
    :param target_length: 模型所需的目标序列长度。
    :return: 一个长度为 target_length 的列表，或一个空列表。
    """
    try:
        # 1. 使用 Scapy 读取 pcap 文件
        packets = rdpcap(pcap_file)
        
        # 过滤掉不含IP层的数据包
        ip_packets = [p for p in packets if IP in p]

        if not ip_packets:
            # 文件中没有IP流量
            return []

        # 2. 动态寻找 "主角IP" (Observer IP)
        # 统计每个IP地址出现的次数 (作为源或目的)
        ip_counter = Counter()
        for pkt in ip_packets:
            ip_counter[pkt[IP].src] += 1
            ip_counter[pkt[IP].dst] += 1
        
        if not ip_counter:
            return []
            
        # 流量最大的IP就是我们的动态Observer IP
        observer_ip = ip_counter.most_common(1)[0][0]

        # 3. 基于动态找到的 observer_ip 生成方向序列
        all_packets_info = []
        for pkt in ip_packets:
            timestamp = pkt.time
            direction = 0
            
            if pkt[IP].src == observer_ip:
                direction = 1  # 出站
            elif pkt[IP].dst == observer_ip:
                direction = -1 # 入站
            
            if direction != 0:
                all_packets_info.append((timestamp, direction))

        # Scapy读取的包已按时间排序，无需再次排序
        direction_sequence = [info[1] for info in all_packets_info]

        # 4. 格式化输出
        if len(direction_sequence) > target_length:
            final_sequence = direction_sequence[:target_length]
        else:
            padding = [0] * (target_length - len(direction_sequence))
            final_sequence = direction_sequence + padding
            
        return final_sequence

    except Exception as e:
        pcap_filename = os.path.basename(pcap_file)
        print(f"处理文件 {pcap_filename} 时出错: {e}")
        return [] 