import torch
import torch.nn as nn
import torch.nn.functional as F

class DFNet_Pytorch(nn.Module):
    def __init__(self, input_shape, num_classes):
        super(DFNet_Pytorch, self).__init__()
        
        # PyTorch的Conv1D输入格式是 (N, C_in, L_in)
        # N = batch_size, C_in = channels, L_in = length
        # 我们的输入是 (N, 5000, 1)，需要转换为 (N, 1, 5000)
        # 所以C_in=1
        
        # --- 模型参数 ---
        # Keras中的padding='same'在PyTorch中需要手动计算
        # padding = (kernel_size - 1) // 2 for stride=1
        
        # --- Block 1 ---
        self.block1_conv1 = nn.Conv1d(in_channels=1, out_channels=32, kernel_size=8, stride=1, padding='same')
        self.block1_bn1 = nn.BatchNorm1d(32)
        # ELU在PyTorch中是nn.ELU
        self.block1_conv2 = nn.Conv1d(in_channels=32, out_channels=32, kernel_size=8, stride=1, padding='same')
        self.block1_bn2 = nn.BatchNorm1d(32)
        self.block1_pool = nn.MaxPool1d(kernel_size=8, stride=4, padding=2) # Keras 'same' padding approximation
        self.block1_dropout = nn.Dropout(0.1)

        # --- Block 2 ---
        self.block2_conv1 = nn.Conv1d(in_channels=32, out_channels=64, kernel_size=8, stride=1, padding='same')
        self.block2_bn1 = nn.BatchNorm1d(64)
        self.block2_conv2 = nn.Conv1d(in_channels=64, out_channels=64, kernel_size=8, stride=1, padding='same')
        self.block2_bn2 = nn.BatchNorm1d(64)
        self.block2_pool = nn.MaxPool1d(kernel_size=8, stride=4, padding=2)
        self.block2_dropout = nn.Dropout(0.1)
        
        # --- Block 3 ---
        self.block3_conv1 = nn.Conv1d(in_channels=64, out_channels=128, kernel_size=8, stride=1, padding='same')
        self.block3_bn1 = nn.BatchNorm1d(128)
        self.block3_conv2 = nn.Conv1d(in_channels=128, out_channels=128, kernel_size=8, stride=1, padding='same')
        self.block3_bn2 = nn.BatchNorm1d(128)
        self.block3_pool = nn.MaxPool1d(kernel_size=8, stride=4, padding=2)
        self.block3_dropout = nn.Dropout(0.1)

        # --- Block 4 ---
        self.block4_conv1 = nn.Conv1d(in_channels=128, out_channels=256, kernel_size=8, stride=1, padding='same')
        self.block4_bn1 = nn.BatchNorm1d(256)
        self.block4_conv2 = nn.Conv1d(in_channels=256, out_channels=256, kernel_size=8, stride=1, padding='same')
        self.block4_bn2 = nn.BatchNorm1d(256)
        self.block4_pool = nn.MaxPool1d(kernel_size=8, stride=4, padding=2)
        self.block4_dropout = nn.Dropout(0.1)

        # --- 全连接层 (Classifier) ---
        # 我们用一个假的输入来动态计算Flatten之后的大小
        with torch.no_grad():
            dummy_input = torch.zeros(1, *input_shape)
            dummy_input = dummy_input.permute(0, 2, 1) # (N, L, C) -> (N, C, L)
            flattened_size = self._get_conv_output(dummy_input)

        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(flattened_size, 512)
        self.fc1_bn = nn.BatchNorm1d(512)
        self.fc1_dropout = nn.Dropout(0.7)
        
        self.fc2 = nn.Linear(512, 512)
        self.fc2_bn = nn.BatchNorm1d(512)
        self.fc2_dropout = nn.Dropout(0.5)
        
        self.fc3 = nn.Linear(512, num_classes)
        
    def _get_conv_output(self, x):
        x = self.block1_pool(F.elu(self.block1_bn2(self.block1_conv2(F.elu(self.block1_bn1(self.block1_conv1(x)))))))
        x = self.block2_pool(F.relu(self.block2_bn2(self.block2_conv2(F.relu(self.block2_bn1(self.block2_conv1(x)))))))
        x = self.block3_pool(F.relu(self.block3_bn2(self.block3_conv2(F.relu(self.block3_bn1(self.block3_conv1(x)))))))
        x = self.block4_pool(F.relu(self.block4_bn2(self.block4_conv2(F.relu(self.block4_bn1(self.block4_conv1(x)))))))
        return x.nelement() // x.shape[0]

    def forward(self, x):
        # PyTorch需要 (N, C, L) 格式
        x = x.permute(0, 2, 1)

        # Block 1
        x = self.block1_conv1(x)
        x = self.block1_bn1(x)
        x = F.elu(x)
        x = self.block1_conv2(x)
        x = self.block1_bn2(x)
        x = F.elu(x)
        x = self.block1_pool(x)
        x = self.block1_dropout(x)
        
        # Block 2
        x = self.block2_conv1(x)
        x = self.block2_bn1(x)
        x = F.relu(x)
        x = self.block2_conv2(x)
        x = self.block2_bn2(x)
        x = F.relu(x)
        x = self.block2_pool(x)
        x = self.block2_dropout(x)

        # Block 3
        x = self.block3_conv1(x)
        x = self.block3_bn1(x)
        x = F.relu(x)
        x = self.block3_conv2(x)
        x = self.block3_bn2(x)
        x = F.relu(x)
        x = self.block3_pool(x)
        x = self.block3_dropout(x)
        
        # Block 4
        x = self.block4_conv1(x)
        x = self.block4_bn1(x)
        x = F.relu(x)
        x = self.block4_conv2(x)
        x = self.block4_bn2(x)
        x = F.relu(x)
        x = self.block4_pool(x)
        x = self.block4_dropout(x)

        # Classifier
        x = self.flatten(x)
        
        x = self.fc1(x)
        x = self.fc1_bn(x)
        x = F.relu(x)
        x = self.fc1_dropout(x)
        
        x = self.fc2(x)
        x = self.fc2_bn(x)
        x = F.relu(x)
        x = self.fc2_dropout(x)
        
        x = self.fc3(x)
        return x 