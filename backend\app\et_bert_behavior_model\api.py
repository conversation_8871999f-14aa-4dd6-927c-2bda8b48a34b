from fastapi import APIRouter, UploadFile, File, HTTPException
import subprocess
import os
import uuid

import pandas as pd
from collections import Counter

et_bert_behavior_router = APIRouter()

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 定义上传文件和预测结果的保存路径
UPLOAD_FOLDER = os.path.join(current_dir, 'uploads')
PREDICTION_FOLDER = os.path.join(current_dir, 'predictions')
AAA_FOLDER = os.path.join(current_dir, '..', '..', '..', 'AAA')  # 指向AAA目录
LABEL_INFO_PATH = os.path.join(current_dir, 'label_info.txt')

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PREDICTION_FOLDER, exist_ok=True)

def get_protocol_map():
    """解析label_info.txt，返回一个索引到协议的映射。"""
    try:
        df = pd.read_csv(LABEL_INFO_PATH, sep='\t')
        # "name" 列包含 "协议_行为" 格式的字符串
        df['protocol'] = df['name'].apply(lambda x: x.split('_')[0])
        return df['protocol'].to_dict()
    except FileNotFoundError:
        # 在Web API上下文中，最好记录错误而不是打印
        # print(f"错误：无法找到标签信息文件 at {LABEL_INFO_PATH}")
        return {}
    except Exception as e:
        # print(f"解析标签文件时发生错误: {e}")
        return {}

@et_bert_behavior_router.post('/predict')
async def predict(file: UploadFile = File(...)):
    if not file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="Invalid file type, please upload a .tsv file")

    # 保存上传的文件
    filename = str(uuid.uuid4()) + '.tsv'
    test_path = os.path.join(UPLOAD_FOLDER, filename)
    with open(test_path, "wb") as buffer:
        buffer.write(await file.read())

    # 准备模型推理的参数
    load_model_path = os.path.join(current_dir, 'finetuned_model.bin')
    vocab_path = os.path.join(current_dir, 'ET-encryptd_vocab.txt')
    prediction_path = os.path.join(PREDICTION_FOLDER, 'prediction_' + filename)
    config_path = os.path.join(current_dir, 'bert_base_config.json') # 新增配置路径
    
    # 构建并执行推理命令
    command = [
        'python',
        os.path.join(current_dir, 'run_classifier_infer.py'),
        '--load_model_path', load_model_path,
        '--vocab_path', vocab_path,
        '--test_path', test_path,
        '--prediction_path', prediction_path,
        '--config_path', config_path,  # 新增配置参数
        '--labels_num', '51',
        '--embedding', 'word_pos_seg',
        '--encoder', 'transformer',
        '--mask', 'fully_visible'
    ]
    
    try:
        process = subprocess.run(command, check=True, capture_output=True, text=True)
        
        # 读取预测结果
        if not os.path.exists(prediction_path):
             raise HTTPException(status_code=500, detail=f"Prediction failed, result file not found. Stderr: {process.stderr}")

        with open(prediction_path, 'r', encoding='utf-8') as f:
            predictions_indices = []
            for line in f:
                try:
                    predictions_indices.append(int(line.strip()))
                except ValueError:
                    # 忽略无法转换为整数的行，比如文件的标题行
                    continue
        
        # 将索引转换为协议并统计
        protocol_map = get_protocol_map()
        if not protocol_map:
            raise HTTPException(status_code=500, detail="无法加载协议映射。")

        protocol_predictions = [protocol_map.get(idx, "未知") for idx in predictions_indices]
        protocol_counts = Counter(protocol_predictions)
        
        # 返回统计图表的数据
        return {'protocol_counts': dict(protocol_counts)}

    except subprocess.CalledProcessError as e:
        # 增加日志记录，以便于调试
        # logger.error(f"Prediction script failed: {e.stderr}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {e.stderr}")
    except FileNotFoundError:
        raise HTTPException(status_code=500, detail="Prediction result file not found") 