from uer.utils.tokenizers import Char<PERSON>okenizer
from uer.utils.tokenizers import SpaceTokenizer
from uer.utils.tokenizers import BertTokenizer
from uer.utils.data import *
from uer.utils.act_fun import *
from uer.utils.optimizers import *


str2tokenizer = {"char": CharTokenizer, "space": SpaceTokenizer, "bert": BertTokenizer}
str2dataset = {"bert": BertDataset, "lm": LmDataset, "mlm": MlmDataset,
               "bilm": BilmDataset, "albert": AlbertDataset, "seq2seq": Seq2seqDataset,
               "t5": T5Dataset, "cls": ClsDataset, "prefixlm": PrefixlmDataset}
str2dataloader = {"bert": BertDataLoader, "lm": LmData<PERSON>oader, "mlm": MlmDataLoader,
                  "bilm": <PERSON>ilmDataLoader, "albert": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, "seq2seq": Seq2seqD<PERSON><PERSON>oader,
                  "t5": T5<PERSON><PERSON><PERSON>oa<PERSON>, "cls": C<PERSON><PERSON><PERSON><PERSON>oa<PERSON>, "prefixlm": PrefixlmDataLoader}

str2act = {"gelu": gelu, "gelu_fast": gelu_fast, "relu": relu, "silu": silu, "linear": linear}

str2optimizer = {"adamw": AdamW, "adafactor": Adafactor}

str2scheduler = {"linear": get_linear_schedule_with_warmup, "cosine": get_cosine_schedule_with_warmup,
                "cosine_with_restarts": get_cosine_with_hard_restarts_schedule_with_warmup,
                "polynomial": get_polynomial_decay_schedule_with_warmup,
                "constant": get_constant_schedule, "constant_with_warmup": get_constant_schedule_with_warmup}

__all__ = ["CharTokenizer", "SpaceTokenizer", "BertTokenizer", "str2tokenizer",
           "BertDataset", "LmDataset", "MlmDataset", "BilmDataset",
           "AlbertDataset", "Seq2seqDataset", "T5Dataset", "ClsDataset",
           "PrefixlmDataset", "str2dataset",
           "BertDataLoader", "LmDataLoader", "MlmDataLoader", "BilmDataLoader",
           "AlbertDataLoader", "Seq2seqDataLoader", "T5DataLoader", "ClsDataLoader",
           "PrefixlmDataLoader", "str2dataloader",
           "gelu", "gelu_fast", "relu", "silu", "linear", "str2act",
           "AdamW", "Adafactor", "str2optimizer",
           "get_linear_schedule_with_warmup", "get_cosine_schedule_with_warmup",
           "get_cosine_with_hard_restarts_schedule_with_warmup",
           "get_polynomial_decay_schedule_with_warmup",
           "get_constant_schedule", "get_constant_schedule_with_warmup", "str2scheduler"]
