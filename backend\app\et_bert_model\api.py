from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Body
from starlette.responses import FileResponse
import uuid
import os
from pathlib import Path
import zipfile
import shutil
from pydantic import BaseModel
from typing import List, Dict # 导入Dict类型

# 从主应用导入Celery实例和我们新创建的任务
from ..celery_worker import celery_app
from ..tasks import finetune_et_bert_task, preprocess_et_bert_data_task # 导入新任务
# 导入推理函数
from .inference import run_inference
from celery import chain # 导入chain
import json


router = APIRouter()

# --- 持久化存储任务状态 ---
# 定义一个JSON文件来持久化存储我们的任务字典
TASKS_DB_FILE = Path(__file__).parent / "tasks_db.json"

def load_tasks_from_disk() -> Dict:
    """从JSON文件加载任务字典。如果文件不存在，返回空字典。"""
    if TASKS_DB_FILE.exists():
        with open(TASKS_DB_FILE, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return {} # 如果文件为空或损坏，则返回空字典
    return {}

def save_tasks_to_disk(tasks_dict: Dict):
    """将任务字典保存到JSON文件。"""
    with open(TASKS_DB_FILE, "w", encoding="utf-8") as f:
        json.dump(tasks_dict, f, indent=4)

# 在应用启动时加载任务状态
tasks = load_tasks_from_disk()


# 定义基础目录
BASE_ETBERT_DIR = Path(__file__).parent
FINETUNE_DATA_DIR = BASE_ETBERT_DIR / "finetune_data"
FINETUNE_DATA_DIR.mkdir(exist_ok=True)
PREPROCESS_DATA_DIR = BASE_ETBERT_DIR / "preprocess_data"
PREPROCESS_DATA_DIR.mkdir(exist_ok=True)


# --- 现有微调 API ---

@router.post("/finetune", summary="启动一个新的ET-BERT微调任务")
async def start_finetuning_task(
    task_name: str = Form(...),
    train_file: UploadFile = File(...),
    test_file: UploadFile = File(...),
):
    """
    上传训练和测试数据集，创建一个新的微调任务。
    - **task_name**: 为您的任务指定一个唯一的名称。
    - **train_file**: 训练用的.tsv文件。
    - **test_file**: 测试用的.tsv文件。
    """
    if not train_file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="训练文件必须是.tsv格式")
    if not test_file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="测试文件必须是.tsv格式")

    # 为本次任务的数据创建一个唯一的目录ID
    data_dir_id = str(uuid.uuid4())
    task_dir = FINETUNE_DATA_DIR / data_dir_id
    task_dir.mkdir()

    train_path = task_dir / "train_dataset.tsv"
    test_path = task_dir / "test_dataset.tsv"

    try:
        # 使用 shutil.copyfileobj 以流式传输文件，避免将大文件读入内存
        with open(train_path, "wb") as buffer:
            shutil.copyfileobj(train_file.file, buffer)
        with open(test_path, "wb") as buffer:
            shutil.copyfileobj(test_file.file, buffer)
    except Exception as e:
        # 在关闭文件前，主动关闭上传文件的文件对象
        train_file.file.close()
        test_file.file.close()
        raise HTTPException(status_code=500, detail=f"保存文件时出错: {e}")
    finally:
        # 确保文件对象被关闭
        train_file.file.close()
        test_file.file.close()


    # 启动Celery后台微调任务
    celery_task = finetune_et_bert_task.delay(task_dir_str=str(task_dir))
    task_id = celery_task.id

    # 使用Celery任务的ID作为我们跟踪此任务的唯一标识符
    tasks[task_id] = {
        "task_id": task_id,
        "task_name": task_name,
        "status": "PENDING", # Celery任务的初始状态
        "train_file": str(train_path),
        "test_file": str(test_path),
        "output_model_path": None,
        "details": "任务已提交到队列中，等待执行...",
        "accuracy": None
    }
    save_tasks_to_disk(tasks) # <--- 保存状态

    return {"task_id": task_id, "message": "微调任务已成功创建并排队。"}

@router.get("/finetune/{task_id}", summary="获取微调任务的状态")
async def get_task_status(task_id: str):
    """
    根据任务ID检查微调任务的当前状态和进度。
    """
    # 从内存中获取任务的静态元数据
    task_meta = tasks.get(task_id)
    if not task_meta:
        raise HTTPException(status_code=404, detail="找不到指定的任务ID")

    # 从Celery后端获取任务的实时状态
    celery_result = celery_app.AsyncResult(task_id)
    
    task_meta['status'] = celery_result.state

    if celery_result.state == 'PROGRESS':
        info = celery_result.info or {}
        task_meta['details'] = info.get('message', '正在处理...')
        task_meta['progress'] = info.get('progress', '0%')
    elif celery_result.state == 'SUCCESS':
        result = celery_result.get()
        if result.get('status') == 'completed':
            task_meta['status'] = 'COMPLETED'
            task_meta['details'] = f"微调成功完成！"
            task_meta['output_model_path'] = result.get('output_model_path')
            task_meta['accuracy'] = f"{result.get('accuracy', 0):.2%}"
        else: # 内部失败
            task_meta['status'] = 'FAILED'
            task_meta['details'] = f"任务执行失败: {result.get('error')}"
    elif celery_result.state == 'FAILURE':
        task_meta['status'] = 'FAILED'
        task_meta['details'] = f"任务执行遇到意外错误: {celery_result.info}"
    
    # 将更新后的状态存回内存字典并持久化
    tasks[task_id] = task_meta
    save_tasks_to_disk(tasks) # <--- 保存状态

    return task_meta

@router.get("/models", summary="获取所有已完成的微调模型")
async def get_finetuned_models():
    """
    返回一个列表，包含所有已成功完成微调的模型信息。
    """
    # 刷新所有任务的最新状态
    for task_id in tasks.keys():
        await get_task_status(task_id)

    completed_models = [
        task for task in tasks.values() 
        if task["status"] == "COMPLETED"
    ]
    return completed_models

class PredictRequest(BaseModel):
    task_id: str
    text: str

@router.post("/predict", summary="使用微调后的模型进行预测")
async def predict_with_finetuned_model(payload: PredictRequest):
    """
    使用一个已完成微调的模型，对新的文本进行分类预测。
    - **task_id**: 已完成的微调任务ID，用于指定使用哪个模型。
    - **text**: 需要预测的文本内容。
    """
    # 1. 检查任务是否存在且已完成
    task = tasks.get(payload.task_id)
    if not task:
        raise HTTPException(status_code=404, detail="找不到指定的微调任务ID。")
    if task.get("status") != "COMPLETED":
        raise HTTPException(status_code=400, detail="该任务尚未成功完成微调，无法用于预测。")

    model_path = task.get("output_model_path")
    if not model_path or not os.path.exists(model_path):
        raise HTTPException(status_code=500, detail="找不到模型文件，请确认微调过程是否正确生成了模型。")
    
    # 2. 获取模型的标签数量 (需要从训练数据中重新计算)
    # 在生产系统中，这个信息应该与模型一起保存
    train_file_path = task.get("train_file")
    try:
        # 导入用于计算label数量的函数
        # from .finetune import count_labels_num # 移除此行
        labels_num = 2 # 假设默认标签数量为2，如果需要更精确，则需要从原始数据中计算
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"无法从原始数据中确定标签数量: {e}")

    # 3. 调用推理函数
    result = run_inference(
        model_path=model_path,
        text_to_predict=payload.text,
        labels_num=labels_num
    )

    if result.get("status") == "failed":
        raise HTTPException(status_code=500, detail=f"模型推理时发生错误: {result.get('error')}")

    return {
        "prediction_index": result.get("prediction_index"),
        "confidence": result.get("confidence")
    } 


# --- 新的预处理及完整流程 API ---

@router.post("/preprocess-pcaps", summary="启动一个新的PCAP预处理任务")
async def start_pcap_preprocessing_task(
    file: UploadFile = File(..., description="一个包含pcap文件的.zip压缩包"),
    task_name: str = Form(f"PCAP预处理任务 - {str(uuid.uuid4())[:8]}", description="为任务指定名称")
):
    """
    接收一个包含pcap文件的zip包，启动一个独立的预处理任务。
    完成后，可以通过 /preprocess-pcaps/download/{task_id} 下载结果。
    """
    if not file.filename.endswith('.zip'):
        raise HTTPException(status_code=400, detail="请上传一个 .zip 格式的压缩文件。")

    task_dir_id = str(uuid.uuid4())
    task_dir = PREPROCESS_DATA_DIR / task_dir_id
    task_dir.mkdir()
    
    zip_path = task_dir / file.filename
    
    try:
        with open(zip_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(task_dir)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理上传的zip文件时出错: {e}")

    # 只启动独立的预处理任务
    celery_task = preprocess_et_bert_data_task.delay(str(task_dir))
    task_id = celery_task.id

    tasks[task_id] = {
        "task_id": task_id,
        "task_name": task_name,
        "status": "PENDING",
        "details": "预处理任务已提交到队列中。",
        "output_path": str(task_dir) # 保存输出目录以供下载
    }
    save_tasks_to_disk(tasks) # <--- 保存状态

    return {"task_id": task_id, "message": "预处理任务已成功创建。"}

@router.get("/preprocess-pcaps/status/{task_id}", summary="获取预处理任务的状态")
async def get_preprocess_task_status(task_id: str):
    """
    根据任务ID检查预处理任务的当前状态。
    """
    return await get_task_status(task_id)

@router.get("/preprocess-pcaps/download/{task_id}", summary="下载预处理后的TSV文件")
async def download_preprocessed_files(task_id: str):
    """
    在预处理任务成功完成后，下载包含 train_dataset.tsv 和 test_dataset.tsv 的zip压缩包。
    """
    task_meta = tasks.get(task_id)
    if not task_meta:
        raise HTTPException(status_code=404, detail="找不到指定的任务ID")
    
    if task_meta.get("status") != "COMPLETED":
        raise HTTPException(status_code=400, detail="任务尚未成功完成，无法下载文件。")

    output_dir_str = task_meta.get("output_path")
    if not output_dir_str:
        raise HTTPException(status_code=500, detail="任务元数据中缺少输出路径。")

    # 修正：文件实际上在 "dataset" 子目录中
    output_dir = Path(output_dir_str) / "dataset"
    train_file = output_dir / "train_dataset.tsv"
    test_file = output_dir / "test_dataset.tsv"

    if not train_file.exists() or not test_file.exists():
        raise HTTPException(status_code=404, detail="找不到生成的 .tsv 文件。请确认预处理任务是否成功生成了文件。")

    # 创建一个新的zip文件用于下载
    zip_filename = f"preprocessed_datasets_{task_id}.zip"
    zip_path = output_dir / zip_filename

    try:
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            zipf.write(train_file, arcname="train_dataset.tsv")
            zipf.write(test_file, arcname="test_dataset.tsv")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"压缩文件时出错: {e}")

    return FileResponse(
        path=zip_path, 
        filename=zip_filename,
        media_type='application/zip'
    ) 