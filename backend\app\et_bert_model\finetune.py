import argparse
import os
import sys
import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler
from pathlib import Path
import shutil
from pydantic import BaseModel
from typing import List # 导入List类型
import torch.nn.functional as F # 导入F模块

# 动态地将uer库和其父目录添加到Python路径中
# 这使得我们可以直接从 backend/app/et_bert_model 目录运行此脚本
# 并正确解析 'from uer.layers import *' 这样的导入
_CURRENT_DIR = Path(__file__).parent.resolve()
_UER_PATH = _CURRENT_DIR / "uer"
if str(_CURRENT_DIR) not in sys.path:
    sys.path.append(str(_CURRENT_DIR))
if str(_UER_PATH) not in sys.path:
    sys.path.append(str(_UER_PATH))


from uer.layers import *
from uer.encoders import *
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils import *
from uer.utils.optimizers import *
from uer.utils.config import load_hyperparam
from uer.utils.seed import set_seed
from uer.model_saver import save_model
from uer.opts import finetune_opts

# 导入原始脚本中的分类器定义 (假设它也在同一目录下或路径中)
# 我们需要把Classifier类和相关函数从run_classifier.py复制过来
# --- (以下代码大部分从 run_classifier.py 移植并适配) ---

class Classifier(nn.Module):
    def __init__(self, args, class_weights=None):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.tokenizer.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.soft_targets = args.soft_targets
        self.soft_alpha = args.soft_alpha
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, args.labels_num)
        # 保存类别权重
        self.class_weights = class_weights
        # 使用GELU激活函数和Dropout来增强分类头
        self.gelu = nn.GELU()
        self.dropout = nn.Dropout(0.1)
        # 为编码器输出增加一个额外的Dropout层，以加强正则化
        self.encoder_dropout = nn.Dropout(0.1)

    def forward(self, src, tgt, seg, soft_tgt=None):
        emb = self.embedding(src, seg)
        # 在编码器输出后增加Dropout
        output = self.encoder_dropout(self.encoder(emb, seg))

        if self.pooling == "mean":
            output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            output = output[:, -1, :]
        else:
            output = output[:, 0, :]

        # 增强的分类头
        output = self.dropout(self.gelu(self.output_layer_1(output)))
        logits = self.output_layer_2(output)

        if tgt is not None:
            if self.soft_targets and soft_tgt is not None:
                loss = self.soft_alpha * nn.MSELoss()(logits, soft_tgt) + \
                       (1 - self.soft_alpha) * nn.NLLLoss(weight=self.class_weights)(nn.LogSoftmax(dim=-1)(logits), tgt.view(-1))
            else:
                # 应用类别权重和标签平滑
                loss = LabelSmoothingCrossEntropy(weight=self.class_weights)(logits, tgt.view(-1))
            return loss, logits
        else:
            return None, logits


# 标签平滑损失函数
class LabelSmoothingCrossEntropy(nn.Module):
    def __init__(self, smoothing=0.1, weight=None):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.smoothing = smoothing
        self.weight = weight

    def forward(self, x, target):
        confidence = 1. - self.smoothing
        logprobs = F.log_softmax(x, dim=-1)
        nll_loss = -logprobs.gather(dim=-1, index=target.unsqueeze(1))
        nll_loss = nll_loss.squeeze(1)
        smooth_loss = -logprobs.mean(dim=-1)
        loss = confidence * nll_loss + self.smoothing * smooth_loss
        if self.weight is not None:
            # 应用类别权重
            loss = self.weight[target] * loss
        return loss.mean()


def get_labels_and_counts(path):
    """
    从数据文件中获取所有标签及其出现次数。
    返回一个包含(label, count)元组的字典。
    """
    labels, columns = {}, {}
    with open(path, mode="r", encoding="utf-8") as f:
        for line_id, line in enumerate(f):
            if line_id == 0:
                for i, column_name in enumerate(line.strip().split("\t")):
                    columns[column_name] = i
                continue
            line = line.strip().split("\t")
            label = int(line[columns["label"]])
            labels[label] = labels.get(label, 0) + 1
    return labels

def load_or_initialize_parameters(args, model):
    if args.pretrained_model_path is not None:
        try:
            print(f"Loading pretrained model from {args.pretrained_model_path}")
            pretrained_dict = torch.load(args.pretrained_model_path, map_location=args.device)
            model_dict = model.state_dict()

            # 手动过滤和对齐权重：只加载预训练模型和当前模型共有的、且不属于预训练任务的权重
            pretrained_dict = {
                k: v for k, v in pretrained_dict.items() 
                if k in model_dict and "target" not in k
            }
            
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            
            # --- 详细的诊断日志 ---
            print("\n--- Model Loading Diagnostics ---")
            loaded_keys = pretrained_dict.keys()
            # 找出所有应该被加载但实际未加载的骨干网络权重
            missing_backbone_keys = [
                k for k in model.state_dict().keys() 
                if k not in loaded_keys and "output_layer" not in k
            ]
            
            print(f"Total keys in current model: {len(model.state_dict())}")
            print(f"Keys successfully loaded from pretrained model: {len(loaded_keys)}")

            if missing_backbone_keys:
                print("\nCRITICAL WARNING: The following backbone keys were NOT loaded:")
                for k in missing_backbone_keys:
                    print(f"  - {k}")
            else:
                print("\nBackbone successfully loaded.")

            print("\nRe-initializing classifier head...")
            model.output_layer_1.weight.data.normal_(mean=0.0, std=0.02)
            if model.output_layer_1.bias is not None:
                model.output_layer_1.bias.data.zero_()
            model.output_layer_2.weight.data.normal_(mean=0.0, std=0.02)
            if model.output_layer_2.bias is not None:
                model.output_layer_2.bias.data.zero_()
            print("Classifier head re-initialized.")
            print("---------------------------------\n")

        except Exception as e:
            print(f"An error occurred during model loading: {e}")
            print("Initializing entire model randomly.")
            for n, p in list(model.named_parameters()):
                if "gamma" not in n and "beta" not in n:
                    p.data.normal_(0, 0.02)
    else:
        print("No pretrained model path specified. Initializing model with random weights.")
        for n, p in list(model.named_parameters()):
            if "gamma" not in n and "beta" not in n:
                p.data.normal_(0, 0.02)


def build_optimizer(args, model):
    no_decay = ['bias', 'gamma', 'beta']
    
    encoder_params = list(model.embedding.named_parameters()) + list(model.encoder.named_parameters())
    classifier_params = list(model.output_layer_1.named_parameters()) + list(model.output_layer_2.named_parameters())
    
    print("\n--- Applying Differential Learning Rates ---")
    print(f"  > Encoder learning rate: {args.learning_rate}")
    print(f"  > Classifier head learning rate: {1e-4}")
    print("------------------------------------------\n")

    optimizer_grouped_parameters = [
        # Encoder parameters with decay
        {"params": [p for n, p in encoder_params if not any(nd in n for nd in no_decay)], "weight_decay_rate": 0.01, "lr": args.learning_rate},
        # Encoder parameters without decay
        {"params": [p for n, p in encoder_params if any(nd in n for nd in no_decay)], "weight_decay_rate": 0.0, "lr": args.learning_rate},
        # Classifier parameters with decay
        {"params": [p for n, p in classifier_params if not any(nd in n for nd in no_decay)], "weight_decay_rate": 0.01, "lr": 1e-4},
        # Classifier parameters without decay
        {"params": [p for n, p in classifier_params if any(nd in n for nd in no_decay)], "weight_decay_rate": 0.0, "lr": 1e-4},
    ]

    # AdamW is the default in the config, so this is safe.
    optimizer = str2optimizer[args.optimizer](optimizer_grouped_parameters, correct_bias=False)

    if args.scheduler in ["constant"]:
        scheduler = str2scheduler[args.scheduler](optimizer)
    elif args.scheduler in ["constant_with_warmup"]:
        scheduler = str2scheduler[args.scheduler](optimizer, args.train_steps * args.warmup)
    else:
        scheduler = str2scheduler[args.scheduler](optimizer, args.train_steps * args.warmup, args.train_steps)
    
    return optimizer, scheduler


def batch_loader(batch_size, src, tgt, seg, soft_tgt=None):
    instances_num = src.size()[0]
    for i in range(instances_num // batch_size):
        src_batch = src[i * batch_size : (i + 1) * batch_size, :]
        tgt_batch = tgt[i * batch_size : (i + 1) * batch_size]
        seg_batch = seg[i * batch_size : (i + 1) * batch_size, :]
        if soft_tgt is not None:
            soft_tgt_batch = soft_tgt[i * batch_size : (i + 1) * batch_size, :]
            yield src_batch, tgt_batch, seg_batch, soft_tgt_batch
        else:
            yield src_batch, tgt_batch, seg_batch, None
    if instances_num > instances_num // batch_size * batch_size:
        src_batch = src[instances_num // batch_size * batch_size :, :]
        tgt_batch = tgt[instances_num // batch_size * batch_size :]
        seg_batch = seg[instances_num // batch_size * batch_size :, :]
        if soft_tgt is not None:
            soft_tgt_batch = soft_tgt[instances_num // batch_size * batch_size :, :]
            yield src_batch, tgt_batch, seg_batch, soft_tgt_batch
        else:
            yield src_batch, tgt_batch, seg_batch, None


def read_dataset(args, path):
    dataset, columns = [], {}
    with open(path, mode="r", encoding="utf-8") as f:
        for line_id, line in enumerate(f):
            if line_id == 0:
                for i, column_name in enumerate(line.strip().split("\t")):
                    columns[column_name] = i
                continue
            line = line.strip().split("\t")
            original_tgt = int(line[columns["label"]])
            
            # If the label was filtered out, skip this sample.
            if original_tgt not in args.label_map:
                continue

            tgt = args.label_map[original_tgt]

            if "text_b" not in columns:
                text_a = line[columns["text_a"]]
                src = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a))
                seg = [1] * len(src)
            else:
                text_a, text_b = line[columns["text_a"]], line[columns["text_b"]]
                src_a = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a) + [SEP_TOKEN])
                src_b = args.tokenizer.convert_tokens_to_ids(args.tokenizer.tokenize(text_b) + [SEP_TOKEN])
                src = src_a + src_b
                seg = [1] * len(src_a) + [2] * len(src_b)
            
            if len(src) > args.seq_length:
                src = src[:args.seq_length]
                seg = seg[:args.seq_length]
            while len(src) < args.seq_length:
                src.append(PAD_ID)
                seg.append(PAD_ID)
            dataset.append((src, tgt, seg))
    return dataset


def train_model(args, model, optimizer, scheduler, scaler, src_batch, tgt_batch, seg_batch):
    model.zero_grad()
    src_batch = src_batch.to(args.device)
    tgt_batch = tgt_batch.to(args.device)
    seg_batch = seg_batch.to(args.device)

    with autocast():
        loss, _ = model(src_batch, tgt_batch, seg_batch)

    if torch.cuda.device_count() > 1:
        loss = torch.mean(loss)
    
    # Scales loss. Calls backward() on scaled loss to create scaled gradients.
    scaler.scale(loss).backward()
    # scaler.step() first unscales the gradients of the optimizer's assigned params.
    scaler.step(optimizer)
    # Updates the scale for next iteration.
    scaler.update()

    scheduler.step()
    return loss


def evaluate(args, model, dataset):
    src = torch.LongTensor([sample[0] for sample in dataset])
    tgt = torch.LongTensor([sample[1] for sample in dataset])
    seg = torch.LongTensor([sample[2] for sample in dataset])
    batch_size = args.batch_size
    correct = 0
    model.eval()

    all_preds = []
    all_golds = []

    for src_batch, tgt_batch, seg_batch, _ in batch_loader(batch_size, src, tgt, seg):
        src_batch = src_batch.to(args.device)
        tgt_batch = tgt_batch.to(args.device)
        seg_batch = seg_batch.to(args.device)
        with torch.no_grad():
            with autocast():
                _, logits = model(src_batch, None, seg_batch)
        pred = torch.argmax(nn.Softmax(dim=1)(logits), dim=1)
        gold = tgt_batch
        
        all_preds.extend(pred.cpu().numpy())
        all_golds.extend(gold.cpu().numpy())

        correct += torch.sum(pred == gold).item()
    
    # Handle the case where the dataset is empty after filtering
    if len(dataset) == 0:
        print("Warning: Evaluation dataset is empty. Returning 0 accuracy.")
        return 0.0
    
    accuracy = correct / len(dataset)

    # --- Start of Diagnostic ---
    print("\n--- Evaluation Diagnostics ---")
    try:
        from collections import Counter
        gold_counts = Counter(all_golds)
        pred_counts = Counter(all_preds)
        
        print(f"Total samples: {len(dataset)}")
        print(f"Correct predictions: {correct}")
        print(f"Accuracy: {accuracy:.4f}")
        
        print("\nGround Truth Label Distribution:")
        for label, count in sorted(gold_counts.items()):
            print(f"  Label {label}: {count} samples ({(count/len(dataset))*100:.2f}%)")
            
        print("\nPredicted Label Distribution:")
        if not pred_counts:
            print("  No predictions were made.")
        else:
            for label, count in sorted(pred_counts.items()):
                print(f"  Label {label}: {count} predictions ({(count/len(dataset))*100:.2f}%)")

    except ImportError:
        print("Could not import Counter for detailed diagnostics.")
    print("----------------------------\n")
    # --- End of Diagnostic ---

    return accuracy


def run_finetuning(task_id, task_dir, update_status_callback=None):
    """
    一个封装了微调逻辑的函数，可以被Celery任务调用。
    
    Args:
        task_id (str): 任务的唯一ID。
        task_dir (Path): 包含 train_dataset.tsv 和 test_dataset.tsv 的目录。
        update_status_callback (function): 用于更新任务状态和进度的回调函数。
    """
    try:
        # 使用argparse来构建一个默认参数对象，然后手动覆盖关键路径
        parser = argparse.ArgumentParser()
        finetune_opts(parser)
        # 提供一个假的参数列表，避免解析sys.argv
        args = parser.parse_args([])

        # --- 手动设置关键参数 ---
        args.config_path = str(_CURRENT_DIR / "bert_base_config.json")
        args.pretrained_model_path = str(_CURRENT_DIR / "pre-trained_model.bin")
        args.vocab_path = str(_CURRENT_DIR / "encryptd_vocab.txt")
        args.train_path = str(task_dir / "train_dataset.tsv")
        args.dev_path = str(task_dir / "test_dataset.tsv") # 使用测试集作为验证集
        args.test_path = str(task_dir / "test_dataset.tsv")

        # 定义输出目录和文件
        output_dir = _CURRENT_DIR / "finetuned_models" / task_id
        output_dir.mkdir(parents=True, exist_ok=True)
        args.output_model_path = str(output_dir / "finetuned_model.bin")

        # --- 从原始脚本移植过来的剩余设置 ---
        args = load_hyperparam(args)
        set_seed(args.seed)

        # --- 批次大小调整 ---
        # 对于小数据集，较大的批次大小（如64）可能导致模型陷入局部最优。
        # 我们将其减小以增加梯度更新的随机性，有助于模型更好地探索和学习。
        print("\n--- Adjusting Batch Size for Fine-tuning ---")
        original_batch_size = args.batch_size
        args.batch_size = 16
        print(f"Overriding batch_size: {original_batch_size} -> {args.batch_size}")
        print("------------------------------------------\n")
        
        # --- 类别权重计算 ---
        print("\n--- Calculating Class Weights ---")
        label_counts = get_labels_and_counts(args.train_path)
        
        # (可选) 过滤掉稀有类别
        MIN_SAMPLES_THRESHOLD = 10
        labels_to_keep = {l for l, c in label_counts.items() if c >= MIN_SAMPLES_THRESHOLD}
        
        if len(labels_to_keep) < len(label_counts):
            print(f"Filtering out classes with fewer than {MIN_SAMPLES_THRESHOLD} samples.")
            print(f"Keeping {len(labels_to_keep)} out of {len(label_counts)} original classes.")
        
        # 为保留的标签创建映射表
        sorted_labels_to_keep = sorted(list(labels_to_keep))
        args.label_map = {label: i for i, label in enumerate(sorted_labels_to_keep)}
        args.labels_num = len(sorted_labels_to_keep)
        
        # 计算权重
        total_samples_kept = sum(label_counts[l] for l in sorted_labels_to_keep)
        weights = []
        for label in sorted_labels_to_keep:
            # 标准的逆频率加权
            weight = total_samples_kept / (args.labels_num * label_counts[label])
            weights.append(weight)
        
        class_weights = torch.FloatTensor(weights).to(torch.device("cuda" if torch.cuda.is_available() else "cpu"))
        
        print("\nFinal label mapping and applied class weights:")
        for label, mapped_label in args.label_map.items():
            print(f"  Orig Label {label} -> Mapped Label {mapped_label} (Weight: {weights[mapped_label]:.2f})")
        print("---------------------------------\n")
        # --- 权重计算结束 ---

        args.tokenizer = str2tokenizer[args.tokenizer](args)
        
        # 构建模型，并传入类别权重
        model = Classifier(args, class_weights=class_weights)
        args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(args.device)
        
        # 加载参数并重置分类头
        load_or_initialize_parameters(args, model)
        
        # 读取数据集 (现在会根据 label_map 过滤)
        train_set = read_dataset(args, args.train_path)
        dev_set = read_dataset(args, args.dev_path)

        # 如果过滤后训练集为空，则提前退出
        if not train_set:
            raise RuntimeError("Training set is empty after filtering. Check your data and MIN_SAMPLES_THRESHOLD.")

        src = torch.LongTensor([sample[0] for sample in train_set])
        tgt = torch.LongTensor([sample[1] for sample in train_set])
        seg = torch.LongTensor([sample[2] for sample in train_set])
        
        args.train_steps = int(len(train_set) * args.epochs_num / args.batch_size) + 1
        
        optimizer, scheduler = build_optimizer(args, model)
        scaler = GradScaler()
        print("\n--- Automatic Mixed Precision (AMP) Enabled ---")
        
        total_loss, result, best_result = 0.0, 0.0, 0.0
        
        print("\n--- Starting Full Training ---")
        for epoch in range(1, args.epochs_num + 1):
            model.train()
            for i, (src_batch, tgt_batch, seg_batch, _) in enumerate(batch_loader(args.batch_size, src, tgt, seg)):
                loss = train_model(args, model, optimizer, scheduler, scaler, src_batch, tgt_batch, seg_batch)
                total_loss += loss.item()
                if (i + 1) % args.report_steps == 0:
                    print(f"Epoch {epoch}, Step {i + 1} | Loss: {total_loss / args.report_steps:.4f}")
                    if update_status_callback:
                        progress = int(((epoch - 1) * len(train_set) + (i + 1) * args.batch_size) / (args.epochs_num * len(train_set)) * 100)
                        # 修复TypeError：将状态信息打包到一个字典中作为第二个参数传递
                        update_details = {"status": "RUNNING", "message": f"Epoch {epoch}, Step {i+1}"}
                        update_status_callback(progress, update_details)
                    total_loss = 0.0
            
            result = evaluate(args, model, dev_set)
            if result > best_result:
                best_result = result
                save_model(model, args.output_model_path)
        
        # 训练结束
        if update_status_callback:
            final_details = {"status": "COMPLETED", "message": f"Best accuracy: {best_result:.2%}"}
            update_status_callback(100, final_details)
            
        return {
            "status": "completed",
            "output_model_path": args.output_model_path,
            "accuracy": best_result 
        }

    except Exception as e:
        import traceback
        error_details = {"status": "FAILED", "message": str(e)}
        if update_status_callback:
            update_status_callback(100, error_details)
        return {
            "status": "failed",
            "error": str(e),
            "traceback": traceback.format_exc()
        } 