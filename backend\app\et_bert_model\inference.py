import argparse
import os
import sys
import torch
import torch.nn as nn
from pathlib import Path

# --- 路径设置 (与 finetune.py 类似) ---
_CURRENT_DIR = Path(__file__).parent.resolve()
_UER_PATH = _CURRENT_DIR / "uer"
if str(_CURRENT_DIR) not in sys.path:
    sys.path.append(str(_CURRENT_DIR))
if str(_UER_PATH) not in sys.path:
    sys.path.append(str(_UER_PATH))

# 从微调模块导入分类器定义
from .finetune import Classifier
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils import *
from uer.utils.tokenizers import BertTokenizer
from uer.utils.config import load_hyperparam
from uer.model_loader import load_model
from uer.opts import infer_opts
from types import SimpleNamespace

def run_inference(model_path: str, text_to_predict: str, labels_num: int):
    """
    使用指定的微调模型对单段文本进行预测。

    Args:
        model_path (str): 微调后的模型.bin文件路径。
        text_to_predict (str): 需要分类的文本。
        labels_num (int): 模型的分类标签数量。

    Returns:
        dict: 包含预测类别和置信度的字典。
    """
    try:
        # 替代 argparse：手动创建一个配置对象
        args = SimpleNamespace()

        # 手动设置所有必要的参数
        args.load_model_path = model_path
        args.vocab_path = str(_CURRENT_DIR / "encryptd_vocab.txt")
        args.config_path = str(_CURRENT_DIR / "bert_base_config.json") # 假设所有微调都基于base config
        args.labels_num = labels_num
        args.pooling = "first" # 与训练时保持一致
        # args.tokenizer = "sp" # 移除无效的 'sp'
        args.embedding = "word"
        args.encoder = "transformer"
        args.mask = "fully_visible"
        args.layernorm_positioning = "post"
        args.feed_forward = "dense"
        args.layernorm = "normal"
        args.spm_model_path = None # 添加缺失的属性
        args.remove_embedding_layernorm = False
        args.remove_attention_scale = False
        args.remove_transformer_bias = False
        args.bidirectional = False
        args.factorized_embedding_parameterization = False
        args.parameter_sharing = False
        args.relative_position_embedding = False
        args.relative_attention_buckets_num = 32
        args.seq_length = 1024 # 根据模型训练时的设定

        # 加载与模型匹配的配置
        args = load_hyperparam(args)
        # 直接使用 BertTokenizer
        args.tokenizer = BertTokenizer(args)
        
        # 构建模型并加载微调后的权重
        args.soft_targets, args.soft_alpha = False, False
        model = Classifier(args)
        model = load_model(model, args.load_model_path)
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
        model.eval()

        # 准备输入数据
        src = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_to_predict))
        seg = [1] * len(src)
        
        if len(src) > args.seq_length:
            src = src[:args.seq_length]
            seg = seg[:args.seq_length]
        while len(src) < args.seq_length:
            src.append(PAD_ID)
            seg.append(PAD_ID)
        
        src_tensor = torch.LongTensor([src]).to(device)
        seg_tensor = torch.LongTensor([seg]).to(device)

        # 执行预测
        with torch.no_grad():
            _, logits = model(src_tensor, None, seg_tensor)
        
        probabilities = nn.Softmax(dim=1)(logits)
        confidence, predicted_class_idx = torch.max(probabilities, dim=1)
        
        return {
            "status": "success",
            "prediction_index": predicted_class_idx.item(),
            "confidence": confidence.item()
        }

    except Exception as e:
        import traceback
        return {
            "status": "failed",
            "error": str(e),
            "traceback": traceback.format_exc()
        } 