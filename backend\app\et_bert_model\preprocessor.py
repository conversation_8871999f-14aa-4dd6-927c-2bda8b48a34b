import os
import shutil
import subprocess
import csv
import random
import zipfile
from pathlib import Path
from typing import Optional, List, Dict
from sklearn.model_selection import StratifiedShuffleSplit, train_test_split
from tqdm import tqdm
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import tempfile
import uuid
import multiprocessing

# --- Helper Functions ---

def format_payload_to_bigram(payload_hex: str) -> str:
    """
    Converts a raw hex string into space-separated bytes (bigrams).
    Example: "aabbccdd" -> "aa bb cc dd"
    This is the format expected by the tokenizer.
    """
    payload_hex = payload_hex.replace(":", "")
    if len(payload_hex) % 2 != 0:
        payload_hex = payload_hex[:-1]
    
    # Return space-separated bytes
    return ' '.join(payload_hex[i:i+2] for i in range(0, len(payload_hex), 2))


# --- Core Extraction Logic ---

def _extract_features_from_pcap_worker(task_data: tuple) -> list:
    """
    Worker function to process a single pcap file.
    This function is designed to be called by a process pool.
    It extracts features from the first 5 packets of a session based on payload content.
    """
    pcap_file, label_id = task_data
    # print(f"[Worker] Starting processing on: {pcap_file.name}")
    
    # Use a unique temporary file for tshark output to avoid conflicts
    temp_output_path = Path(tempfile.gettempdir()) / f"tshark_{uuid.uuid4()}.tmp"

    try:
        # A more robust tshark command.
        # -n: disable network name resolution
        # We grab all fields we could possibly need.
        cmd = [
            "tshark", "-r", str(pcap_file), "-n",
            "-T", "fields",
            "-e", "frame.number",
            "-e", "tcp.stream",
            "-e", "udp.stream",
            "-e", "ip.src",
            "-e", "ip.dst",
            "-e", "ip.len",
            # Replace protocol-specific payloads with the generic 'data.data' field
            # This is more robust across different tshark versions.
            "-e", "data.data",
            "-E", "separator=\t", # Use tab as separator
            "-E", "quote=d",     # Double quote all fields
            "-E", "occurrence=a" # Aggregate repeated fields
        ]
        
        # Using a file to avoid stdout buffer limitations.
        # We now capture stderr to diagnose tshark issues.
        with open(temp_output_path, "w", encoding='utf-8') as f_out:
            result = subprocess.run(cmd, stdout=f_out, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore', timeout=120)

        # Process the structured output
        sessions: Dict[str, list] = {}
        lines_read = 0
        with open(temp_output_path, "r", encoding='utf-8', errors='ignore') as f_in:
            lines = f_in.readlines()
            lines_read = len(lines)

            # NEW: Check for tshark errors if output is empty
            if lines_read == 0 and result.stderr:
                print(f"!!! TSHARK WARNING for {pcap_file.name}: tshark produced no output. Stderr below:")
                print("---------------- TSHARK STDERR START ----------------")
                print(result.stderr.strip())
                print("----------------- TSHARK STDERR END -----------------")

            for line in lines:
                parts = line.strip().split('\t')
                if len(parts) < 6:
                    continue
                
                # Unquote fields
                try:
                    # Adjust parsing for the new command structure (data.data is the 7th field)
                    frame_num_str, stream_tcp, stream_udp, ip_src, ip_dst, ip_len_str = [p.strip('"') for p in parts[:6]]
                    payload_hex = parts[6].strip('"') if len(parts) > 6 else ""
                except IndexError:
                    continue

                stream_idx = stream_tcp or stream_udp
                if not stream_idx or not ip_src:
                    continue # Not part of a TCP/UDP session or no source IP

                if stream_idx not in sessions:
                    sessions[stream_idx] = []
                
                try:
                    ip_len = int(ip_len_str)
                    frame_num = int(frame_num_str)
                except (ValueError, TypeError):
                    continue # Skip if length/frame is not a valid integer

                # payload_hex is now directly from parts[6]
                
                # Store all relevant info
                sessions[stream_idx].append({
                    'frame': frame_num,
                    'src': ip_src,
                    'len': ip_len,
                    'payload': payload_hex.replace(":", "") # Clean payload immediately
                })

        # print(f"[Worker] Read {lines_read} lines from tshark output for {pcap_file.name}.")
        # print(f"[Worker] Found {len(sessions)} sessions in {pcap_file.name}.")

        # Generate feature strings from sessions based on the new logic
        final_features = []
        for stream_idx, packets in sessions.items():
            if not packets:
                continue

            # 1. Sort packets by frame number to ensure chronological order.
            packets.sort(key=lambda p: p['frame'])

            # 2. Select the first 5 packets.
            packets_to_process = packets[:5]

            all_bigrams = []
            # 3. Extract payload and generate bigrams for each of the selected packets.
            for pkt in packets_to_process:
                # Ensure payload exists and is not empty
                if pkt.get('payload'):
                    content_bigrams_str = format_payload_to_bigram(pkt['payload'])
                    if content_bigrams_str:
                        all_bigrams.extend(content_bigrams_str.split())
            
            # 4. Concatenate all bigrams into a single feature string for the stream.
            if all_bigrams:
                feature_string = " ".join(all_bigrams)
                final_features.append((label_id, feature_string))

        # print(f"[Worker] Successfully generated {len(final_features)} feature strings for {pcap_file.name}.")
        return final_features
    
    except subprocess.TimeoutExpired:
        print(f"!!! Worker for {pcap_file.name} timed out.")
        return []
    except Exception as e:
        # More detailed error logging for debugging
        import traceback
        print(f"!!! An unexpected exception occurred in worker for {pcap_file.name}: {e}")
        traceback.print_exc()
        return []
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)


# --- Main Orchestration Logic ---

def run_preprocessing_logic(task_dir_str: str, progress_callback=None):
    """
    Main preprocessing function.
    Finds pcap files, extracts features in parallel, and creates datasets.
    """
    task_dir = Path(task_dir_str)
    
    def report(percent, msg):
        if progress_callback:
            progress_callback(min(99, int(percent)), msg)

    # 1. Discover pcap files and their labels
    report(5, "Searching for pcap files...")
    
    unzipped_root = None
    # Find the main data directory, avoid MacOS specific folders
    for item in task_dir.iterdir():
        if item.is_dir() and item.name != '__MACOSX':
             if any(item.glob("**/*.pcap")) or any(d for d in item.iterdir() if d.is_dir()):
                unzipped_root = item
                break
    if not unzipped_root:
        unzipped_root = task_dir

    label_dirs = [d for d in unzipped_root.iterdir() if d.is_dir()]
    if not label_dirs:
        # Handle case where pcaps are in the root of the unzipped folder
        if list(unzipped_root.glob("*.pcap")):
             label_dirs = [unzipped_root]
             label_map = {unzipped_root.name: 0}
        else:
            report(100, "Error: No labeled subdirectories or .pcap files found.")
            return {"status": "failed", "error": "No data found."}
    else:
        label_map = {d.name: i for i, d in enumerate(label_dirs)}

    pcap_tasks = []
    for d in label_dirs:
        # Use a label of 0 if we are in the root pcap case
        label_id = label_map.get(d.name, 0)
        pcap_list = list(d.glob("*.pcap")) + list(d.glob("*.pcapng"))
        for pcap_file in pcap_list:
            pcap_tasks.append((pcap_file, label_id))

    if not pcap_tasks:
        report(100, "Error: No .pcap or .pcapng files were found.")
        return {"status": "failed", "error": "No .pcap or .pcapng files found."}

    # print(f"Found {len(pcap_tasks)} pcap files in {len(label_dirs)} classes.")
    
    # 2. Feature extraction using ThreadPoolExecutor
    # This is safe to use in daemonic processes like Celery workers because it uses threads, not processes.
    # The GIL is released when calling the external tshark process, so we get true concurrency.
    report(20, "Extracting features from pcap files...")
    
    all_features = []
    
    # print("Using ThreadPoolExecutor for parallel feature extraction.")
    # Use max_workers=os.cpu_count() to balance CPU load from multiple tshark instances
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        futures = {executor.submit(_extract_features_from_pcap_worker, task): task for task in pcap_tasks}
        
        for i, future in enumerate(as_completed(futures)):
            try:
                result = future.result()
                if result:
                    all_features.extend(result)
            except Exception as e:
                # A worker thread might fail
                pcap_file, _ = futures[future]
                print(f"!!! A failure occurred in the main loop for {pcap_file.name}: {e}")
            
            progress_percent = 20 + (i + 1) / len(pcap_tasks) * 60
            report(progress_percent, f"Processing file {i+1}/{len(pcap_tasks)}...")

    if not all_features:
        report(100, "Error: No valid features could be extracted from the pcap files.")
        return {"status": "failed", "error": "Feature extraction yielded no results."}
    
    # print(f"Successfully extracted features from {len(all_features)} sessions.")
    random.shuffle(all_features)
    
    # 3. Split data and write to files
    report(85, "Splitting data and creating dataset files...")

    labels = [f[0] for f in all_features]
    features = [f[1] for f in all_features]
    
    # Create a stratified split for train/test
    # Ensure there are at least 2 samples per class for stratification
    unique_labels, counts = np.unique(labels, return_counts=True)
    if len(labels) > 1 and all(counts > 1):
        sss = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
        train_index, test_index = next(sss.split(features, labels))
        X_train, X_test = [features[i] for i in train_index], [features[i] for i in test_index]
        y_train, y_test = [labels[i] for i in train_index], [labels[i] for i in test_index]
    else:
        print("Warning: Not enough samples or classes for a stratified split. Using a simple random split.")
        try:
             X_train, X_test, y_train, y_test = train_test_split(features, labels, test_size=0.2, random_state=42, stratify=labels if all(counts > 1) else None)
        except:
             X_train, X_test, y_train, y_test = train_test_split(features, labels, test_size=0.2, random_state=42)

    output_dir = task_dir / "dataset"
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir()

    # Write files
    for split_type, data in [("train", zip(y_train, X_train)), ("test", zip(y_test, X_test))]:
        with open(output_dir / f"{split_type}_dataset.tsv", "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f, delimiter="\t")
            writer.writerow(["label", "text_a"])
            writer.writerows(data)

    with open(output_dir / "label_map.txt", "w", encoding="utf-8") as f:
        # Sort by index for consistent output
        sorted_labels = sorted(label_map.items(), key=lambda item: item[1])
        for name, idx in sorted_labels:
            f.write(f"{name}\t{idx}\n")
    
    report(100, "Preprocessing complete!")
    return {
        "status": "success",
        "dataset_path": str(output_dir),
        "train_samples": len(y_train),
        "test_samples": len(y_test)
    }

# This part of the code handles being called as a script for debugging
if __name__ == '__main__':
    print("Running preprocessor in debug mode...")
    
    # IMPORTANT: Replace this path with the root folder containing class subdirectories with pcaps
    # Example structure:
    # C:/temp/my_pcaps/
    #  |- class_A/
    #  |   |- file1.pcap
    #  |- class_B/
    #  |   |- file2.pcap
    pcap_root_path = "C:/replace/with/your/pcap_root_folder"
    
    if "replace/with/your" in pcap_root_path:
        print("Please update the 'pcap_root_path' variable in the __main__ block to your test data directory.")
    else:
        def cli_progress(percent, message):
            print(f"[{int(percent)}%] {message}")

        result = run_preprocessing_logic(pcap_root_path, progress_callback=cli_progress)
        
        print("\n--- Preprocessing Result ---")
        print(result)
        if result['status'] == 'success':
            print(f"Dataset files created at: {result['dataset_path']}")
        print("--------------------------") 