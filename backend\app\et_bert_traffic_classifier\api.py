from fastapi import APIRouter, UploadFile, File, HTTPException
import os
import torch
import torch.nn as nn
from typing import List, Dict, Any
import pandas as pd
import io
import sys
import argparse
import json
from uer.utils.constants import *

# 动态设置模块路径
module_path = os.path.dirname(__file__)
if module_path not in sys.path:
    sys.path.append(module_path)

from uer.utils.vocab import Vocab
from uer.utils.config import load_hyperparam
from uer.utils.seed import set_seed
from uer.model_loader import load_model
from uer.layers import *
from uer.encoders import *
from uer.utils.tokenizers import BertTokenizer # <--- 引入 BertTokenizer

# 定义模型类 (从 eval_4_08.py 迁移)
class Classifier(nn.Module):
    def __init__(self, args):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.soft_targets = args.soft_targets
        self.soft_alpha = args.soft_alpha
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, self.labels_num)
        
        print(f"Classifier 初始化完成:")
        print(f"  labels_num: {self.labels_num}")
        print(f"  pooling: {self.pooling}")
        print(f"  embedding: {type(self.embedding).__name__}")
        print(f"  encoder: {type(self.encoder).__name__}")

    def forward(self, src, tgt, seg, soft_tgt=None):
        """
        Args:
            src: [batch_size x seq_length]
            tgt: [batch_size]
            seg: [batch_size x seq_length]
        """
        # 深度调试打印
        print(f"\n--- [API] ---")
        print(f"[API] src tensor sum: {src.sum()}")

        # Embedding.
        emb = self.embedding(src, seg)
        print(f"[API] emb tensor sum: {emb.sum()}")

        # Encoder.
        output = self.encoder(emb, seg)
        print(f"[API] encoder output tensor sum: {output.sum()}")

        # Target.
        if self.pooling == "mean":
            output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            output = output[:, -1, :]
        else:
            output = output[:, 0, :]
        
        print(f"[API] pooled output tensor sum: {output.sum()}")

        output = torch.tanh(self.output_layer_1(output))
        print(f"[API] output_layer_1 tensor sum: {output.sum()}")

        logits = self.output_layer_2(output)
        print(f"[API] logits tensor sum: {logits.sum()}")

        if tgt is not None:
            if self.soft_targets and soft_tgt is not None:
                loss = self.soft_alpha * nn.MSELoss()(logits, soft_tgt) + \
                       (1 - self.soft_alpha) * nn.NLLLoss()(nn.LogSoftmax(dim=-1)(logits), tgt.view(-1))
            else:
                loss = nn.NLLLoss()(nn.LogSoftmax(dim=-1)(logits), tgt.view(-1))
            return loss, logits
        else:
            return None, logits

# 全局缓存
model_cache = {}
tokenizer_cache = {}
args_cache = {}

# 明确定义特殊标记，作为全局常量
CLS_TOKEN = "[CLS]"
UNK_TOKEN = "[UNK]"
PAD_ID = 0


router = APIRouter()

def load_label_info(label_info_path: str) -> Dict[int, str]:
    """从文件加载标签映射信息"""
    label_dict = {}
    try:
        print(f"加载标签信息文件: {label_info_path}")
        with open(label_info_path, encoding="utf-8") as f:
            header = next(f)  # 跳过表头
            print(f"标签文件表头: {header.strip()}")
            
            line_count = 0
            for line in f:
                line_count += 1
                parts = line.strip().split("\t")
                if len(parts) >= 2 and parts[0].isdigit():
                    label_id = int(parts[0])
                    label_name = parts[1]
                    label_dict[label_id] = label_name
                    
                    # 打印前10个和最后10个标签，以便检查
                    if line_count <= 5 or line_count >= 500:
                        print(f"  标签 {label_id}: {label_name}")
                        
            print(f"共加载了 {len(label_dict)} 个标签")
    except Exception as e:
        print(f"加载标签信息文件失败: {e}")
        import traceback
        traceback.print_exc()
    return label_dict

@router.on_event("startup")
async def startup_event():
    """
    应用启动时加载模型和配置
    """
    try:
        # 定义模型相关文件的路径
        model_dir = os.path.dirname(__file__)
        config_path = os.path.join(model_dir, "bert_base_config.json")
        vocab_path = os.path.join(model_dir, "encryptd_vocab.txt")
        pretrained_model_path = os.path.join(model_dir, "finetuned_model.bin")
        label_info_path = os.path.join(model_dir, "label_info.txt")

        # --- 最终修正：完全复现原始脚本的参数初始化顺序 ---

        # 1. 创建一个空的 Namespace 对象，并设置 config_path
        args = argparse.Namespace(
            embedding="word_pos_seg",
            max_seq_length=512,  # 关键修正：显式设置模型结构所需的最大序列长度
            relative_position_embedding=False,
            relative_attention_buckets_num=32,
            remove_embedding_layernorm=False,
            remove_attention_scale=False,
            encoder="transformer",
            mask="fully_visible",
            layernorm_positioning="post",
            feed_forward="dense",
            remove_transformer_bias=False,
            layernorm="normal",
            bidirectional=False,
            factorized_embedding_parameterization=False,
            parameter_sharing=False,
            hidden_size=768,
            layers_num=12,
            heads_num=12,
            dropout=0.1,
            feedforward_size=3072,
            hidden_act="gelu",
            pooling="first",
            seq_length=128, # 关键修正：确保数据处理的序列长度也正确
            soft_targets=False,
            soft_alpha=0.5,
            do_lower_case=False # 关键修正：与原始脚本的默认值保持一致
        )
        
        # 2. 设定所有在 uer/opts.py 中定义的、且与模型构建相关的默认值
        #    这是最关键的一步，确保我们的基础参数与原始脚本完全一致。
        args.config_path = config_path
        
        # 3. 执行 load_hyperparam 的逻辑：用 JSON 文件中的值去覆盖默认值
        with open(args.config_path, mode="r", encoding="utf-8") as f:
            param = json.load(f)
        args_dict = vars(args)
        args_dict.update(param)
        args = argparse.Namespace(**args_dict)

        # 4. 补充运行时才确定的参数
        args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载词汇表并设置相关参数
        vocab = Vocab()
        vocab.load(vocab_path)
        args.vocab = vocab
        args.tgt_vocab = vocab
        args.vocab_path = vocab_path
        args.spm_model_path = None

        # 初始化 Tokenizer (现在 args 已经完全配置好了)
        tokenizer = BertTokenizer(args, do_lower_case=args.do_lower_case)
        tokenizer_cache["tokenizer"] = tokenizer
        
        # 设置标签数量
        label_info = load_label_info(label_info_path)
        if not label_info:
             raise ValueError("标签信息为空")
        args.labels_num = max(int(k) for k in label_info.keys()) + 1
        
        # 打印最终配置用于验证
        print("最终模型配置 (Args):", args)

        # 构建模型
        model = Classifier(args)

        # 加载预训练模型
        try:
            state_dict = torch.load(pretrained_model_path, map_location='cpu')
            
            # 关键诊断：加载模型权重，并捕获加载状态
            print("Loading model state dict with diagnostic...")
            missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)

            # 打印出缺失和意外的键，以进行诊断
            if missing_keys:
                print(f"Missing keys when loading state_dict: {missing_keys}")
            if unexpected_keys:
                print(f"Unexpected keys when loading state_dict: {unexpected_keys}")

            # 检查关键的分类层是否在缺失列表中
            critical_layers = ["output_layer_1.weight", "output_layer_2.weight"]
            if any(key in missing_keys for key in critical_layers):
                print("CRITICAL DIAGNOSTIC ERROR: Classification layers (output_layer_1 or output_layer_2) were not found in the model file!")
            else:
                print("Diagnostic check passed: Classification layers appear to be present in the model file.")

        except Exception as e:
            print(f"Failed to load model weights: {e}", exc_info=True)
            raise

        model = model.to(args.device) # 修正：将模型移动到 args.device，而不是硬编码到 CPU
        model.eval()

        # 缓存
        model_cache["model"] = model
        tokenizer_cache["tokenizer"] = tokenizer
        args_cache["args"] = args # <--- 统一缓存到 args_cache
        model_cache["label_info"] = label_info

        print(f"ET-BERT 流量分类模型加载成功！")

    except Exception as e:
        print(f"ET-BERT 流量分类模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        model_cache["model"] = None
        args_cache["args"] = None
        model_cache["label_info"] = None


def convert_dataframe_to_tensors(df: pd.DataFrame, args: Any):
    """
    将 pandas DataFrame 转换为模型所需的 tensors
    (终极修正版，严格复刻 run_classifier.py 的 read_dataset 逻辑)
    """
    # 从缓存中获取已经正确初始化的对象
    tokenizer = tokenizer_cache["tokenizer"]
    seq_length = args.seq_length

    dataset = []
    
    for _, row in df.iterrows():
        text_a = str(row["text_a"])
        tgt = int(row["label"])
        
        # --- 像素级复刻原始脚本的核心逻辑 ---
        # 1. 分词
        tokens = [CLS_TOKEN] + tokenizer.tokenize(text_a)
        
        # 2. 使用 tokenizer 的官方方法转换 ID (最关键的修正！)
        src = tokenizer.convert_tokens_to_ids(tokens)
        
        # 3. 创建 segment IDs
        seg = [1] * len(src)

        # 4. 截断 (在正确的位置，对 src 和 seg 同时操作)
        if len(src) > seq_length:
            src = src[:seq_length]
            seg = seg[:seq_length]
        
        # 5. 填充 (同样对 src 和 seg 同时操作)
        while len(src) < seq_length:
            src.append(PAD_ID)
            seg.append(PAD_ID)
        
        dataset.append((src, tgt, seg))
    
    # 批量转换为张量
    src_tensor = torch.LongTensor([sample[0] for sample in dataset])
    tgt_tensor = torch.LongTensor([sample[1] for sample in dataset])
    seg_tensor = torch.LongTensor([sample[2] for sample in dataset])
    
    return src_tensor, seg_tensor, tgt_tensor


@router.post("/evaluate_traffic")
async def evaluate_traffic(file: UploadFile = File(...)):
    """
    接收上传的 TSV 文件，使用加载的模型进行评估，并返回结果。
    """
    # 1. 从缓存中获取模型、Tokenizer和参数
    model = model_cache.get("model")
    tokenizer = tokenizer_cache.get("tokenizer")
    args = args_cache.get("args")
    label_info = model_cache.get("label_info")

    if not all([model, tokenizer, args, label_info]):
        raise HTTPException(status_code=503, detail="Model is not ready, please wait or check logs.")
    
    # 打印模型状态
    print(f"\n===== 开始评估 =====")
    print(f"模型类型: {type(model).__name__}")
    print(f"模型设备: {next(model.parameters()).device}")
    print(f"标签数量: {args.labels_num}")
    print(f"词汇表大小: {len(args.vocab.w2i)}")

    # 1. 读取和解析上传的文件
    try:
        contents = await file.read()
        df = pd.read_csv(io.BytesIO(contents), sep='\t')
        if "text_a" not in df.columns or "label" not in df.columns:
            raise HTTPException(status_code=400, detail="文件格式错误，必须包含 'text_a' 和 'label' 列")
        
        # 诊断信息：输出数据集的基本信息
        print(f"数据集信息: 行数={len(df)}, 列={df.columns.tolist()}")
        print(f"标签分布: {df['label'].value_counts().to_dict()}")
        
        # 检查标签是否在预期范围内
        unique_labels = df['label'].unique()
        min_label = min(unique_labels)
        max_label = max(unique_labels)
        print(f"标签范围: 最小={min_label}, 最大={max_label}, 唯一值数量={len(unique_labels)}")
        
        if max_label >= args.labels_num:
            print(f"警告: 数据集中的最大标签 {max_label} 超出了模型配置的标签数量 {args.labels_num}")
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件读取或解析失败: {e}")

    # 2. 将数据转换为 Tensors
    try:
        src_tensor, seg_tensor, tgt_tensor = convert_dataframe_to_tensors(df, args)
        
        # 诊断信息：输出张量的形状和内容摘要
        print(f"src_tensor shape: {src_tensor.shape}, dtype: {src_tensor.dtype}")
        print(f"seg_tensor shape: {seg_tensor.shape}, dtype: {seg_tensor.dtype}")
        print(f"tgt_tensor shape: {tgt_tensor.shape}, dtype: {tgt_tensor.dtype}")
        
        # 检查输入数据是否有效
        print(f"src_tensor 非零元素比例: {(src_tensor != 0).float().mean().item():.4f}")
        print(f"src_tensor 样本: {src_tensor[0, :20].tolist()}")  # 打印第一个样本的前20个词元
        
    except Exception as e:
        print(f"张量转换错误: {e}")
        raise HTTPException(status_code=500, detail=f"数据转换失败: {e}")

    # 3. 逐批次进行预测和评估
    model.eval()

    # --- 最终诊断：手动禁用所有 Dropout 层 ---
    print("正在手动禁用所有 Dropout 层...")
    for module in model.modules():
        if isinstance(module, torch.nn.Dropout):
            print(f"  - 禁用 Dropout 层: {module}")
            module.p = 0.0 # 将失活概率设置为0

    correct_count = 0
    total_count = len(df)
    # 最终修正：将 batch_size 改为 1，与原始脚本完全一致
    batch_size = 32
    confusion_matrix = torch.zeros(args.labels_num, args.labels_num, dtype=torch.long)
    results_details = []

    with torch.no_grad():
        for i in range(0, total_count, batch_size):
            end_idx = min(i + batch_size, total_count)
            
            src_batch = src_tensor[i:end_idx].to(args.device)
            seg_batch = seg_tensor[i:end_idx].to(args.device)
            tgt_batch = tgt_tensor[i:end_idx].to(args.device) # 将真实标签也移动到设备上

            # --- 关键修正：完全复现原始脚本的预测逻辑 ---
            # 1. 像原始脚本一样，将 tgt_batch 传递给模型
            _, logits = model(src_batch, tgt=tgt_batch, seg=seg_batch) 
            
            # 2. 精确匹配原始脚本，在 logits 上应用 softmax 后再进行 argmax
            pred = torch.argmax(torch.nn.functional.softmax(logits, dim=1), dim=1)
            
            # 将预测结果移回 CPU 进行比较
            pred_cpu = pred.cpu()
            tgt_cpu = tgt_batch.cpu() # 从设备移回CPU
            
            # 更新统计
            correct_count += torch.sum(pred_cpu == tgt_cpu).item()
            
            for j in range(len(pred_cpu)):
                true_label = tgt_cpu[j].item()
                pred_label = pred_cpu[j].item()
                confusion_matrix[true_label, pred_label] += 1
                
                results_details.append({
                    "text": df["text_a"].iloc[i + j],
                    "true_label_id": true_label,
                    "true_label_name": label_info.get(true_label, "Unknown"),
                    "predicted_label_id": pred_label,
                    "predicted_label_name": label_info.get(pred_label, "Unknown"),
                    "is_correct": bool(pred_label == true_label)
                })

    accuracy = correct_count / total_count if total_count > 0 else 0
    
    # 计算每个类别的精确率、召回率和F1值
    precision_recall_f1 = []
    for i in range(args.labels_num):
        # 避免除零错误
        eps = 1e-9
        precision = confusion_matrix[i, i].item() / (confusion_matrix[i, :].sum().item() + eps)
        recall = confusion_matrix[i, i].item() / (confusion_matrix[:, i].sum().item() + eps)
        f1 = 2 * precision * recall / (precision + recall + eps) if precision + recall > 0 else 0
        
        if confusion_matrix[i, :].sum().item() > 0 or confusion_matrix[:, i].sum().item() > 0:
            precision_recall_f1.append({
                "label_id": i,
                "label_name": label_info.get(i, "Unknown"),
                "precision": f"{precision:.4f}",
                "recall": f"{recall:.4f}",
                "f1": f"{f1:.4f}"
            })

    return {
        "filename": file.filename,
        "num_samples": total_count,
        "accuracy": f"{accuracy:.4f}",
        "correct_predictions": correct_count,
        "confusion_matrix": confusion_matrix.tolist(),
        "precision_recall_f1": precision_recall_f1,
        "details": results_details,
        "debug_info": [],  # 包含调试信息在响应中
        "prediction_distribution": []  # 添加预测分布信息
    } 