from fastapi import APIRouter, UploadFile, File, HTTPException
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
import torch
import numpy as np
import os
import io
import pickle
from torch.utils.data import DataLoader, TensorDataset

# 确保能正确导入同级目录下的模块
from .model import TF_classifier

router = APIRouter()

# --- 模型和设备配置 ---
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# 注意: 模型权重路径现在指向原始的 .pkl 文件
MODEL_PATH = "app/models/fa_net/tunnel_behavior_fs.pkl"

def load_model_for_eval():
    """
    加载用于评估的FA-Net模型。
    此实现现在严格遵循独立项目中的模型定义和参数。
    """
    if not os.path.exists(MODEL_PATH):
        raise HTTPException(status_code=500, detail=f"模型文件未找到: {MODEL_PATH}")

    # --- 1. 初始化与训练时完全一致的模型结构 ---
    # (参数与独立项目 ttrain.py 和 evaluate.py 对齐)
    model = TF_classifier(
        nb_classes=8,
        inter_head_nums=8, 
        inter_block_nums=3, 
        inter_proj_nums=32, 
        inter_sequence_length=10,
        intra_head_nums=8, 
        intra_block_nums=3, 
        intra_proj_nums=32, 
        intra_sequence_length=20,
        embedding_dim=128,
        dropout=0.3,
        input_time=True,
        input_dirt=True
    ).to(DEVICE)
    
    try:
        # --- 2. 使用 PyTorch 原生 API 加载模型权重 ---
        # 增加对 DataParallel 'module.' 前缀的处理
        state_dict = torch.load(MODEL_PATH, map_location=DEVICE)
        if list(state_dict.keys())[0].startswith('module.'):
            state_dict = {k[len('module.'):]: v for k, v in state_dict.items()}
            
        model.load_state_dict(state_dict)
        model.eval()
        return model
    except Exception as e:
        # 提供更详细的错误信息
        raise HTTPException(status_code=500, detail=f"加载模型权重失败: {e}")

def batch_predict(model, data_batch):
    """对一个批次的数据进行预测"""
    # 确保数据已经是 torch.Tensor 类型
    seqs = data_batch.float().to(DEVICE)
    with torch.no_grad():
        predict_logit = model(seqs)
        _, predicted = torch.max(predict_logit.data, 1)
    return predicted.cpu().numpy()


@router.post("/evaluate/fanet_pkl")
async def evaluate_fanet_from_pkl(pkl_file: UploadFile = File(...)):
    """
    从上传的 .pkl 缓存文件评估 FA-Net 模型。
    此端点的逻辑现在与独立项目中的 `test_pkl.py` 对齐。
    """
    try:
        contents = await pkl_file.read()
        buffer = io.BytesIO(contents)
        data = pickle.load(buffer)
        
        # 使用与 export_test_set.py 一致的键名 'X_test' 和 'y_test'
        X_test = data['X_test']
        y_test = data['y_test']
        class_name_map = data.get('class_names', {})
        
        # 将原始的 {name: id} 映射转换为API报告所需的 [name_at_id_0, name_at_id_1, ...] 列表
        idx_to_name = {v: k for k, v in class_name_map.items()}
        class_names = [idx_to_name.get(i, f"未知类别_{i}") for i in range(len(idx_to_name))]

    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"无效的 .pkl 文件: 缺少必需的键 {e}。文件应包含 'X_test' 和 'y_test'。")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"无效或损坏的 .pkl 文件: {e}")

    model = load_model_for_eval()
    
    batch_size = 32
    all_predictions = []
    
    # --- 核心改造: 使用 DataLoader 进行标准的数据加载和批次处理 ---
    test_dataset = TensorDataset(X_test) # 评估时只需要特征
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    for (batch_X,) in test_loader: # DataLoader 会将单个张量包装在元组中
        predictions = batch_predict(model, batch_X)
        all_predictions.extend(predictions)

    if not all_predictions:
        raise HTTPException(status_code=400, detail="在 .pkl 文件中没有找到有效的样本进行评估。")

    true_labels_np = y_test.numpy()
    
    # --- 核心改造: 使用 classification_report 生成详细报告 ---
    # `output_dict=True` 使其返回一个可以轻松转换为JSON的字典
    report = classification_report(
        true_labels_np, 
        all_predictions, 
        target_names=class_names, 
        digits=4, 
        zero_division=0,
        output_dict=True
    )
    
    cm = confusion_matrix(true_labels_np, all_predictions, labels=list(range(len(class_names))))

    # --- 最终修正: 同时提供顶层指标(供UI使用)和详细报告(供分析使用) ---
    weighted_avg = report.get("weighted avg", {})

    return {
        "accuracy": report.get("accuracy", 0.0),
        "precision": weighted_avg.get("precision", 0.0),
        "recall": weighted_avg.get("recall", 0.0),
        "f1_score": weighted_avg.get("f1-score", 0.0),
        "classification_report": report,
        "confusion_matrix": cm.tolist(),
        "class_names": class_names,
        "total_samples": len(all_predictions)
    } 