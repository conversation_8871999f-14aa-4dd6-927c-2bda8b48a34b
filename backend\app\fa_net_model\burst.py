import os
import json
import numpy as np
import random as Random
import gzip
import pickle
import torch as th
import tqdm

class Dataset_fgnet:
    '''
    FGNet 数据集的访问类
    '''
    MTU = 1500
    burst_nums_list = []
    burst_size_list = []
    flow_size_list = []
    def __init__(self, raw_dir, dumpfile, renew=False, batch_size=128, split_rate=0.1, randseed = 128,
                 max_flow_len=200, max_burst_size=20, max_burst_nums=50, labelname= {}, reshuffle_data=True):
        '''

        :param raw_dir:        FG-Net 数据集的原始目录，这些数据应该是在data/fgnet_dataset目录
        :param dumpfile:       数据集序列化文件,
        :param renew:          是否重新构建数据集
        :param split_rate:     数据集划分比例; 测试集和验证集的占比
        :param batch_size:     批大小
        :param randseed :      随机数种子
        :param max_burst_nums: 每条流最大的burst长度
        :param max_flow_len:   每条流截取的最大包个数
        :param max_burst_size: 每个burst包含的最大包个数
        :return:
        '''

        self.split_rate = split_rate
        self.randseed = randseed
        self.my_rand = Random.Random(self.randseed)

        self.max_burst_nums = max_burst_nums
        self.max_burst_size = max_burst_size
        self.max_flow_len = max_flow_len

        self.batch_size = batch_size
        self.raw_directory = raw_dir
        self.dumpfile = dumpfile

        self.flow_length = []
        self.flow_graph  = []
        self.flow_label  = []
        self.labelname = labelname #标签对应的真实app名称

        self.train_set = []
        self.valid_set = []
        self.test_set = []

        self.train_watch = 0
        self.test_watch = 0
        self.valid_watch = 0
        self.epoch_num = 0


        if renew == False:
            if os.path.exists(dumpfile):
                pass
            else:
                pass
        else:
            pass
        if reshuffle_data== True:
            pass
    
    def load_data(self, renew=False, reshuffle_data=True):
        """
        显式加载数据的方法，将耗时操作从__init__中分离。
        """
        if renew == False:
            if os.path.exists(self.dumpfile):
                self.load_dumpfile()
            else:
                self.load_raw_dateset()
        else:
            self.load_raw_dateset()
        if reshuffle_data== True:
            self.shuffle_dataset()
        self.my_rand.shuffle(self.train_set)
        self.my_rand.shuffle(self.test_set)
        self.my_rand.shuffle(self.valid_set)
        print(Dataset_fgnet.burst_size_list)
        print(self.__str__())

    def split_burst(self, packet_len, arr_time_delta, dirtion, burst_size=50, burst_num=200, time_shift =1):
        class _burst:
            def __init__(self, s, e):
                self.start = s
                self.end = e
            def __str__(self):
                return '[{0},{1})'.format(self.start,self.end)
        burst = [_burst(0,1)]

        for i in range(1,len(dirtion)):
            if dirtion[i] == 0:
                #已经到了末尾
                break
            if dirtion[burst[-1].start] == dirtion[i]:
                burst[-1].end += 1
            else:
                Dataset_fgnet.burst_size_list.append(burst[-1].end - burst[-1].start)
                burst.append(_burst(i, i+1))
        Dataset_fgnet.flow_size_list.append(burst[-1].end)
        Dataset_fgnet.burst_size_list.append(burst[-1].end - burst[-1].start)
        Dataset_fgnet.burst_nums_list.append(len(burst))

        burst += [_burst(0,0)] * burst_num
        #填充
        packet_len_burst =[]
        arr_time_delta_burst = []
        dirtion_burst =[]
        for each in burst:
            #print(each)
            len_b = packet_len[each.start:each.end] + [0] * burst_size
            time_b = (np.array(arr_time_delta[each.start:each.end]) - arr_time_delta[each.start] + time_shift).tolist() +[0.0] * burst_size
            dirt_b = dirtion[each.start:each.end] + [0] * burst_size

            len_b = len_b[:burst_size]
            time_b = time_b[:burst_size]
            dirt_b = dirt_b[:burst_size]

            packet_len_burst.append(len_b)
            arr_time_delta_burst.append(time_b)
            dirtion_burst.append(dirt_b)
        return packet_len_burst[:burst_num], arr_time_delta_burst[:burst_num], dirtion_burst[:burst_num]

    def load_raw_dateset(self, raw_dir= None):
        if raw_dir == None:
            raw_dir = self.raw_directory
      
        self.flow_length = []
        self.flow_time = []
        self.flow_dirt = []
        self.flow_graph  = []
        self.flow_label  = []
        #self.labelname = {} #标签对应的真实app名称

        flow_length = []
        flow_label = []
        flow_time = []
        flow_dirt = []
        print(os.path.exists(raw_dir))
        for _root, _dirs, _files in os.walk(raw_dir):
            print(_files)
            for file in _files:
                if '.json'  in file:
                    package = ".".join(file.split(".")[:-1])
                    if package not in self.labelname :
                        self.labelname.setdefault(package,len(self.labelname))
                        print('add new type:', package, self.labelname[package])
                    print(file)
                    file  = _root + "/" + file
                    with open(file) as fp:
                        flows = json.load(fp)
                    #if len(flow_label) > 50 :
                    #    break
                    for flow in flows:
                        packet_length = flow['packet_length']
                        arr_time_delta = flow['arrive_time_delta'] if 'arr_time_delta' in flow else [0.0] * len(flow['packet_length'])
                        if len(packet_length) < 10 :
                            continue
                        packet_length, arr_time_delta = self.refine_packet_length(packet_length, arr_time_delta, max_len= self.max_flow_len)
                        dirt  =  np.sign(packet_length).tolist()
                        packet_length, arr_time_delta, dirt = self.split_burst(packet_length,arr_time_delta,dirt, burst_size=self.max_burst_size, burst_num= self.max_burst_nums)
                        ###每条流packet_length的形状 : [max_burst_nums, max_burst_size]
                        ###每条流arr_time_delta的形状: [max_burst_nums, max_burst_size]
                        ###每条流dir 的形状:           [max_burst_nums, max_burst_size]
                        flow_length.append(packet_length)
                        flow_time.append(arr_time_delta)
                        flow_dirt.append(dirt)

                        flow_label.append(self.labelname[package])

        for i in tqdm.trange(len(flow_length)):
            if len(flow_dirt[i]) == 0:
                continue
            self.flow_length.append(flow_length[i])
            self.flow_dirt.append(flow_dirt[i])
            self.flow_time.append(flow_time[i])
            self.flow_label.append(flow_label[i])
        ##划分数据集
        self.shuffle_dataset()
        self.save_dumpfile()
    def shuffle_dataset(self):
        self.train_set = []
        self.valid_set = []
        self.test_set = []
        for i in range(len(self.flow_label)):
            r = self.my_rand.uniform(0, 1)
            if r < self.split_rate:
                if r / (self.split_rate+1e-9) < 0.5:
                   self.test_set.append(i)
                else:
                   self.valid_set.append(i)
            else:
                self.train_set.append(i)	
    def refine_packet_length(self,packet_length, arr_time_delta, max_len, time_shift=1):
        rst_len =[]
        rst_time =[]
        for i in range(min(len(packet_length), max_len)):
            if abs(packet_length[i]) < 1500 :
                rst_len.append(packet_length[i])
                rst_time.append(arr_time_delta[i] + time_shift)
            else:
                sign = np.sign(packet_length[i])
                each = abs(packet_length[i])
                while each > 1500:
                    rst_len.append(sign * 1500)
                    rst_time.append(arr_time_delta[i] + time_shift)
                    each = each -1500
        if len(rst_len)< max_len :
            rst_len = rst_len + [0] * (max_len - len(rst_len))
            rst_time = rst_time + [0] * (max_len - len(rst_time))
        return  rst_len[:max_len], rst_time[:max_len]
    def load_dumpfile(self, dumpfile = None):
        print('Load data dumpfile for history.')
        if dumpfile == None :
            dumpfile = self.dumpfile
        fp = gzip.GzipFile(dumpfile,'rb')
        data = pickle.load(fp)
        fp.close()
        self.flow_label = data['flow_label']
        self.flow_length = data['flow_length']
        self.flow_dirt = data['flow_dirt']
        self.flow_time = data['flow_time']
        self.train_set = data['train_set']
        self.test_set = data['test_set']
        # self.valid_set = data['valid_set']
        self.labelname = data['labelname']

    def save_dumpfile(self,dumpfile=None ):
        if dumpfile== None:
            dumpfile = self.dumpfile
        fp = gzip.GzipFile(dumpfile,'wb')
        pickle.dump({
            'flow_length': self.flow_length,
            'flow_time': self.flow_time,
            'flow_dirt': self.flow_dirt,
            'flow_label': self.flow_label,
            'train_set': self.train_set,
            'test_set': self.test_set,
            # 'valid_set': self.valid_set,
            'labelname': self.labelname
        },file = fp,protocol=2)
        fp.close()

    def __next_batch(self,name,batch_size):
        seqs = []
        labels =[]

        for i in range(batch_size):
            if name == 'train':
                #print(self.train_watch,len(self.train_set),len(self.flow_graph),self.train_set[self.train_watch])
                seqs.append([self.flow_length[self.train_set[self.train_watch]], self.flow_time[self.train_set[self.train_watch]], self.flow_dirt[self.train_set[self.train_watch]]])
                labels.append(self.flow_label[self.train_set[self.train_watch]])

                if (self.train_watch + 1) == len(self.train_set):
                    self.epoch_num += 1
                    Random.shuffle(self.train_set)
                self.train_watch = (self.train_watch + 1) % len(self.train_set)
            elif name =='valid':
                seqs.append([self.flow_length[self.valid_set[self.valid_watch]], self.flow_time[self.valid_set[self.valid_watch]], self.flow_dirt[self.valid_set[self.valid_watch]]])
                labels.append(self.flow_label[self.valid_set[self.valid_watch]])
                self.valid_watch = (self.valid_watch + 1) % len(self.valid_set)
            else:
                seqs.append([self.flow_length[self.test_set[self.test_watch]], self.flow_time[self.test_set[self.test_watch]], self.flow_dirt[self.test_set[self.test_watch]]])
                labels.append(self.flow_label[self.test_set[self.test_watch]])
                self.test_watch = (self.test_watch + 1) % len(self.test_set)
        ###seqs的形状：  [batch_size, 3 , max_burst_nums, max_burst_size]
        ###labels的形状: [batch_size,]

        return th.tensor(seqs), th.tensor(labels)

    def next_train_batch(self,batch_size=None):
        if batch_size == None:
            batch_size = self.batch_size
        return self.__next_batch('train', batch_size)
    def next_valid_batch(self,batch_size= None):
        if batch_size == None:
            batch_size = self.batch_size
        return self.__next_batch('valid', batch_size)

    def next_test_batch(self, batch_size= None):
        if batch_size == None:
            batch_size = self.batch_size
        return self.__next_batch('test', batch_size)

    def __str__(self):
        return  'Total Sample:{0}, train sample: {1}, test sample: {2}, valid sample: {3}, class num: {4}, dumpfile:{5}'.format(len(self.flow_label),len(self.train_set),len(self.test_set),len(self.valid_set),len(self.labelname), self.dumpfile)
def print_data_statistic(list):
    avg = np.mean(list)
    std = np.std(list)
    percent = [10,20,30,40,50,60,70,80,90,95,99]
    print('\taverage: ', avg)
    print('\tstd: ', std)

    for each_percent in percent:
        print("\tP( v<={1}) = {0}".format(each_percent, np.percentile(list,each_percent)))

if __name__ == '__main__':
    labelname = {'air.ITVMobilePlayer': 0, 'com.vkontakte.android': 1, 'com.ss.android.ugc.aweme': 2, 'tv.danmaku.bili': 3, 'com.joelapenna.foursquared': 4, 'com.imdb.mobile': 5, 'info.androidz.horoscope': 6, 'bbc.mobile.weather': 7, 'cn.cntv': 8, 'com.shazam.android': 9, 'com.xunmeng.pinduoduo': 10, 'uk.co.nationalrail.google': 11, 'com.spotify.music': 12, 'com.flipkart.android': 13, 'com.jingdong.app.mall': 14, 'com.twitter.android': 15, 'com.google.android.gm': 16, 'com.cnn.mobile.android.phone': 17, 'com.particlenews.newsbreak': 18, 'com.kakao.talk': 19, 'com.gumtree.android': 20, 'com.taobao.taobao': 21, 'com.amazon.mShop.android.shopping': 22, 'com.nytimes.android': 23, 'com.instagram.android': 24, 'com.booking': 25, 'com.tripadvisor.tripadvisor': 26, 'com.contextlogic.wish': 27, 'com.snapchat.android': 28, 'com.dropbox.android': 29, 'com.tencent.qqlivei18n': 30, 'tv.twitch.android.app': 31, 'com.facebook.katana': 32, 'ru.ideast.championat': 33, 'sg.bigo.live': 34, 'com.vidio.android': 35, 'com.groupon': 36, 'com.ideashower.readitlater.pro': 37, 'com.skype.raider': 38, 'com.lenovo.anyshare.gps': 39, 'com.sina.weibo': 40, 'com.shpock.android': 41, 'com.amazon.kindle': 42, 'com.badoo.mobile': 43, 'com.reddit.frontpage': 44, 'com.google.android.youtube': 45, 'com.guardian': 46, 'com.pinterest': 47, 'com.imo.android.imoim': 48, 'kik.android': 49, 'flipboard.app': 50, 'com.ted.android': 51, 'com.iconology.comics': 52}
	
    dator = Dataset_fgnet(raw_dir='',dumpfile='app53_burst.gzip', renew= False, split_rate=0.99, labelname=labelname)
    print(dator.labelname)
    #exit(0)
    print('flow size:')
    print_data_statistic(Dataset_fgnet.flow_size_list)

    print('burst num:')
    print_data_statistic(Dataset_fgnet.burst_nums_list)
    print('burst size:')
    print_data_statistic(Dataset_fgnet.burst_size_list)

