import math
import torch
import  torch as th
from .burst import Dataset_fgnet
import torch.nn as nn
from .attention_imp import MultiheadAttention
from torch.nn import functional as F
# this is from T5
def relative_position_bucket(relative_position, bidirectional=True, num_buckets=32, max_distance=128):
    ret = torch.zeros_like(relative_position, dtype=torch.long)
    n = -relative_position
    if bidirectional:
        num_buckets //= 2
        ret += (n < 0).to(torch.long) * num_buckets  # mtf.to_int32(mtf.less(n, 0)) * num_buckets
        n = torch.abs(n)
    else:
        n = torch.max(n, torch.zeros_like(n))
    # now n is in the range [0, inf)

    # half of the buckets are for exact increments in positions
    max_exact = num_buckets // 2
    is_small = n < max_exact

    # The other half of the buckets are for logarithmically bigger bins in positions up to max_distance
    val_if_large = max_exact + (
        torch.log(n.float() / max_exact) / math.log(max_distance / max_exact) * (num_buckets - max_exact)
    ).to(torch.long)
    val_if_large = torch.min(val_if_large, torch.full_like(val_if_large, num_buckets - 1))

    ret += torch.where(is_small, n, val_if_large)
    return ret

class MultiHeadAttentionBlock(nn.Module):
    def __init__(self, input_dim,      #输入的维度
                 head_nums,             #头数目
                 dim_feedforward=128,  #前向的输出维度
                 dropout=0,
                 activation=nn.ReLU()):
        super(MultiHeadAttentionBlock, self).__init__()
        ##Attention 层
        self.attn_layer = MultiheadAttention(embed_dim=input_dim,
                                             num_heads=head_nums,
                                             dropout=dropout,
                                             bias=False)
        ## 前馈网络层

        self.linear1 = nn.Linear(input_dim, dim_feedforward )
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward,input_dim)

        self.norm1 = nn.LayerNorm(input_dim)
        self.norm2 = nn.LayerNorm(input_dim)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)

        self.activation = activation

    def forward(self, src , attn_bias, evolving_attn_alpha=0, evolving_attn_beta=0, evolving_attn_cnn_layer=None, evolving_attn_prev_attn=None):
        src2, attn, attn_logits = self.attn_layer(src,src,src,
                                     attn_bias= attn_bias, ##位置编码
                                     evolving_attn_cnn_layer= evolving_attn_cnn_layer, evolving_attn_alpha= evolving_attn_alpha,
                                     evolving_attn_beta= evolving_attn_beta, evolving_attn_prev_attn= evolving_attn_prev_attn)
        src = src + self.dropout1(src2)

        src = self.norm1(src)

        src2 = self.linear2(self.dropout(self.activation(self.linear1(src))))
        src = src + self.dropout2(src2)
        src = self.norm2(src)

        ##attn是注意力值

        return src, attn, attn_logits


class MultiHeadAttentionBlocks(nn.Module):
    def __init__(self,input_dim,
                 head_nums,
                 block_nums,
                 dropout=0,
                 activation = nn.ReLU(),
                 abs_position=False,
                 rel_position=False,
                 rel_position_k=25,
                 max_seq_len=50,
                 evolving_attn = False,  # 是否开启evolving attention 机制, 实现原理参见 Evolving Attention with Residual Convolutions
                 evolving_cnn_kernel_size = 3,  # 如果开启的话,其中的卷积核大小默认为3x3
                 evolving_alpha = 0.5,  # evolving attention里面用于调节 本block的attention和上一步attention的比例系数
                 evolving_beta = 0.4,  # evolving attention里面用于调节 本block的卷积残差
                 evolving_cnn_share = False  # evolving attention里面残差卷积块是否在不同的block之间共享
                 ):
        super(MultiHeadAttentionBlocks,self).__init__()

        self.max_seq_len = max_seq_len
        self.head_nums = head_nums
        self.block_nums = block_nums
        self.embedding_dim = input_dim
        self.abs_position = abs_position
        self.rel_position = rel_position
        self.evolving_attn_mechanism = evolving_attn
        self.evolving_cnn_kernel_size = evolving_cnn_kernel_size
        self.evolving_alpha = evolving_alpha
        self.evolving_beta = evolving_beta
        self.evolving_cnn_share = evolving_cnn_share


        self.evolving_cnn_layers = nn.ModuleList()
        if self.evolving_attn_mechanism == True:
            cnn_number = 1 if self.evolving_cnn_share == True else self.block_nums
            assert evolving_cnn_kernel_size % 2 == 1        #因为需要same 卷积,因此要保证kernel size是基数的
            padding_size = (evolving_cnn_kernel_size - 1) // 2
            stride_size = 1
            dilation = 1
            for each_cnn in range(cnn_number):
                self.evolving_cnn_layers.append(nn.Conv2d(in_channels=self.head_nums, out_channels=self.head_nums,
                                                          kernel_size=self.evolving_cnn_kernel_size, stride=stride_size,
                                                          padding=padding_size,
                                                          dilation=dilation))
                #考虑到cnn前后的形状要一致,因此要合理的配置 stride和padding.

        self.layers = nn.ModuleList()
        for each in range(block_nums):
            self.layers.append(MultiHeadAttentionBlock(input_dim=input_dim,
                                                       head_nums=head_nums,
                                                       dropout=dropout,
                                                       activation=activation))

        if abs_position:
            ###准备绝对位置的编码
            self.attn_scale_factor = 2
            self.num_attention_heads = head_nums
            self.pos = nn.Embedding(self.max_seq_len + 1, self.embedding_dim)
            self.pos_q_linear = nn.Linear(self.embedding_dim, self.embedding_dim)
            self.pos_k_linear = nn.Linear(self.embedding_dim, self.embedding_dim)
            self.pos_scaling = float(self.embedding_dim / head_nums * self.attn_scale_factor) ** -0.5
            self.pos_ln = nn.LayerNorm(self.embedding_dim)

        if rel_position:
            ###准备相对位置编码
            self.max_rel_pos = rel_position_k
            self.rel_position_attn_bias = nn.Embedding(self.max_rel_pos + 1, embedding_dim= head_nums)
            seq_len = self.max_seq_len
            context_position = torch.arange(seq_len, dtype=torch.long)[:, None]
            memory_position = torch.arange(seq_len, dtype=torch.long)[None, :]
            relative_position = memory_position - context_position
            self.rp_bucket = relative_position_bucket(
                relative_position,
                num_buckets=self.max_rel_pos,
                max_distance=self.max_rel_pos
            )

    def get_rel_pos_bias(self, x):
        # Assume the input is ordered. If your input token is permuted, you may need to update this accordingly
        if self.rp_bucket.device != x.device:
            self.rp_bucket = self.rp_bucket.to(x.device)
        seq_len = x.size(0)
        rp_bucket = self.rp_bucket[:seq_len, :seq_len]
        #rp_bucket shape:  [seq_len, seq_len]
        values = F.embedding(rp_bucket, self.rel_position_attn_bias.weight)
        #values shapes: [seq_len,seq_len, num_attention_heads]
        values = values.permute([2, 0, 1])
        #values shapes: [num_attention_heads, seq_len, seq_len]

        return values.contiguous()

    def forward(self, src, need_attn=False):
        if self.rel_position:
            #准备相对位置bias
            rel_pos_bias = self.get_rel_pos_bias(src)
        else:
            rel_pos_bias = None

        if self.abs_position:
            #准备绝对位置编码
            seq_len = src.size(0)
            weight = self.pos_ln(self.pos.weight[:seq_len ,: ])
            pos_q = self.pos_q_linear(weight).view(seq_len, self.head_nums, -1).transpose(0, 1) * self.pos_scaling
            pos_k = self.pos_k_linear(weight).view(seq_len , self.num_attention_heads, -1).transpose(0, 1)
            abs_pos_bias = torch.bmm(pos_q, pos_k.transpose(1,2))
            if rel_pos_bias is not None:
                abs_pos_bias += rel_pos_bias
            abs_pos_bias = abs_pos_bias.reshape(-1, seq_len, seq_len)
            #print(abs_pos_bias.shape)
        else:
            abs_pos_bias = None



        ## Now everything goes together...
        output = src
        attn_logit = None
        for index, mod in enumerate(self.layers):
            ##第一个block是没有前置block的attn的
            if self.evolving_attn_mechanism:
                cnn_layer = self.evolving_cnn_layers[0] if self.evolving_cnn_share else self.evolving_cnn_layers[index]
            else:
                cnn_layer = None
            output, attn, attn_logit = mod(output, attn_bias = abs_pos_bias,
                                           evolving_attn_alpha= self.evolving_alpha, evolving_attn_beta= self.evolving_beta, evolving_attn_cnn_layer= cnn_layer, evolving_attn_prev_attn= attn_logit )

        if not need_attn:
            return  output
        else:
            return output, attn

class Transformer(nn.Module):
    def __init__(self,input_dim, head_nums, block_nums, dropout, proj_num, max_seq_len, rel_position, abs_position, evolving_attn, evolving_attn_cnn_share, evolving_alpha, evolving_beta, evolving_kernel_size):
        super(Transformer, self).__init__()
        self.input_dim = input_dim
        self.head_nums = head_nums
        self.block_nums = block_nums
        self.dropout = dropout
        self.proj_num = proj_num

        self.multihead_blocks = MultiHeadAttentionBlocks(input_dim=self.input_dim, head_nums=self.head_nums, block_nums=self.block_nums, dropout=dropout,
                                                         max_seq_len= max_seq_len,
                                                         rel_position= rel_position, abs_position=abs_position,
                                                         evolving_attn=evolving_attn,evolving_beta= evolving_beta, evolving_alpha= evolving_alpha,
                                                         evolving_cnn_kernel_size = evolving_kernel_size, evolving_cnn_share= evolving_attn_cnn_share)

        self.project_layer = nn.Linear(in_features=self.input_dim, out_features= self.proj_num, bias=False)

    def forward(self, input_feature):
        #input_feature 形状: [batch_size, sequence_length, embed_size]
        #output 形状: [batch_size, sequence_length * proj_num ]
        batch_size, sequence_length, embed_size = input_feature.shape
        ##改变维度[batch_size, sequence_length, embed_size] ==> [sequence_length, batch_size, embed_size]

        input_feature = input_feature.permute(1,0,2)
        ##特征注意力
        feature_attn = self.multihead_blocks(input_feature)
        ##变换回维度
        feature_attn = feature_attn.permute(1,0,2)
        ##特征投影,目的是降低复杂度
        feature_proj = self.project_layer(feature_attn)
        feature_proj = th.reshape(feature_proj, (-1, sequence_length * self.proj_num))
        ##返回
        return feature_proj

class TF_classifier(nn.Module):
    def __init__(self, nb_classes=7,
                 inter_head_nums= 2,
                 inter_block_nums= 2,
                 inter_proj_nums = 30,
                 inter_sequence_length = 10,
                 intra_head_nums = 5,
                 intra_block_nums = 4,
                 intra_proj_nums = 30,
                 intra_sequence_length = 20,
                 time_gransize= 0.05,
                 embedding_dim = 128,
                 dropout=0,
                 len_embedding = True,  #是否开启包长embedding
                 dirt_embedding = True, #是否开启方向embedding
                 time_embedding = True, #是否开启时间embedding
                 input_time = True,     #是否输入包达到时间
                 input_dirt = True,     #是否输入包方向信息
                 relative_position= True, #是否进行相对位置编码
                 abs_position = True,    #是否进行绝对位置编码
                 evolving_attn = True,   #是否开启Evolving attention 机制
                 evolving_alpha = 0.5,
                 evolving_beta = 0.4,
                 evolving_kernel_size = 3,
                 evolving_cnn_share = False, #Evolving Attenttion里面的CNN层是否需要共享
                 ):
        super(TF_classifier,self).__init__()

        self.MTU = 1500 # 标准以太网MTU值，用于归一化
        self.nb_classes = nb_classes
        self.inter_head_nums = inter_head_nums
        self.inter_block_nums = inter_block_nums
        self.inter_proj_nums = inter_proj_nums
        self.inter_sequence_length = inter_sequence_length

        self.intra_head_nums = intra_head_nums
        self.intra_block_nums = intra_block_nums
        self.intra_proj_nums = intra_proj_nums
        self.intra_sequence_length = intra_sequence_length


        self.time_gransize = time_gransize
        self.embedding_dim = embedding_dim

        self.len_embedding = len_embedding
        self.dirt_embedding = dirt_embedding
        self.time_embedding = time_embedding
        self.input_time = input_time
        self.input_dirt = input_dirt

        # Save additional configuration parameters so they can be reused later
        self.dropout = dropout
        self.relative_position = relative_position
        self.abs_position = abs_position
        self.evolving_attn = evolving_attn
        self.evolving_alpha = evolving_alpha
        self.evolving_beta = evolving_beta
        self.evolving_kernel_size = evolving_kernel_size
        self.evolving_cnn_share = evolving_cnn_share

        in_feat = 0
        if len_embedding :
            self.len_embedding_layer = th.nn.Embedding(num_embeddings= 3100,
                                                       embedding_dim= self.embedding_dim)
            in_feat += self.embedding_dim
        else:
            in_feat += 1

        if input_time and time_embedding:
            self.time_embedding_layer = th.nn.Embedding(num_embeddings= 3100,
                                                        embedding_dim= self.embedding_dim//2)
            in_feat += self.embedding_dim//2
        elif input_time:
            in_feat +=1

        if input_dirt and dirt_embedding:
            self.dirt_embedding_layer = th.nn.Embedding(num_embeddings=3,
                                                        embedding_dim= self.embedding_dim//4)
            in_feat += self.embedding_dim//4

        elif input_dirt:
            in_feat += 1

        self.input_dim = in_feat  ##最终输入到模型的向量的维度

        ##构建多头注意力
        ### inter-burst的transformer
         ## intra-burst attention blocks (处理burst内部)
        self.intra_transformer = self.build_multihead_blocks(
            input_dim=self.input_dim,  # <-- 使用最原始的输入维度
            head_nums=self.intra_head_nums,
            block_nums=self.intra_block_nums,
            dropout=self.dropout,
            proj_nums=self.intra_proj_nums,
            max_seq_len=self.intra_sequence_length,  # <-- 对应 burst_size
            rel_position=self.relative_position,
            abs_position=self.abs_position,
            evolving_attn=self.evolving_attn,
            evolving_attn_cnn_share=self.evolving_cnn_share,
            evolving_alpha=self.evolving_alpha,
            evolving_beta=self.evolving_beta,
            evolving_kernel_size=self.evolving_kernel_size
        )

        ## inter-burst attention blocks (处理burst之间的关系)
        self.inter_transformer = self.build_multihead_blocks(
            input_dim=self.intra_proj_nums * self.intra_sequence_length,  # <-- 使用 intra 模块的输出维度
            head_nums=self.inter_head_nums,
            block_nums=self.inter_block_nums,
            dropout=self.dropout,
            proj_nums=self.inter_proj_nums,
            max_seq_len=self.inter_sequence_length,  # <-- 对应 burst_num
            rel_position=self.relative_position,
            abs_position=self.abs_position,
            evolving_attn=self.evolving_attn,
            evolving_attn_cnn_share=self.evolving_cnn_share,
            evolving_alpha=self.evolving_alpha,
            evolving_beta=self.evolving_beta,
            evolving_kernel_size=self.evolving_kernel_size
        )

        ##构建分类器
        self.linear1 = nn.Linear(in_features= self.inter_sequence_length * self.inter_proj_nums, out_features= 1024, bias=True)
        self.activate1 =  nn.ReLU()
        self.linear2 = nn.Linear(in_features= 1024, out_features= nb_classes)

    def build_multihead_blocks(self,input_dim, head_nums, block_nums, dropout, proj_nums, max_seq_len, rel_position, abs_position, evolving_attn, evolving_attn_cnn_share, evolving_alpha, evolving_beta, evolving_kernel_size):
        return Transformer(input_dim=input_dim, head_nums=head_nums,block_nums=block_nums,dropout=dropout,proj_num=proj_nums,
                            max_seq_len=max_seq_len, rel_position= rel_position, abs_position= abs_position,
                            evolving_attn= evolving_attn, evolving_attn_cnn_share= evolving_attn_cnn_share, evolving_alpha= evolving_alpha, evolving_beta=evolving_beta, evolving_kernel_size=evolving_kernel_size)

        #输出结果是(S,N,E); S是序列长度,N是batchsize, E是embedding size.


    def forward(self, seqs, return_feature= False):
        ###seqs的形状：[batch_size, 3,  max_burst_num, max_burst_size]
        batch_size, feature_num, max_burst_num, max_burst_size = seqs.shape
        # 交换了以下两个assert，使其符合逻辑
        assert max_burst_num == self.inter_sequence_length
        assert max_burst_size == self.intra_sequence_length
        flow_len = seqs[:,0,:,:] # 长度特征应该是整数，而不是归一化后的小数
        flow_time = seqs[:,1,:,:]
        flow_time = torch.clamp(flow_time, min=0, max=1.0) # Clamp to prevent out-of-bounds on time_embedding
        flow_dirt = seqs[:,2,:,:]
        
        if self.len_embedding:
           len_embed = self.len_embedding_layer(flow_len.long() + Dataset_fgnet.MTU)
        else:
            len_embed = flow_len
        ##嵌入表达后, len_embed 形状: [batch_size, max_burst_num, max_burst_size, 128]
        if self.input_dirt:
            dirt_embed = self.dirt_embedding_layer(flow_dirt.long() + 1)
            ##嵌入表达后, dirt_embed 形状: [batch_size, max_burst_num, max_burst_size, 32]
        if self.input_time:
            time_embed = self.time_embedding_layer((flow_time//self.time_gransize).long())
            ##嵌入表达后, time_embed 形状: [batch_size, max_burst_num, max_burst_size, 64]

        ##合并三维特征
        if self.input_dirt and self.input_time:
            feature = th.cat([len_embed,time_embed,dirt_embed], dim=3)
            ##feature的形状: [batch_size, max_burst_num, max_burst_size, 224]
        elif self.input_dirt:
            feature = th.cat([len_embed, dirt_embed], dim=3)
            ##feature的形状: [batch_size, max_burst_num, max_burst_size, 128+32]
        elif self.input_time:
            feature = th.cat([len_embed, time_embed], dim=3)
            ##feature的形状: [batch_size, max_burst_num, max_burst_size, 128+64]
        else:
            feature = len_embed
        
        ## 修正并重构Transformer处理流程
        
        ## 1. Intra-burst attention (处理burst内部的packets)
        # 因为每个burst的处理是独立的, 所以可以把所有burst看成一个更大的batch来加速计算
        feature = feature.view(batch_size * max_burst_num, max_burst_size, self.input_dim)
        
        # intra_transformer的输入维度是self.input_dim, 与feature的最后一维匹配
        processed_bursts = self.intra_transformer(feature)
        # processed_bursts 的形状: [batch_size * max_burst_num, max_burst_size * self.intra_proj_nums]
        
        ## 2. Inter-burst attention (处理burst之间的关系)
        # 将处理后的burst恢复成流的序列
        feature_for_inter = processed_bursts.view(batch_size, max_burst_num, max_burst_size * self.intra_proj_nums)
        # inter_transformer的输入维度是 `max_burst_size * self.intra_proj_nums`, 与feature_for_inter最后一维匹配

        processed_flow = self.inter_transformer(feature_for_inter)
        # processed_flow 的形状: [batch_size, max_burst_num * self.inter_proj_nums]
        
        ##最后是分类
        # processed_flow的维度与分类器linear1的输入维度匹配
        feature = self.linear1(processed_flow)
        feature = self.activate1(feature)
        prob = self.linear2(feature)
        if return_feature == True:
           # feature_map在ttrain.py中被用于TripletLoss，对应修正后的总流特征processed_flow
           return prob, None, processed_flow
        return prob
