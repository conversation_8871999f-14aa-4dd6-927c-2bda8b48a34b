#模型的保存和加载
import torch
import json
from . import logger_wrappers
import os

def save(model, model_name, optimizer, checkpoint_path):
    if os.path.exists(checkpoint_path) == False:
        os.makedirs(checkpoint_path)
    # print(checkpoint_path)
    # print(model_name)
    path = checkpoint_path + model_name
    torch.save(model.state_dict(), path)
    #torch.save(
    #    {'state_dict':model.state_dict(),
    #     'optimizer':optimizer.state_dict()},
    #    (checkpoint_path+model_name).replace("//","/")
    #)
    info = "Dump model to {0} well.".format(path)
    logger_wrappers.warning(info)

def load(model, model_name, optimizer=None, checkpoint_path = 'saved_models/', use_gpu =True, device='cuda:0'):
    #print(device)
    print(checkpoint_path)
    print(model_name)
    path = checkpoint_path + model_name
    # path = (checkpoint_path+model_name).replace("//","/")
    if os.path.exists(path):
        if use_gpu == False:
            map_location= torch.device('cpu')
        else:
            device_id = 0
            if ":" in device:
                try:
                    device_id = int(device.split(":")[-1])
                except (ValueError, IndexError):
                    # 如果解析失败，则保留默认的 device_id=0
                    pass
            map_location = lambda storage, loc: storage.cuda(device_id)
        model_CKPT = torch.load(path, map_location=map_location)
        model.load_state_dict(model_CKPT)
        #model.load_state_dict(model_CKPT['state_dict'])
        #optimizer.load_state_dict(model_CKPT['optimizer'])
        info ="Load model from {0} well.".format(path)
        logger_wrappers.warning(info)
    else:
        logger_wrappers.warning('Load empty model from {0}.'.format(path))
    return model#,optimizer
