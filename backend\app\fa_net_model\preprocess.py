import numpy as np
from scapy.all import PcapReader
from collections import defaultdict
import warnings

# 忽略Scapy的警告
warnings.filterwarnings("ignore", category=UserWarning, module="scapy")

# 这些参数应该与原始项目中 'predict.py' 和 'ttrain.py' 的设置保持一致
# 修正: MAX_BURST_NUM 必须与模型加载时使用的 inter_sequence_length 一致
MAX_BURST_NUM = 10
MAX_BURST_SIZE = 20
MIN_FLOW_PACKETS = 10
MAX_FLOW_LEN = 200 # 对应 burst.py 中的 refine_packet_length 逻辑
MTU = 1500

def process_single_pcap(pcap_path):
    """
    在内存中完整地对单个pcap文件进行预处理。
    这个实现严格遵循了原始 fa_net/predict.py 中的多阶段预处理逻辑，
    包括两阶段截断、MTU分包、Burst分割和时间戳局部归一化。
    Returns:
        A tuple (status_code, data), where:
        - status_code 0: Success
        - status_code 1: Read error
        - status_code 2: No valid TCP/UDP flows found
        - status_code 3: All flows found were too short
        - data: The list of processed flows, or an empty list on failure.
    """
    flows = defaultdict(list)
    try:
        for packet in PcapReader(pcap_path):
            if 'IP' in packet and ('TCP' in packet or 'UDP' in packet):
                protocol = 'TCP' if 'TCP' in packet else 'UDP'
                if packet['IP'].src < packet['IP'].dst:
                    key = (packet['IP'].src, packet['IP'].dst, packet[protocol].sport, packet[protocol].dport, protocol)
                else:
                    key = (packet['IP'].dst, packet['IP'].src, packet[protocol].dport, packet[protocol].sport, protocol)
                flows[key].append(packet)
    except Exception as e:
        print(f"FA-Net-Error: 在用PcapReader读取pcap文件 {pcap_path} 时发生致命错误: {e}")
        return 1, []

    if not flows:
        return 2, []

    processed_flows = []
    for key, packets in flows.items():
        if len(packets) < MIN_FLOW_PACKETS:
            continue
        
        packets.sort(key=lambda p: p.time)
        client_ip = packets[0]['IP'].src
        
        all_lengths_signed = [len(p) if p['IP'].src == client_ip else -len(p) for p in packets]
        all_times = [0.0] + [float(packets[i].time - packets[i-1].time) for i in range(1, len(packets))]

        initial_lengths = all_lengths_signed[:MAX_FLOW_LEN]
        initial_times = all_times[:MAX_FLOW_LEN]

        refined_lengths_intermediate = []
        refined_times_intermediate = []
        for i in range(len(initial_lengths)):
            length, time = initial_lengths[i], initial_times[i]
            sign = np.sign(length)
            abs_len = abs(length)
            if abs_len <= MTU:
                refined_lengths_intermediate.append(length)
                refined_times_intermediate.append(time)
            else:
                num_full_packets = int(abs_len // MTU)
                refined_lengths_intermediate.extend([int(sign * MTU)] * num_full_packets)
                refined_times_intermediate.extend([time] * num_full_packets)
                remaining_len = abs_len % MTU
                if remaining_len > 0:
                    refined_lengths_intermediate.append(int(sign * remaining_len))
                    refined_times_intermediate.append(time)

        final_lengths = refined_lengths_intermediate[:MAX_FLOW_LEN]
        final_times = refined_times_intermediate[:MAX_FLOW_LEN]
        final_lengths.extend([0] * (MAX_FLOW_LEN - len(final_lengths)))
        final_times.extend([0.0] * (MAX_FLOW_LEN - len(final_times)))
        
        final_directions = [np.sign(l) for l in final_lengths]
        
        class _Burst:
            def __init__(self, start, end): self.start, self.end = start, end

        bursts = []
        if len(final_directions) > 0 and final_directions[0] != 0:
            bursts.append(_Burst(0, 1))
            for i in range(1, len(final_directions)):
                if final_directions[i] == 0: break
                if final_directions[i] == final_directions[bursts[-1].start]:
                    bursts[-1].end += 1
                else:
                    bursts.append(_Burst(i, i + 1))
        
        bursts_feature_list = []
        for burst in bursts:
            burst_len_slice = [final_lengths[i] for i in range(burst.start, burst.end)]
            original_burst_times = [final_times[i] for i in range(burst.start, burst.end)]
            first_time_in_burst = original_burst_times[0] if original_burst_times else 0.0
            
            burst_time_slice = [min((t - first_time_in_burst) + 1.0, 10.0) for t in original_burst_times]
            burst_dir_slice = [final_directions[i] for i in range(burst.start, burst.end)]

            padded_lengths = (burst_len_slice + [0] * MAX_BURST_SIZE)[:MAX_BURST_SIZE]
            padded_times = (burst_time_slice + [0.0] * MAX_BURST_SIZE)[:MAX_BURST_SIZE]
            padded_directions = (burst_dir_slice + [0] * MAX_BURST_SIZE)[:MAX_BURST_SIZE]
            
            burst_features = np.array([padded_lengths, padded_times, padded_directions], dtype=np.float32).T
            bursts_feature_list.append(burst_features)

        if len(bursts_feature_list) > MAX_BURST_NUM:
            bursts_feature_list = bursts_feature_list[:MAX_BURST_NUM]
        else:
            padding_burst = np.zeros((MAX_BURST_SIZE, 3), dtype=np.float32)
            bursts_feature_list.extend([padding_burst] * (MAX_BURST_NUM - len(bursts_feature_list)))

        processed_flows.append(np.array(bursts_feature_list))

    if not processed_flows:
        return 3, []
        
    return 0, processed_flows 