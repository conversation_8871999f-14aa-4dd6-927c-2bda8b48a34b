from fastapi import APIRouter, UploadFile, File, HTTPException
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import torch
import json
import numpy as np
import os

# 确保能正确导入同级目录下的模块
from .model import FSNet
from .inference import PAD_KEY, START_KEY, END_KEY

router = APIRouter()

# --- 模型和设备配置 (与 inference.py 保持一致) ---
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
CONFIG = {
    "class_num": 8,
    "max_packet_length": 1500,
    "length_dim": 16,
    "hidden_size": 128,
    "num_layers": 2,
    "dropout_prob": 0.0,
    "chunk_size": 350, # 评估时使用固定的长度，与训练时一致
}
# 注意: 模型路径是相对于 FastAPI 运行根目录的相对路径
MODEL_PATH = "app/fs_net_model/fsnet_pytorch_final.pth"

# 这是一个示例，实际名称需要根据数据集的定义来确定
CLASS_NAMES = [f"Class_{i}" for i in range(CONFIG["class_num"])]


def load_fsnet_model(model_path):
    """加载 FS-Net 模型"""
    if not os.path.exists(model_path):
        raise HTTPException(status_code=500, detail=f"模型文件未找到: {model_path}")

    model = FSNet(
        class_num=CONFIG["class_num"],
        max_packet_val=CONFIG["max_packet_length"],
        length_dim=CONFIG["length_dim"],
        hidden_size=CONFIG["hidden_size"],
        num_layers=CONFIG["num_layers"],
        dropout_prob=CONFIG["dropout_prob"]
    ).to(DEVICE)
    try:
        model.load_state_dict(torch.load(model_path, map_location=DEVICE))
        model.eval()
        return model
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型加载失败: {e}")

def process_and_predict_from_json(model, data):
    """
    从解析后的 JSON 数据中处理并预测样本。
    """
    all_predictions = []
    all_labels = []

    for sample in data:
        flow_sequence = sample.get('flow', [])
        label = sample.get('label')

        if not isinstance(flow_sequence, list) or label is None or not flow_sequence:
            continue

        clipped_flow = [max(0, min(p, CONFIG["max_packet_length"])) for p in flow_sequence]
        
        if len(clipped_flow) > CONFIG["chunk_size"]:
            clipped_flow = clipped_flow[:CONFIG["chunk_size"]]
        
        processed_sequence = [START_KEY] + clipped_flow + [END_KEY]
        seq_len = len(processed_sequence)

        target_len = CONFIG["chunk_size"] + 2
        padding_needed = target_len - seq_len
        if padding_needed > 0:
            processed_sequence.extend([PAD_KEY] * padding_needed)
        
        flow_tensor = torch.LongTensor([processed_sequence]).to(DEVICE)
        seq_len_tensor = torch.LongTensor([seq_len])

        with torch.no_grad():
            outputs = model(flow_tensor, seq_len_tensor)
            _, predicted_class_idx = torch.max(outputs, 1)
            all_predictions.append(predicted_class_idx.item())
            all_labels.append(label)

    return all_predictions, all_labels


@router.post("/evaluate/fsnet_json")
async def evaluate_fsnet_from_json(json_file: UploadFile = File(...)):
    """
    从上传的 JSON 文件评估 FS-Net 模型。
    这是一个同步执行的端点。
    """
    try:
        contents = await json_file.read()
        data = json.loads(contents)
    except Exception:
        raise HTTPException(status_code=400, detail="无效或损坏的 JSON 文件。")

    if not isinstance(data, list):
        raise HTTPException(status_code=400, detail="JSON 文件内容必须是一个对象列表。")

    model = load_fsnet_model(MODEL_PATH)
    
    predictions, true_labels = process_and_predict_from_json(model, data)

    if not predictions:
        raise HTTPException(status_code=400, detail="在 JSON 文件中没有找到有效的样本进行评估。")

    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, predictions, average='weighted', zero_division=0)
    cm = confusion_matrix(true_labels, predictions, labels=list(range(CONFIG["class_num"])))

    return {
        "accuracy": accuracy,
        "precision": precision,
        "recall": recall,
        "f1_score": f1,
        "confusion_matrix": cm.tolist(),
        "class_names": CLASS_NAMES,
        "total_samples": len(predictions)
    } 