from collections import Counter
import numpy as np
import torch
import os

# 本地依赖: 从同一目录下的文件中导入
from .model import FSNet
from .scapy_extractor import extract_packet_length_sequence

# 从原项目中借用这几个常量
PAD_KEY = 0
START_KEY = 1
END_KEY = 2

def process_and_predict_chunks(pcap_lengths, model, device, max_packet_length, length_block, chunk_size, step_size):
    """
    接收一个pcap文件的完整包长序列, 应用分块和处理流程, 并返回一个包含所有块预测结果的列表。
    此函数逻辑完全基于 FS-Net/predict_pcap.py。
    """
    # 步骤 1: 第一次转换 (裁剪, 变换), 与 pytorch_prepro.py 逻辑一致
    flow_data = [min(p, max_packet_length) for p in pcap_lengths]
    flow_data = [l // length_block + 3 for l in flow_data]

    # 步骤 2: 对序列进行分块 (Chunking)
    chunks = []
    if len(flow_data) > chunk_size:
        for i in range(0, len(flow_data) - chunk_size + 1, step_size):
            chunks.append(flow_data[i:i + chunk_size])
    else:
        chunks.append(flow_data)

    all_predictions = [] # 存储每个块的 (class_idx, confidence)
    for chunk in chunks:
        # 对每个块应用与 dataset.py 一致的处理流程
        # 步骤 3: 添加 START/END 标记
        processed_sequence = [START_KEY] + chunk + [END_KEY]

        # 步骤 4: 填充
        seq_len = len(processed_sequence)
        target_len = chunk_size + 2 # 目标长度现在是块大小+2
        if seq_len < target_len:
            processed_sequence.extend([PAD_KEY] * (target_len - seq_len))
        
        # 步骤 5: 转换为Tensor并预测
        flow_tensor = torch.LongTensor([processed_sequence]).to(device)
        seq_len_tensor = torch.LongTensor([seq_len])
        with torch.no_grad():
            outputs = model(flow_tensor, seq_len_tensor)
            probabilities = torch.nn.functional.softmax(outputs, dim=1)
            confidence, predicted_class_idx_tensor = torch.max(probabilities, 1)
            all_predictions.append((predicted_class_idx_tensor.item(), confidence.item()))
            
    return all_predictions

def predict(pcap_path, model_path="app/fs_net_model/fsnet_pytorch_final.pth"):
    """
    使用FS-Net模型对PCAP文件进行预测。
    此版本现在实现了与原始FS-Net项目 predict_pcap.py 一致的 "单一序列+分块投票" 逻辑。
    """
    # --- 模型和设备配置 (与原FS-Net项目训练/预测参数保持一致) ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 这里的参数现在完全匹配 FS-Net/predict_pcap.py 和相关训练脚本
    config = {
        "class_num": 8,
        # 注意: 根据 'final_diagnosis.py' 和 'preprocess.py' 的线索,
        # 原始模型训练时很可能使用了5000。
        # 经用户确认, 此处应遵循最新脚本, 使用 1500。
        "max_packet_length": 1500, 
        "length_dim": 16,
        "hidden_size": 128,
        "num_layers": 2,
        "dropout_prob": 0.0, # 预测时通常不使用dropout
        "length_block": 1,
        "chunk_size": 350,
        "step_size": 175
    }

    # 1. 检查模型文件是否存在
    if not os.path.exists(model_path):
        return {"error": f"Model file not found at {model_path}"}
        
    # 2. 初始化模型
    model = FSNet(
        class_num=config["class_num"],
        max_packet_val=config["max_packet_length"],
        length_dim=config["length_dim"],
        hidden_size=config["hidden_size"],
        num_layers=config["num_layers"],
        dropout_prob=config["dropout_prob"]
    ).to(device)
    
    # 3. 加载模型权重
    try:
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
    except Exception as e:
        return {"error": f"Error loading model state_dict: {e}"}

    
    # 4. 从pcap中提取完整的、单一的包长序列
    try:
        pcap_lengths = extract_packet_length_sequence(pcap_path)
    except Exception as e:
        return {"error": f"Error extracting packet sequence with scapy: {e}"}
        
    if not pcap_lengths:
        return {"status": "No valid IP packets found in the pcap file."}

    # 5. 对序列进行分块、预测并收集结果
    all_predictions = process_and_predict_chunks(
        pcap_lengths, model, device,
        max_packet_length=config["max_packet_length"],
        length_block=config["length_block"],
        chunk_size=config["chunk_size"],
        step_size=config["step_size"]
    )

    if not all_predictions:
        return {"status": "Failed to get any valid predictions from the pcap's chunks."}

    # 6. 投票聚合逻辑
    vote_counts = Counter(p[0] for p in all_predictions)
    final_prediction_idx, top_vote_count = vote_counts.most_common(1)[0]
    
    # 计算获胜类别的平均置信度
    winning_confidences = [p[1] for p in all_predictions if p[0] == final_prediction_idx]
    average_confidence = sum(winning_confidences) / len(winning_confidences) if winning_confidences else 0.0

    # 7. 返回结构化的最终结果
    return {
        "status": "success",
        "final_prediction": {
            "class_idx": final_prediction_idx,
            "confidence": average_confidence
        },
        "details": {
            "total_chunks_processed": len(all_predictions),
            "vote_counts": dict(vote_counts)
        }
    }

if __name__ == '__main__':
    # 提供一个示例pcap文件路径进行测试
    # 注意:你需要提供一个实际存在的pcap文件路径
    test_pcap_path = 'path/to/your/test.pcap' 
    if os.path.exists(test_pcap_path):
        final_result = predict(test_pcap_path)
        print("Final Prediction Result:")
        if isinstance(final_result, dict) and final_result.get("status") == "success":
            final_prediction = final_result.get("final_prediction")
            details = final_result.get("details")

            if isinstance(final_prediction, dict) and isinstance(details, dict):
                class_idx = final_prediction.get("class_idx")
                confidence = final_prediction.get("confidence", 0.0)
                vote_counts = details.get("vote_counts")
                total_chunks = details.get("total_chunks_processed")

                print(f"  Predicted Class Index: {class_idx}")
                print(f"  Average Confidence: {confidence:.4f}")
                print(f"  Total Chunks Processed: {total_chunks}")
                print(f"  Vote Details: {vote_counts}")
            else:
                print(f"  Prediction failed: Malformed success response from model.")

        elif isinstance(final_result, dict):
            print(f"  Prediction failed: {final_result.get('error') or final_result.get('status')}")
        else:
            print(f"  Prediction failed: Invalid response type from predict function: {type(final_result)}")

    else:
        print(f"Test file not found: {test_pcap_path}. Please provide a valid pcap file for testing.")
