# fsnet_model_pytorch.py

import torch
import torch.nn as nn
from torch.nn.utils.rnn import pack_padded_sequence, pad_packed_sequence

class FSNet(nn.Module):
    def __init__(self, class_num, max_packet_val, length_dim, hidden_size, num_layers, dropout_prob):
        """
        FS-Net 模型的 PyTorch 实现
        :param class_num: 类别数量
        :param max_packet_val: 数据包长度的最大值 (用于确定词汇表大小)
        :param length_dim: 包长嵌入的维度
        :param hidden_size: GRU 的隐藏层大小
        :param num_layers: GRU 的层数
        :param dropout_prob: Dropout 的概率
        """
        super().__init__()
        
        # 词汇表大小，+4的逻辑来自原项目的 config.length_num
        vocab_size = max_packet_val + 4

        # 1. 嵌入层
        self.embedding = nn.Embedding(
            num_embeddings=vocab_size,
            embedding_dim=length_dim,
            padding_idx=0 # PAD_KEY
        )

        # 2. 编码器 (双向 GRU)
        self.encoder = nn.GRU(
            input_size=length_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True, # PyTorch中常用的数据格式 (batch, seq_len, features)
            dropout=dropout_prob if num_layers > 1 else 0
        )
        
        # 3. 解码器 (双向 GRU)
        self.decoder = nn.GRU(
            input_size=hidden_size * 2, # 输入是编码器特征
            hidden_size=hidden_size,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout_prob if num_layers > 1 else 0
        )

        # 4. 特征融合层 (Fusion) - 对应原项目的 _fusion 函数
        # 输入维度是 e_fea(2*h) + d_fea(2*h) + e_fea*d_fea(2*h)
        fusion_input_dim = hidden_size * 2 * 3
        self.fusion_gate = nn.Linear(fusion_input_dim, hidden_size * 2)
        self.fusion_update = nn.Linear(fusion_input_dim, hidden_size * 2)

        # 5. 最终分类器 - 对应原项目的 _compress 和 _classify
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size * 2),
            nn.SELU(), # 原项目使用的激活函数
            nn.Dropout(dropout_prob),
            nn.Linear(hidden_size * 2, class_num)
        )
        
    def forward(self, flow_seq, seq_lens):
        # flow_seq: (batch_size, max_len+2)
        # seq_lens: (batch_size,)
        
        # 1. 嵌入
        # -> (batch_size, max_len+2, length_dim)
        embedded_seq = self.embedding(flow_seq)
        
        # 打包变长序列，提高RNN效率
        packed_input = pack_padded_sequence(embedded_seq, seq_lens.cpu(), batch_first=True, enforce_sorted=False)

        # 2. 编码器
        # e_outputs 是每个时间步的输出, e_last_hidden 是最后一个时间步的隐状态
        packed_e_outputs, e_last_hidden = self.encoder(packed_input)
        
        # e_last_hidden 的形状是 (num_layers*2, batch_size, hidden_size)
        # 我们需要最后-层的 [前向, 后向] 拼接作为特征
        # -> (batch_size, hidden_size*2)
        e_feature = torch.cat((e_last_hidden[-2,:,:], e_last_hidden[-1,:,:]), dim=1)

        # 3. 解码器
        # 解码器的输入是编码器特征在时间维度上的平铺
        # -> (batch_size, max_len+2, hidden_size*2)
        decoder_input = e_feature.unsqueeze(1).repeat(1, flow_seq.size(1), 1)
        packed_d_input = pack_padded_sequence(decoder_input, seq_lens.cpu(), batch_first=True, enforce_sorted=False)
        
        packed_d_outputs, d_last_hidden = self.decoder(packed_d_input)
        d_feature = torch.cat((d_last_hidden[-2,:,:], d_last_hidden[-1,:,:]), dim=1)
        
        # 4. 特征融合
        # -> (batch_size, hidden_size*6)
        fusion_input = torch.cat([e_feature, d_feature, e_feature * d_feature], dim=1)
        
        gate = torch.sigmoid(self.fusion_gate(fusion_input))
        update = torch.tanh(self.fusion_update(fusion_input))
        
        # -> (batch_size, hidden_size*2)
        fused_feature = e_feature * gate + (1 - gate) * update

        # 5. 分类
        # -> (batch_size, class_num)
        logits = self.classifier(fused_feature)
        
        return logits