from scapy.all import rdpcap, TCP, UDP, IP
from collections import defaultdict
import os

class Flow:
    """用于存储单个流信息的简单类。"""
    def __init__(self):
        self.ip_lengths = []
        self.ip_timestamps = []

    def add_packet(self, pkt):
        """向流中添加一个数据包的信息。"""
        # scapy中, pkt.time是时间戳, len(pkt)是IP包的总长度
        self.ip_timestamps.append(float(pkt.time))
        self.ip_lengths.append(len(pkt))

def get_flow_key(pkt):
    """
    根据数据包的五元组（源IP, 目的IP, 协议, 源端口, 目的端口）生成一个唯一的、方向无关的键。
    """
    if IP in pkt:
        src_ip = pkt[IP].src
        dst_ip = pkt[IP].dst
        proto = pkt[IP].proto
        src_port, dst_port = None, None
        
        if TCP in pkt:
            src_port = pkt[TCP].sport
            dst_port = pkt[TCP].dport
        elif UDP in pkt:
            src_port = pkt[UDP].sport
            dst_port = pkt[UDP].dport
        
        # 通过排序确保(A->B)和(B->A)的包属于同一个流
        if (src_ip, src_port) > (dst_ip, dst_port):
            return (dst_ip, src_ip, proto, dst_port, src_port)
        else:
            return (src_ip, dst_ip, proto, src_port, dst_port)
    return None

def extract(pcap_file, verbose=True):
    """
    使用 Scapy 从 pcap 文件中提取所有独立的流。
    这个函数模仿 flowcontainer.extractor.extract 的功能和输出结构。
    :param pcap_file: pcap文件的路径。
    :param verbose: 是否打印错误信息。
    :return: 一个字典，键是流的标识符，值是包含该流信息的Flow对象。
    """
    flows = defaultdict(Flow)
    try:
        # 使用 scapy 的 rdpcap 读取pcap文件
        packets = rdpcap(pcap_file)
    except Exception as e:
        if verbose:
            pcap_filename = os.path.basename(pcap_file)
            print(f"\n[错误] 使用scapy读取文件 {pcap_filename} 时出错: {e}")
        return {}

    for pkt in packets:
        # 只处理IP数据包
        if IP not in pkt:
            continue
            
        flow_key = get_flow_key(pkt)
        if flow_key:
            flows[flow_key].add_packet(pkt)
            
    # rdpcap读取的数据包已按时间戳排序，所以流内的数据包也是有序的。
    # 将 defaultdict 转换为普通 dict 并返回。键转换为字符串以保持兼容性。
    return {str(key): flow for key, flow in flows.items()}


def extract_packet_length_sequence(pcap_file):
    """
    使用 Scapy 从 pcap 文件中提取一个单一的、按时间排序的IP包长序列。
    这个函数用于数据生成阶段 (generate_fsnet_data.py)。
    :param pcap_file: pcap文件的路径。
    :return: 一个包含所有IP包长度的列表。
    """
    try:
        packets = rdpcap(pcap_file)
        # rdpcap返回的包列表已按时间戳排序，我们只需提取IP包的长度
        length_sequence = [len(pkt) for pkt in packets if IP in pkt]
        return length_sequence
    except Exception as e:
        pcap_filename = os.path.basename(pcap_file)
        print(f"\n[错误] 使用scapy处理文件 {pcap_filename} 时出错: {e}")
        return [] 