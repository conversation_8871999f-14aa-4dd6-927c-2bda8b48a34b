import torch
import torch.nn as nn
from scapy.all import rdpcap, PcapReader
import numpy as np
import os
from .df_model.model import DFNet_Pytorch
# 从 df_model 中导入正确的特征提取函数
from .df_model.flow_extract import extract_direction_sequence_for_df
import pickle
import joblib  # 引入 joblib
import pandas as pd
from .appscanner.preprocessor import Preprocessor
from sklearn.ensemble import RandomForestClassifier
import random
from .fa_net_model.model import TF_classifier
from .fa_net_model.burst import Dataset_fgnet
from .fa_net_model.model_seriealization import load
from .fa_net_model.preprocess import process_single_pcap # 导入新的预处理函数
from .fs_net_model.inference import predict as fs_net_predict
from .yatc_model.inference import predict_yatc # <--- 导入YaTC的预测函数
from .lexnet_model.api import predict_from_pcap as lexnet_predict_func, load_model as load_lexnet_model # <--- 导入 LexNet 的预测函数
from collections import Counter
from typing import Dict, Any

# --- 按需加载，不再使用全局缓存 ---

# --- 关键路径和参数配置 ---
# 使用相对路径，确保在容器内也能正确找到文件
_FA_NET_BASE_DIR = os.path.dirname(os.path.abspath(__file__))
_FA_NET_MODEL_DIR = os.path.join(_FA_NET_BASE_DIR, "models", "fa_net")
_FA_NET_MODEL_FILE = "tunnel_behavior_fs.pkl"
_FA_NET_DUMP_FILE = "my_8class_dataset.gzip"
_FA_NET_CHECKPOINT_PATH = _FA_NET_MODEL_DIR + "/" # load函数需要一个目录路径

# 模型和数据的固定参数
_FA_NET_MAX_BURST_SIZE = 20
_FA_NET_MAX_BURST_NUM = 10 # 修正: 根据模型权重文件反推得出，正确的burst数量应为10
_FA_NET_NUM_CLASSES = 8

# 为 DF-Master 创建类别映射 (根据用户提供的权威顺序)
DF_CLASS_MAPPING = {
    0: "勒索软件",
    1: "恶意文件",
    2: "扫描探测",
    3: "木马流量",
    4: "致瘫攻击",
    5: "良性",
    6: "隐蔽传输",
    7: "高危漏洞"
}

# 为 FA-Net 创建类别映射 (根据用户提供的权威顺序)
FA_NET_CLASS_MAPPING = {
    0: "勒索软件",
    1: "恶意文件",
    2: "扫描探测",
    3: "木马流量",
    4: "致瘫攻击",
    5: "良性",
    6: "隐蔽传输",
    7: "高危漏洞"
}

# 为 FS-Net 创建类别映射 
FS_NET_CLASS_MAPPING = {
    0: "勒索软件",
    1: "恶意文件",
    2: "扫描探测",
    3: "木马流量",
    4: "致瘫攻击",
    5: "良性",
    6: "隐蔽传输",
    7: "高危漏洞"
}

# 为YaTC创建类别映射 (根据yatc_model/inference.py)
YATC_CLASS_MAPPING = {
    '良性': '良性', 
    '勒索软件': '勒索软件', 
    '恶意文件': '恶意文件', 
    '扫描探测': '扫描探测', 
    '木马流量': '木马流量', 
    '致瘫攻击': '致瘫攻击', 
    '隐蔽传输': '隐蔽传输', 
    '高危漏洞': '高危漏洞'
}

# 为 LexNet 创建类别映射 (根据 lexnet_refactored/config/config.yml)
LEXNET_CLASS_MAPPING = {
    '勒索软件': '勒索软件',
    '恶意文件': '恶意文件',
    '扫描探测': '扫描探测',
    '木马流量': '木马流量',
    '致瘫攻击': '致瘫攻击',
    '良性': '良性',
    '隐蔽传输': '隐蔽传输',
    '高危漏洞': '高危漏洞'
}


def _load_df_model():
    """按需加载 DF-Master 模型并返回。"""
    print("按需加载DF模型...")
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    # 修正路径，指向 df_model 目录下的 model_weights.pth
    model_path = os.path.join(os.path.dirname(__file__), "df_model", "model_weights.pth")
    model = DFNet_Pytorch(input_shape=(5000, 1), num_classes=8)
    
    if torch.cuda.is_available():
        model.load_state_dict(torch.load(model_path))
    else:
        model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
        
    model.to(device)
    model.eval()
    print(f"DF模型加载成功，运行在 {device}。")
    return model, device

def predict_df(filepath: str):
    """
    使用 DF-Master 模型对 pcap 文件进行流量分类预测。
    """
    model, device = _load_df_model()

    try:
        # 使用正确的特征提取函数，从pcap生成方向序列
        sizes = extract_direction_sequence_for_df(filepath, target_length=5000)

        # 如果特征提取失败（例如，pcap为空或无IP流量），则返回错误
        if not sizes:
            return {
                "model": "DF-Master",
                "status": "失败",
                "error": "未能从pcap文件中提取有效的IP流量方向序列。"
            }

        input_tensor = torch.tensor(sizes, dtype=torch.float32).unsqueeze(0).unsqueeze(-1).to(device)
        
        with torch.no_grad():
            outputs = model(input_tensor)
            probabilities = nn.functional.softmax(outputs, dim=1)
            confidence, predicted_class_idx_tensor = torch.max(probabilities, 1)
            
            predicted_idx = predicted_class_idx_tensor.item()
            prediction_name = DF_CLASS_MAPPING.get(predicted_idx, f"未知类别-{predicted_idx}")

        # 准备图表数据: 将方向序列转换为图表库所需的格式
        # features 变量名来自最初的函数调用，这里代表方向序列
        # 为了避免前端卡顿，如果序列太长，可以考虑进行采样，这里我们返回前500个点
        chart_data = [
            {'index': i, 'direction': int(d)} for i, d in enumerate(sizes) if i < 500
        ]

        return {
            "model": "DF-Master",
            "status": "成功",
            "prediction": prediction_name,
            "confidence": float(confidence.item()),
            "details": {"chart_data": chart_data}
        }
    except ValueError as ve:
        return {
            "model": "DF-Master",
            "status": "失败",
            "error": f"处理pcap文件时scapy内部发生错误: {ve}"
        }
    except Exception as e:
        return {
            "model": "DF-Master",
            "status": "失败",
            "error": str(e)
        }
    finally:
        # 确保模型和相关缓存被清理
        del model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def _load_appscanner_model():
    """按需加载 AppScanner 模型并返回。"""
    print("按需加载AppScanner模型...")
    model_path = os.path.join(os.path.dirname(__file__), "appscanner", "appscanner_model.pkl")
    model = joblib.load(model_path)
    print("AppScanner模型加载成功。")
    return model

def predict_appscanner(filepath: str):
    """
    使用 AppScanner 对 pcap 文件进行流量分类预测。
    """
    model = _load_appscanner_model()
    
    try:
        preprocessor = Preprocessor()
        features_dict = preprocessor.extract(filepath)

        if not features_dict:
            return {
                "model": "AppScanner",
                "status": "成功",
                "prediction": "pcap文件中无有效TCP/UDP flow",
                "confidence": 1.0
            }

        features_df = pd.DataFrame.from_dict(features_dict, orient='index')
        predictions = model.predict(features_df)
        prediction = Counter(predictions).most_common(1)[0][0] # 取最常见的预测结果
        probabilities = model.predict_proba(features_df)
        confidence = probabilities.max()

        # 提取并格式化流量详情以用于JSON响应
        flow_details = []
        # 将字典的键（元组）和值（numpy数组）转换为可处理的列表
        flows = list(features_dict.keys())
        # 根据每个 flow 的数据包/特征数量（这里用特征数组的长度近似）进行排序
        flows.sort(key=lambda flow: len(features_dict[flow]), reverse=True)
        
        # 只取前5个最主要的 flow
        for flow_key in flows[:5]:
            # flow_key: (protocol, src, sport, dst, dport)
            flow_details.append({
                'protocol': flow_key[0],
                'source_ip': flow_key[1],
                'source_port': flow_key[2],
                'dest_ip': flow_key[3],
                'dest_port': flow_key[4],
                'feature_count': len(features_dict[flow_key]) # 代表这个流的活跃度
            })

        return {
            "model": "AppScanner",
            "status": "成功",
            "prediction": prediction,
            "confidence": float(confidence),
            "details": flow_details # <--- 在这里添加详情
        }
    except Exception as e:
        return {
            "model": "AppScanner",
            "status": "失败",
            "error": str(e)
        }
    finally:
        del model

# def predict_yatc(filepath: str):
#     """
#     使用 YaTC 模型对 pcap 文件进行流量分类预测。
#     """
#     print("使用YaTC模型进行预测...")
#     try:
#         result = yatc_predict(filepath)
#         if "error" in result:
#             return {
#                 "model": "YaTC",
#                 "status": "失败",
#                 "error": result["error"]
#             }
        
#         return {
#             "model": "YaTC",
#             "status": "成功",
#             "prediction": result["prediction"],
#             "confidence": float(result["confidence"])
#         }
#     except Exception as e:
#         import traceback
#         return {
#             "model": "YaTC",
#             "status": "失败",
#             "error": f"YaTC模型预测时发生未知错误: {str(e)}\n{traceback.format_exc()}"
#         }

def predict_lexnet(filepath: str):
    """
    使用 LexNet 模型对 pcap 文件进行流量分类预测。
    """
    print("使用LexNet模型进行预测...")
    try:
        # 确保模型已加载
        load_lexnet_model()
        
        result = lexnet_predict_func(filepath)
        
        if "error" in result:
            return {
                "model": "LexNet",
                "status": "失败",
                "error": result["error"]
            }

        # 转换预测结果格式以匹配前端期望
        # dominant_class 作为主要预测
        # predictions 列表作为详情
        dominant_class = result.get("dominant_class", "未知")
        
        # 计算置信度：这里我们使用主要类别的流量百分比作为置信度
        confidence = 0.0
        if dominant_class != "未知" and "predictions" in result:
            for pred_item in result["predictions"]:
                if pred_item.get("class") == dominant_class:
                    confidence = float(pred_item.get("percentage", 0.0)) / 100.0
                    break

        return {
            "model": "LexNet",
            "status": "成功",
            "prediction": dominant_class,
            "confidence": float(confidence),
            "details": result.get("predictions", [])
        }
    except Exception as e:
        # 在日志中记录完整的异常信息，以便调试
        import traceback
        print(f"LexNet 预测失败: {traceback.format_exc()}")
        return {
            "model": "LexNet",
            "status": "失败",
            "error": str(e)
        }


def _initialize_fa_net_model():
    """按需加载FA-Net模型和类别名并返回。"""
    print("按需加载FA-Net模型...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu") # 确定设备
    try:
        # 1. 直接从我们硬编码的映射中获取类别名称
        class_names = [FA_NET_CLASS_MAPPING[i] for i in range(_FA_NET_NUM_CLASSES)]
        print(f"FA-Net: 类别名称: {class_names}")

        # 2. 初始化模型结构 (修正参数以匹配独立项目和api.py)
        model = TF_classifier(
            nb_classes=_FA_NET_NUM_CLASSES,
            inter_head_nums=8,
            inter_block_nums=3,
            inter_proj_nums=32,
            inter_sequence_length=_FA_NET_MAX_BURST_NUM,    # 修正: inter 对应 burst 数量
            intra_head_nums=8,
            intra_block_nums=3,
            intra_proj_nums=32,
            intra_sequence_length=_FA_NET_MAX_BURST_SIZE, # 修正: intra 对应 burst 内包数量
            embedding_dim=128,                          # 新增: 匹配模型定义
            dropout=0.3,
            input_time=True,
            input_dirt=True,
            # 新增: 开启位置编码以匹配权重文件中的keys
            len_embedding=True,
            time_embedding=True,
            dirt_embedding=True,
            relative_position=True,
            abs_position=True,
            evolving_attn=True,
            evolving_alpha=0.5,
            evolving_beta=0.4,
            evolving_kernel_size=3,
            evolving_cnn_share=False
        ).to(device)

        # 3. 加载模型权重 (切换为torch.load，并处理 'module.' 前缀)
        load_path = os.path.join(_FA_NET_MODEL_DIR, _FA_NET_MODEL_FILE)
        print(f"FA-Net: 正在从 '{load_path}' 加载模型权重...")
        
        state_dict = torch.load(load_path, map_location=device)
        # 处理可能的 DataParallel 'module.' 前缀
        if list(state_dict.keys())[0].startswith('module.'):
            state_dict = {k[len('module.'):]: v for k, v in state_dict.items()}

        model.load_state_dict(state_dict)
        model.eval()
        print("FA-Net: 模型加载成功并已设置为评估模式。")
        
        # 返回模型、类别名和设备
        return model, class_names, device

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"FA-Net 模型加载过程中发生致命错误: {e}\n{error_details}")
        return None, None, None

def predict_fa_net(filepath: str):
    """
    使用FA-Net模型对PCAP文件进行预测。
    """
    model, class_names, device = _initialize_fa_net_model()
    
    if not model:
        return {
            "model": "FA-Net",
            "status": "失败",
            "error": "FA-Net模型或类别信息未能成功加载，无法进行预测。",
        }
    
    status_code, processed_flows = process_single_pcap(filepath)
    
    if status_code != 0:
        error_message = f"未知预处理错误 (状态码: {status_code})。"
        if status_code == 1:
            error_message = "读取pcap文件时发生内部错误，文件可能已损坏。"
        elif status_code == 2:
            error_message = "pcap文件中未找到任何有效的TCP或UDP流。"
        elif status_code == 3:
            from .fa_net_model.preprocess import MIN_FLOW_PACKETS
            error_message = f"找到了流，但所有流都因数据包过少 (少于{MIN_FLOW_PACKETS}个) 而被忽略。"
        
        return {
            "model": "FA-Net",
            "status": "失败",
            "error": error_message
        }
    
    # 对返回的每一个流进行预测并返回第一个成功的结果
    for flow_features in processed_flows:
        try:
            input_tensor = torch.tensor(flow_features, dtype=torch.float32).unsqueeze(0).to(device)
            input_tensor = input_tensor.permute(0, 3, 1, 2)

            with torch.no_grad():
                outputs = model(input_tensor)
                probabilities = nn.functional.softmax(outputs, dim=1)
                confidence, predicted_class_idx_tensor = torch.max(probabilities, 1)
                
                predicted_idx = predicted_class_idx_tensor.item()
                prediction_name = FA_NET_CLASS_MAPPING.get(predicted_idx, f"未知类别-{predicted_idx}")

            # 只要有一个流成功预测，就立即返回结果
            return {
                "model": "FA-Net",
                "status": "成功",
                "prediction": prediction_name,
                "confidence": float(confidence.item())
            }
        except Exception as e:
            # 如果对当前流的预测失败，记录错误并继续尝试下一个流
            import traceback
            print(f"FA-Net 在预测单个流时捕获到异常: {e}")
            print(traceback.format_exc())
            continue
    
    # 如果所有流都尝试失败了
    return {
        "model": "FA-Net",
        "status": "失败",
        "error": "预处理成功并找到了有效流量，但模型在对所有这些流进行预测时都失败了。"
    }

def predict_fs_net(filepath: str):
    """
    使用 FS-Net 模型对 pcap 文件中的所有流进行分类预测。
    """
    try:
        # 调用 FS-Net 的预测函数
        predictions = fs_net_predict(filepath)

        # 检查是否有错误或未找到流
        if "error" in predictions:
            return {
                "model": "FS-Net",
                "status": "失败",
                "error": predictions["error"]
            }
        if "status" in predictions:
            # 这种情况表示pcap有效但无流量，我们视为成功
             return {
                "model": "FS-Net",
                "status": "成功",
                "prediction": "pcap文件中无有效flow",
                "confidence": 1.0,
            }
        
        # 聚合逻辑：多数投票
        if not predictions:
            return {
                "model": "FS-Net",
                "status": "成功",
                "prediction": "无流量可分析",
                "confidence": 1.0
            }

        # 1. 统计每个类别的票数
        class_votes = Counter(result['class_idx'] for result in predictions.values())
        
        # 2. 找到得票最多的类别
        if not class_votes:
             return {
                "model": "FS-Net",
                "status": "成功",
                "prediction": "分析完成但无明确分类结果",
                "confidence": 0.0
            }
        
        most_common_class_idx, _ = class_votes.most_common(1)[0]
        
        # 3. 计算"冠军类别"的平均置信度
        confidences_for_winner = [
            result['confidence'] 
            for result in predictions.values() 
            if result['class_idx'] == most_common_class_idx
        ]
        average_confidence = sum(confidences_for_winner) / len(confidences_for_winner)
        
        # 4. 获取类别名称
        prediction_name = FS_NET_CLASS_MAPPING.get(most_common_class_idx, f"未知类别-{most_common_class_idx}")

        return {
            "model": "FS-Net",
            "status": "成功",
            "prediction": prediction_name,
            "confidence": average_confidence
        }
    except ImportError as ie:
         return {
            "model": "FS-Net",
            "status": "失败",
            "error": f"导入FS-Net模块失败，请检查依赖是否安装: {ie}"
        }
    except Exception as e:
        return {
            "model": "FS-Net",
            "status": "失败",
            "error": str(e)
        } 

def all_models_predict(filepath: str):
    """
    对给定的pcap文件并行运行所有模型的预测。
    """
    # 模型预测函数的列表
    # 元组格式: (函数名, '模型标识')
    prediction_functions = [
        (predict_df, 'DF-Master'),
        (predict_appscanner, 'AppScanner'),
        (predict_fa_net, 'FA-Net'),
        (predict_fs_net, 'FS-Net'),
        (predict_yatc, 'YaTC'),
        (predict_lexnet, 'LexNet') # <--- 添加 LexNet
    ]

    results = []
    
    for func, model_name in prediction_functions:
        try:
            # 调用预测函数
            result = func(filepath)
            results.append(result)
        except Exception as e:
            # 记录任何意外错误
            print(f"模型 {model_name} 在预测时发生严重错误: {e}")
            results.append({
                "model": model_name,
                "status": "失败",
                "error": "一个意外的内部错误发生了。"
            })

    # 返回所有模型的结果列表
    return results 