
import os
import sys
import yaml
import logging
import numpy as np
import torch
import joblib

# --- 将项目根目录添加到Python路径中 ---
# 使用相对路径，以适应在原型系统中的新位置
PROJ_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(PROJ_ROOT)) # app 目录
sys.path.append(PROJ_ROOT) # lexnet_model 目录


from lexnet_model.core.model import build_lexnet
from lexnet_model.data_preparation.generate_features import process_single_pcap

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 全局变量，缓存模型和配置 ---
model = None
config = None
scaler_magnitude = None
scaler_direction = None
device = None

def load_model():
    """加载模型、配置和缩放器"""
    global model, config, scaler_magnitude, scaler_direction, device

    if model is not None:
        return

    try:
        # --- 1. 加载配置文件 ---
        config_path = os.path.join(PROJ_ROOT, 'config.yml')
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        logger.info(f"成功加载配置文件: {config_path}")

        # --- 2. 加载全局缩放器 ---
        scaler_magnitude_path = os.path.join(PROJ_ROOT, 'data', 'scalers', 'scaler_magnitude.joblib')
        scaler_direction_path = os.path.join(PROJ_ROOT, 'data', 'scalers', 'scaler_direction.joblib')

        scaler_magnitude = joblib.load(scaler_magnitude_path)
        scaler_direction = joblib.load(scaler_direction_path)
        logger.info("成功加载全局数据缩放器。")

        # --- 3. 加载模型 ---
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {device}")

        model_cfg = config['model']
        num_classes = len(config['data']['class_names'])
        prototype_shape = (
            model_cfg['prototypes_per_class'] * num_classes,
            model_cfg['prototype_channel_depth'],
            model_cfg['prototype_h'],
            model_cfg['prototype_w']
        )
        
        model = build_lexnet(
            num_classes=num_classes,
            prototype_shape=prototype_shape,
            input_shape=(1, 56, 28),
            base_architecture=model_cfg['base_architecture']
        )
        
        model_path = os.path.join(PROJ_ROOT, 'best_model.pth')
        model.load_state_dict(torch.load(model_path, map_location=device))
        logger.info(f"成功从 {model_path} 加载模型权重。")
        
        model.to(device)
        model.eval()

    except Exception as e:
        logger.error(f"加载模型或配置文件时出错: {e}")
        raise e

def preprocess_for_prediction(X_batch, scaler_magnitude, scaler_direction):
    """
    对预测时的数据进行预处理，使用预先加载的全局缩放器。
    (B, 784) -> (B, 1, 56, 28)
    """
    if X_batch.ndim != 2 or X_batch.shape[1] != 784:
        logger.warning(f"输入批次的形状不是 (B, 784)，而是 {X_batch.shape}。将跳过预处理。")
        return torch.from_numpy(X_batch.astype(np.float32))

    X = X_batch.astype(np.float32)

    # 1. 分离大小和方向
    X_magnitude = np.abs(X)
    X_direction = np.sign(X)

    # 2. 归一化
    X_magnitude_scaled = scaler_magnitude.transform(X_magnitude)
    X_direction_scaled = scaler_direction.transform(X_direction)

    # 3. 合并并重塑
    X_combined = np.concatenate([X_magnitude_scaled, X_direction_scaled], axis=1)
    X_processed = np.reshape(X_combined, (X.shape[0], 1, 56, 28))
    
    return torch.from_numpy(X_processed)

def predict_from_pcap(pcap_path):
    """
    对单个PCAP文件进行预测。
    """
    global model, config, scaler_magnitude, scaler_direction, device

    if model is None:
        load_model()
    
    # --- 1. 从PCAP文件中提取特征 ---
    logger.info(f"[LexNet API] Starting feature extraction from: {pcap_path}")
    chunk_size = config['data']['chunk_size']
    features = process_single_pcap(pcap_path, chunk_size)
    logger.info(f"[LexNet API] Feature extraction finished. Found {len(features)} features.")

    if not features:
        logger.warning(f"未能从 {pcap_path} 中提取任何有效特征。请检查pcap文件是否包含带有载荷的IP/TCP或IP/UDP流量。")
        return {"error": f"未能从 {os.path.basename(pcap_path)} 中提取任何有效特征。"}

    # --- 2. 数据预处理 ---
    data = np.vstack(features)
    data_tensor = preprocess_for_prediction(data, scaler_magnitude, scaler_direction).to(device)

    # --- 3. 执行预测 ---
    predictions = []
    with torch.no_grad():
        for i in range(len(data_tensor)):
            sample = data_tensor[i:i+1] # (1, 1, 56, 28)
            logits, _ = model(sample)
            pred_index = torch.argmax(logits, dim=1).item()
            predictions.append(pred_index)

    # --- 4. 格式化结果 ---
    class_names = config['data']['class_names']
    pred_counts = {name: 0 for name in class_names}
    for pred_idx in predictions:
        pred_name = class_names[pred_idx]
        pred_counts[pred_name] += 1
        
    total_preds = len(predictions)
    
    results = {
        "summary": f"对 {os.path.basename(pcap_path)} 的预测完成。总共分析了 {total_preds} 个流量。",
        "predictions": [],
    }
    
    for name, count in pred_counts.items():
        percentage = (count / total_preds) * 100 if total_preds > 0 else 0
        results["predictions"].append({
            "class": name,
            "count": count,
            "percentage": round(percentage, 2)
        })
        
    if total_preds > 0:
        dominant_class = max(pred_counts, key=pred_counts.get)
        results["dominant_class"] = dominant_class
    else:
        results["dominant_class"] = "未检测到任何可分类的活动"

    return results

if __name__ == '__main__':
    # 用于测试的简单脚本
    try:
        load_model()
        logger.info("模型和配置加载成功！")
        # 在这里可以添加一个测试pcap的路径来验证
        # test_pcap_path = 'path/to/your/test.pcap'
        # if os.path.exists(test_pcap_path):
        #     prediction_result = predict_from_pcap(test_pcap_path)
        #     import json
        #     print(json.dumps(prediction_result, indent=2, ensure_ascii=False))
        # else:
        #     logger.warning("请提供一个测试pcap文件路径来进行测试。")
            
    except Exception as e:
        logger.error(f"在独立测试模式下运行失败: {e}", exc_info=True) 