
# =================================================================
# === LEXNet Refactored - 全局配置文件
# =================================================================
# 此文件集中管理所有超参数和路径，便于调整实验

# --- 模型名称 ---
model_name: "lexnet_refactored"

# --- 新的、统一的数据准备配置 ---
# generate_features.py 脚本将从此部分读取配置
data_preparation:
  # [必须修改] PCAP文件的根目录
  pcap_root: 'D:/新工程数据集/'
  
  # 生成的 .npy 数据集存放目录
  output_dir: 'data/MyAttackDataset_balanced_refactored/'
  
  # 单个数据样本的维度 (特征数)
  chunk_size: 784
  
  # 从数据集中划分多少比例作为测试集
  test_split: 0.2

  file_sample_rate: 0.5
# --- 数据相关配置 ---
data:
  # [必须修改] 指向你的原始pcap文件根目录, 用于生成数据集和获取类别名称
  # 这是包含类别子文件夹的根目录, 例如 'D:/数据集/MyPcaps/'
  pcap_dir: 'D:/新工程数据集/'
  
  # [可选修改] 生成的 .npy 数据集存放的目录 (相对于项目根目录)
  # 运行 `generate_features.py` 时会创建此目录
  output_dir_refactored: 'data/MyAttackDataset_balanced_refactored/'

  # [新增] 从训练集中划分出验证集的比例
  validation_split_ratio: 0.15

  # [新增] 单个数据样本的维度 (特征数), 保持与 data_preparation 中一致
  chunk_size: 784

  # [新增][必须修改] 类别名称列表, 顺序必须与数据集生成时一致
  # 例如: ['Benign', 'DDoS', 'PortScan']
  class_names: ['勒索软件', '恶意文件', '扫描探测', '木马流量', '致瘫攻击', '良性', '隐蔽传输', '高危漏洞']


# --- 数据生成脚本配置 ---
data_generation:
  # 从训练集中划分多少比例作为测试集
  test_split: 0.2
  
  # 单个数据样本的维度 (特征数)
  chunk_size: 784


# --- 模型结构配置 ---
model:
  # 骨干网络名称 (目前固定)
  base_architecture: "lexnet_backbone"
  
  # 每个类别的原型数量
  prototypes_per_class: 2
  
  # 原型在特征图中的通道深度 (应与骨干网络最后一层输出通道数一致)
  prototype_channel_depth: 32
  
  # 原型核的大小 (height, width)
  prototype_h: 1
  prototype_w: 1


# --- 训练过程参数 ---
train_params:
  # 总训练轮数
  epochs: 50
  
  # 批处理大小
  batch_size: 32
  
  # "预热"轮数：只训练原型向量，固定骨干网络
  warm_epochs: 5
  
  # --- 原型更新 (Push) 相关 ---
  # 从第几轮开始进行原型更新
  push_start: 10
  
  # 每隔多少轮进行一次原型更新
  push_freq: 10
  
  # 每次原型更新后，单独训练最后一层的轮数
  push_last_epochs: 5
  
  # --- 学习率调度器相关 ---
  # 联合训练阶段，学习率每隔多少轮衰减一次
  joint_lr_step_size: 5
  
  # 学习率衰减的乘法因子
  gamma_lr: 0.5


# --- 学习率配置 ---
learning_rates:
  # 骨干网络 (特征提取器) 的学习率
  features: 0.001
  
  # 原型向量的学习率
  prototype_vectors: 0.001
  
  # 最后一层分类器的学习率
  last_layer: 0.01


# --- 损失函数各部分系数 ---
# loss = crs_ent * L_ce + clst * L_clst + sep * L_sep + reg_protos * R_p + reg_last * R_l
loss_coefficients:
  # 交叉熵损失
  crs_ent: 1.0
  
  # 聚类损失 (同类原型与样本距离)
  clst: 0.8
  
  # 分离损失 (异类原型与样本距离)
  sep: -0.08
  
  # 原型 L2 正则化
  reg_protos: 0.001
  
  # 最后一层 L2 正则化
  reg_last: 0.0001 