
# -*- coding: utf-8 -*-
"""
数据集和数据加载模块 (重构版)

功能:
- 定义了数据预处理流程，包括关键的 reshape 操作。
- 提供了自定义的 PyTorch Dataset 类来封装数据。
- 提供了创建 train/validation/test 数据加载器的辅助函数。
"""
import os
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader, TensorDataset, Subset
from sklearn import preprocessing
from sklearn.model_selection import StratifiedKFold
import logging
import joblib
from functools import partial
import sys

def preprocess_batch(X_batch, scaler_magnitude, scaler_direction):
    """
    对一个批次的原始一维数据进行预处理和变形。
    此函数现在使用预先加载的全局缩放器。

    Args:
        X_batch (np.array): 从DataLoader出来的一批原始数据，形状为 (B, 784)，B是批量大小。
        scaler_magnitude (object): 加载好的 magnitude scaler。
        scaler_direction (object): 加载好的 direction scaler。
    Returns:
        torch.Tensor: 处理后可直接输入模型的张量，形状为 (B, 1, 56, 28)
    """
    if X_batch.ndim != 2 or X_batch.shape[1] != 784:
        logging.warning(f"输入批次的形状不是 (B, 784)，而是 {X_batch.shape}。将跳过预处理。")
        return torch.from_numpy(X_batch.astype(np.float32))

    # 使用 float32 以节省内存
    X = X_batch.astype(np.float32)

    # 1. 分离大小和方向
    X_magnitude = np.abs(X)
    X_direction = np.sign(X)

    # 2. 归一化 (使用加载的全局 scaler!)
    X_magnitude = scaler_magnitude.transform(X_magnitude)
    X_direction = scaler_direction.transform(X_direction)

    # 3. 合并并重塑为对CNN友好的图像格式 (B, C, H, W)
    # (B, 784) + (B, 784) -> (B, 1568) -> (B, 1, 56, 28)
    X_combined = np.concatenate([X_magnitude, X_direction], axis=1)
    X_processed = np.reshape(X_combined, (X.shape[0], 1, 56, 28))
    
    # 4. 使用 from_numpy 避免额外的数据复制
    return torch.from_numpy(X_processed)


def custom_collate_fn(batch, scaler_magnitude, scaler_direction):
    """
    自定义的collate_fn，用于在运行时对批次数据进行预处理。
    这是一个顶层函数，以避免多进程环境下的pickle问题。
    """
    # batch 是一个元组列表 [(data_sample_1, label_1), (data_sample_2, label_2), ...]
    raw_x = np.stack([item[0] for item in batch])
    raw_y = np.stack([item[1] for item in batch])

    # 对整个批次进行预处理
    processed_x = preprocess_batch(raw_x, scaler_magnitude, scaler_direction)
    y = torch.LongTensor(raw_y)

    return processed_x, y


def worker_init_fn(worker_id):
    """
    Initialization function for each DataLoader worker.
    It opens the memory-mapped file in the worker process to avoid pickling issues.
    """
    worker_info = torch.utils.data.get_worker_info()
    dataset = worker_info.dataset

    # The actual dataset is nested in Subset for train/val sets
    if isinstance(dataset, Subset):
        dataset = dataset.dataset

    # Now dataset is the NpyDataset instance. Let's open the file.
    if dataset.data is None:
        try:
            dataset.data = np.load(dataset.data_path, mmap_mode='r')
        except Exception as e:
            logging.error(f"Worker {worker_id} failed to load data from {dataset.data_path}: {e}")


class NpyDataset(Dataset):
    """
    一个用于加载.npy文件的自定义PyTorch Dataset。
    它使用内存映射来处理大文件，并且不在初始化时预处理数据。
    """
    def __init__(self, data_path, label_path):
        """
        Args:
            data_path (str): .npy 数据文件路径。
            label_path (str): .npy 标签文件路径。
        """
        logging.info(f"Dataset object created for data path: {data_path}")
        # 只保存路径，不在主进程中加载数据
        self.data_path = data_path
        self.data = None  # 将在每个worker中独立初始化
        
        # 标签通常很小，可以安全地加载在主进程中
        self.labels = np.load(label_path, allow_pickle=True)
        logging.info(f"Labels loaded for {len(self.labels)} samples.")

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        # 确保数据在worker中已经被加载
        if self.data is None:
            # 这个分支主要用于 num_workers=0 的情况
            self.data = np.load(self.data_path, mmap_mode='r')

        # 直接返回原始数据，预处理将由DataLoader的collate_fn完成
        return self.data[idx], self.labels[idx]


def get_dataloaders(config, batch_size, val_split_ratio=0.2, shuffle=True, num_workers=2):
    """
    从.npy文件创建训练、验证和测试数据加载器。
    采用内存映射和即时批处理技术来处理大规模数据集。
    现在会加载并使用全局缩放器。
    """
    logging.info("="*30)
    logging.info("         准备数据加载器         ")
    logging.info("="*30)

    # --- 新增: 加载全局缩放器 ---
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_dir = os.path.join(project_root, config['data']['output_dir_refactored'])
    scaler_magnitude_path = os.path.join(data_dir, 'scalers', 'scaler_magnitude.joblib')
    scaler_direction_path = os.path.join(data_dir, 'scalers', 'scaler_direction.joblib')

    try:
        scaler_magnitude = joblib.load(scaler_magnitude_path)
        scaler_direction = joblib.load(scaler_direction_path)
        logging.info("成功加载全局数据缩放器。")
    except FileNotFoundError:
        logging.error("错误: 未找到缩放器文件。")
        logging.error(f"请先运行 'data_preparation/generate_scalers.py' 脚本来生成它们。")
        logging.error(f"预期的路径: {scaler_magnitude_path}")
        sys.exit(1)

    # --- 创建一个包含缩放器上下文的 collate_fn ---
    collate_fn_with_scalers = partial(
        custom_collate_fn, 
        scaler_magnitude=scaler_magnitude, 
        scaler_direction=scaler_direction
    )

    # --- 创建测试集 ---
    test_dataset = NpyDataset(
        data_path=os.path.join(data_dir, 'test_x.npy'),
        label_path=os.path.join(data_dir, 'test_y.npy')
    )
    
    # --- 创建完整的训练+验证集 ---
    full_train_dataset = NpyDataset(
        data_path=os.path.join(data_dir, 'train_x.npy'),
        label_path=os.path.join(data_dir, 'train_y.npy')
    )

    # --- 基于索引划分训练集和验证集 ---
    train_dataset, val_dataset = None, None
    if val_split_ratio > 0:
        skf = StratifiedKFold(n_splits=int(1/val_split_ratio), shuffle=True, random_state=42)
        try:
            # 使用完整数据集的标签进行分层抽样
            train_indices, val_indices = next(iter(skf.split(np.zeros(len(full_train_dataset)), full_train_dataset.labels)))
            train_dataset = Subset(full_train_dataset, train_indices)
            val_dataset = Subset(full_train_dataset, val_indices)
        except ValueError:
            logging.warning("样本太少无法进行分层抽样，将进行随机抽样。")
            indices = np.arange(len(full_train_dataset))
            np.random.shuffle(indices)
            split_point = int(len(full_train_dataset) * (1-val_split_ratio))
            train_indices, val_indices = indices[:split_point], indices[split_point:]
            train_dataset = Subset(full_train_dataset, train_indices)
            val_dataset = Subset(full_train_dataset, val_indices)
    else:
        train_dataset = full_train_dataset
    
    # --- 创建加载器 ---
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=collate_fn_with_scalers,
        pin_memory=True,
        worker_init_fn=worker_init_fn # 在每个worker中初始化
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_fn_with_scalers,
        pin_memory=True,
        worker_init_fn=worker_init_fn # 在每个worker中初始化
    )
    
    val_loader = None
    if val_dataset and len(val_dataset) > 0:
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            collate_fn=collate_fn_with_scalers,
            pin_memory=True,
            worker_init_fn=worker_init_fn # 在每个worker中初始化
        )

    logging.info(f"训练集大小: {len(train_dataset)}")
    if val_loader:
        logging.info(f"验证集大小: {len(val_dataset)}")
    logging.info(f"测试集大小: {len(test_dataset)}")
    logging.info(f"批处理大小: {batch_size}")
    
    return train_loader, val_loader, test_loader 