
# -*- coding: utf-8 -*-
"""
LEXNet 模型定义 (重构版)

功能:
- 将原有的 backbone 和主模型文件合并。
- 提供了清晰的模型结构，包括骨干网络和原型层。
- 包含辅助函数，用于构建和初始化模型。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .receptive_field import compute_proto_layer_rf_info

# =========================================================================
# === 1. 骨干网络 (Backbone) 部分 (原 lexnet_backbone.py)
# =========================================================================

def conv3x3(in_channels, out_channels, stride=1):
    """3x3 卷积层"""
    return nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)

class LEResidualBlock(nn.Module):
    """
    LERes 残差块 (重构为标准的ResNet块)
    修复了原始实现中主路径和残差路径下采样不一致导致的尺寸错误。
    """
    NUM_LAYERS = 2

    def __init__(self, in_channels, out_channels, stride=1, linear_transformation=False, baseline_activation_function="relu"):
        super(LEResidualBlock, self).__init__()
        
        # --- 主路径 ---
        # 第一个卷积层负责处理步长（下采样）
        self.conv1 = conv3x3(in_channels, out_channels, stride=stride)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        # 第二个卷积层在下采样后的特征图上操作
        self.conv2 = conv3x3(out_channels, out_channels)
        self.bn2 = nn.BatchNorm2d(out_channels)

        # --- 残差/快捷连接路径 ---
        self.downsample = None
        # 当需要下采样 或 输入/输出通道数不匹配时，创建下采样层
        if linear_transformation:
            self.downsample = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels),
            )
            
        # 最终的激活函数
        if baseline_activation_function == "sigmoid":
            self.last_activation = nn.Sigmoid()
        else:
            self.last_activation = nn.ReLU()

    def forward(self, x):
        residual = x

        # 主路径
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        # 残差路径
        if self.downsample is not None:
            residual = self.downsample(x)
        
        # 合并
        out += residual
        out = self.last_activation(out)
        return out

    def block_conv_info(self):
        # 正确地反映层信息以用于感受野计算
        s1 = self.conv1.stride[0]
        s2 = self.conv2.stride[0] # s2 is always 1
        return [3, 3], [s1, s2], [1, 1]


class LEXNetFeatures(nn.Module):
    """LEXNet 的 CNN 骨干网络"""
    def __init__(self, block=LEResidualBlock, lyrs=[2, 2], baseline_activation_function="relu"):
        super(LEXNetFeatures, self).__init__()
        self.kernel_sizes = []
        self.strides = []
        self.paddings = []
        
        self.in_channels = 16
        self.conv1 = nn.Conv2d(1, self.in_channels, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(self.in_channels)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)

        self._add_conv_info(7, 2, 3)

        self.layer1 = self._make_layer(block, 16, lyrs[0])
        self.layer2 = self._make_layer(block, 32, lyrs[1], stride=2, baseline_activation_function=baseline_activation_function)
        
        self._initialize_weights()

    def _add_conv_info(self, kernel_size, stride, padding):
        self.kernel_sizes.append(kernel_size)
        self.strides.append(stride)
        self.paddings.append(padding)

    def _make_layer(self, block, out_channels, blocks, stride=1, baseline_activation_function="relu"):
        linear_transformation = False
        if stride != 1 or self.in_channels != out_channels:
            linear_transformation = True
        
        layers = []
        layers.append(block(self.in_channels, out_channels, stride, linear_transformation))
        self.in_channels = out_channels
        for i in range(1, blocks):
            layers.append(
                block(
                    self.in_channels,
                    out_channels,
                    baseline_activation_function=baseline_activation_function,
                )
            )

        for each_block in layers:
            block_kernel_sizes, block_strides, block_paddings = each_block.block_conv_info()
            self.kernel_sizes.extend(block_kernel_sizes)
            self.strides.extend(block_strides)
            self.paddings.extend(block_paddings)
            
        return nn.Sequential(*layers)

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        x = self.layer1(x)
        x = self.layer2(x)
        return x

    def conv_info(self):
        return self.kernel_sizes, self.strides, self.paddings

def lexnet_backbone(baseline_activation_function="relu"):
    """构建 LEXNet 的 CNN 骨干网络"""
    return LEXNetFeatures(baseline_activation_function=baseline_activation_function)


# =========================================================================
# === 2. LEXNet 主模型 (原 lexnet.py)
# =========================================================================

class LEXNet(nn.Module):
    """LEXNet 模型"""
    def __init__(self, features, input_shape, prototype_shape, num_classes,
                 prototype_activation_function="log", init_weights=True):
        super(LEXNet, self).__init__()
        self.input_shape = input_shape # e.g., (1, 28, 28)
        self.num_classes = num_classes
        self.prototype_shape = prototype_shape # e.g., (num_prototypes, 32, 1, 1)
        self.num_prototypes = prototype_shape[0]
        self.prototype_activation_function = prototype_activation_function
        self.epsilon = 1e-4

        self.features = features # CNN Backbone

        # Prototype vectors
        self.prototype_vectors = nn.Parameter(torch.rand(self.prototype_shape), requires_grad=True)
        
        # Prototype-class identity matrix
        self.prototype_class_identity = nn.Parameter(torch.zeros(self.num_prototypes, self.num_classes), requires_grad=False)
        self.num_prototypes_per_class = self.num_prototypes // self.num_classes
        for j in range(self.num_prototypes):
            self.prototype_class_identity[j, j // self.num_prototypes_per_class] = 1

        # Receptive field info for visualization/push
        self.proto_layer_rf_info = compute_proto_layer_rf_info(
            img_height=self.input_shape[1], # H
            img_width=self.input_shape[2],  # W
            layer_filter_sizes=self.features.conv_info()[0],
            layer_strides=self.features.conv_info()[1],
            layer_paddings=self.features.conv_info()[2],
            prototype_kernel_size=self.prototype_shape[2],
        )

        # Last layer
        self.last_layer = nn.Linear(self.num_prototypes, self.num_classes, bias=False)

        if init_weights:
            self._initialize_weights()

    def _l2_convolution(self, x):
        """计算输入 x 和原型之间的 L2 距离"""
        # x is conv_features output
        # self.prototype_vectors is Wp
        # a^2 - 2ab + b^2
        x2 = x ** 2
        x2_patch_sum = F.conv2d(input=x2, weight=torch.ones(self.prototype_shape).to(x.device))

        p2 = self.prototype_vectors ** 2
        p2_sum = torch.sum(p2.view(self.num_prototypes, -1), dim=1).view(-1, 1, 1)

        xp = F.conv2d(input=x, weight=self.prototype_vectors)
        
        # distance d is of shape (batch, num_prototypes, out_h, out_w)
        d = F.relu(x2_patch_sum - 2 * xp + p2_sum)
        return d

    def prototype_distances(self, x):
        """计算每个输入样本到每个原型的最小距离"""
        conv_features = self.features(x)
        distances = self._l2_convolution(conv_features)
        # Global min pooling
        min_distances = -F.max_pool2d(-distances, kernel_size=(distances.size(2), distances.size(3)))
        min_distances = min_distances.view(-1, self.num_prototypes)
        return min_distances

    def distance_to_similarity(self, distances):
        """将距离转换为相似度分数"""
        if self.prototype_activation_function == 'log':
            return torch.log((distances + 1) / (distances + self.epsilon))
        elif self.prototype_activation_function == 'linear':
            return -distances
        else:
            raise ValueError("Unsupported activation function")

    def forward(self, x):
        min_distances = self.prototype_distances(x)
        prototype_activations = self.distance_to_similarity(min_distances)
        logits = self.last_layer(prototype_activations)
        return logits, min_distances

    def push_forward(self, x):
        conv_output = self.features(x)
        distances = self._l2_convolution(conv_output)
        return conv_output, distances

    def set_last_layer_incorrect_connection(self, incorrect_strength):
        """设置最后全连接层的权重"""
        positive_one_weights_locations = torch.t(self.prototype_class_identity.clone().detach())
        negative_one_weights_locations = 1 - positive_one_weights_locations
        self.last_layer.weight.data.copy_(
            1 * positive_one_weights_locations + incorrect_strength * negative_one_weights_locations
        )

    def _initialize_weights(self):
        self.set_last_layer_incorrect_connection(incorrect_strength=-0.5)

    def __repr__(self):
        return (
            f"LEXNet(\n"
            f"\tfeatures: {self.features.__class__.__name__},\n"
            f"\tinput_shape: {self.input_shape},\n"
            f"\tprototype_shape: {self.prototype_shape},\n"
            f"\tnum_classes: {self.num_classes}\n)"
        )

# =========================================================================
# === 3. 模型构建辅助函数
# =========================================================================

def get_model(num_classes, prototype_shape, input_shape, base_architecture):
    """
    一个辅助函数，用于构建模型。
    这使得从其他脚本创建模型变得更容易。
    """
    return build_lexnet(
        num_classes=num_classes,
        prototype_shape=prototype_shape,
        input_shape=input_shape,
        # 这里可以根据 base_architecture 的值选择不同的骨干网络，如果将来有的话
        baseline_activation_function="relu" # 示例
    )

def build_lexnet(num_classes, prototype_shape, input_shape=(1, 56, 28), **kwargs):
    """
    构建完整的LEXNet模型。
    """
    # 从kwargs中移除不属于LEXNetFeatures的参数
    features_kwargs = kwargs.copy()
    if 'base_architecture' in features_kwargs:
        del features_kwargs['base_architecture']
        
    features_backbone = LEXNetFeatures(**features_kwargs)
    
    return LEXNet(
        features=features_backbone,
        input_shape=input_shape,
        prototype_shape=prototype_shape,
        num_classes=num_classes,
        init_weights=True
    ) 