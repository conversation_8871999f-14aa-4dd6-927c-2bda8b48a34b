# --- START OF FILE core/push.py (FINAL CORRECTED VERSION) ---

# -*- coding: utf-8 -*-
"""
原型更新 (Push) 模块 (重构版)

功能:
- 实现将训练样本中的潜在表示“推送”为模型原型的核心逻辑。
- 寻找每个原型类别中激活最高的图像块。
- 保存和可视化原型。
"""
import torch
import numpy as np
import os
import time
import logging
import cv2
from tqdm import tqdm

# 关键：导入与训练时完全一致的预处理函数
from .dataset import preprocess_batch 

# --- 辅助函数 ---

def makedir(path):
    if not os.path.exists(path):
        os.makedirs(path)

def find_high_activation_crop(activation_map, percentile=95):
    """找到激活图中高激活区域的边界框"""
    threshold = np.percentile(activation_map, percentile)
    mask = activation_map >= threshold
    if not np.any(mask):
        return 0, activation_map.shape[0], 0, activation_map.shape[1]
        
    coords = np.argwhere(mask)
    y_min, x_min = coords.min(axis=0)
    y_max, x_max = coords.max(axis=0)
    return y_min, y_max, x_min, x_max

def save_prototype_image(fname, img_data):
    """
    保存原型图像，进行归一化处理。
    img_data 应该是一个 2D numpy 数组 (灰度图)。
    """
    # 归一化到 0-255 并转为 uint8
    # 增加一个检查，确保输入不是空的
    if img_data is None or img_data.size == 0:
        logging.warning(f"Attempted to save an empty image to {fname}. Skipping.")
        return
    img_data_normalized = cv2.normalize(img_data, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    cv2.imwrite(fname, img_data_normalized)


def _compute_rf_prototype(img_size_tuple, rf_info, proto_location):
    """
    根据给定的感受野信息和原型在特征图上的位置，计算其在原图上的边界框。
    Args:
        img_size_tuple (tuple): 原始输入图像的 (H, W) 元组。
        rf_info (list): 包含感受野信息的列表 [j, r, start]。
        proto_location (tuple): 原型在特征图上的 (h, w) 坐标。
    Returns:
        list: [h_start, h_end, w_start, w_end]
    """
    img_h, img_w = img_size_tuple
    j, r, start = rf_info[2], rf_info[3], rf_info[4]
    h_loc, w_loc = proto_location

    h_start = int(max(0, start + h_loc * j))
    h_end = int(min(img_h, h_start + r))
    
    w_start = int(max(0, start + w_loc * j))
    w_end = int(min(img_w, w_start + r))
    
    return [h_start, h_end, w_start, w_end]


# --- Push 核心逻辑 ---

def _update_prototypes_on_batch(
    search_batch_input,
    start_index_of_search_batch,
    model,
    global_min_proto_dist,
    global_min_fmap_patches,
    proto_rf_boxes,
    proto_bound_boxes,
    search_y,
):
    """
    在一个批次上更新每个原型的最近距离和对应的特征图块。
    """
    model.eval()
    model_module = model.module if isinstance(model, torch.nn.DataParallel) else model

    with torch.no_grad():
        search_batch_input = search_batch_input.cuda()
        # push_forward 返回 (conv_features, distances)
        conv_features, distances = model_module.push_forward(search_batch_input)
        proto_dist_torch = distances.cpu()

    batch_size = search_batch_input.size(0)
    num_prototypes = model_module.num_prototypes

    for i in range(batch_size):
        for j in range(num_prototypes):
            target_map = proto_dist_torch[i, j, :, :]
            min_dist_in_map = torch.min(target_map)

            if min_dist_in_map < global_min_proto_dist[j]:
                global_min_proto_dist[j] = min_dist_in_map.item()
                
                min_loc = torch.argmin(target_map)
                h_loc = min_loc // target_map.shape[1]
                w_loc = min_loc % target_map.shape[1]

                fmap_patch = conv_features[i, :, h_loc:h_loc+1, w_loc:w_loc+1]
                global_min_fmap_patches[j, :, :, :] = fmap_patch.cpu().numpy()

                rf_info = model_module.proto_layer_rf_info
                # 从模型配置中获取预处理后的图像尺寸
                img_size_tuple = (model_module.input_shape[1], model_module.input_shape[2])
                rf_box = _compute_rf_prototype(img_size_tuple, rf_info, (h_loc.item(), w_loc.item()))
                
                proto_rf_boxes[j, 0] = start_index_of_search_batch + i
                proto_rf_boxes[j, 1:5] = rf_box
                proto_rf_boxes[j, 5] = search_y[i].item()

                act_map = distances[i, j, :, :].cpu().numpy()
                act_h_min, act_h_max, act_w_min, act_w_max = find_high_activation_crop(act_map)
                proto_bound_boxes[j, 0] = proto_rf_boxes[j, 0]
                proto_bound_boxes[j, 1:5] = [act_h_min, act_h_max, act_w_min, act_w_max]
                proto_bound_boxes[j, 5] = proto_rf_boxes[j, 5]


def push_prototypes(
    dataloader,
    prototype_network_parallel,
    root_dir_for_saving_prototypes,
    epoch_number=None,
    percentile=95,
):
    """
    主函数，执行原型更新流程。
    """
    model = prototype_network_parallel
    model_module = model.module if isinstance(model, torch.nn.DataParallel) else model
    model.eval()
    logging.info(f"Epoch {epoch_number}: Pushing prototypes...")
    start = time.time()
    
    prototype_shape = model_module.prototype_shape
    n_prototypes = model_module.num_prototypes
    global_min_proto_dist = np.full(n_prototypes, float('inf'))
    global_min_fmap_patches = np.zeros(
        [n_prototypes, prototype_shape[1], prototype_shape[2], prototype_shape[3]]
    )
    proto_rf_boxes = np.full(shape=[n_prototypes, 6], fill_value=-1)
    proto_bound_boxes = np.full(shape=[n_prototypes, 6], fill_value=-1)
    
    makedir(root_dir_for_saving_prototypes)

    search_batch_size = dataloader.batch_size
    for i, (search_batch_input, search_y) in enumerate(tqdm(dataloader, desc=f"Pushing Epoch {epoch_number}")):
        start_index = i * search_batch_size
        _update_prototypes_on_batch(
            search_batch_input, start_index, model,
            global_min_proto_dist, global_min_fmap_patches,
            proto_rf_boxes, proto_bound_boxes, search_y
        )

    logging.info("Executing push...")
    prototype_update = np.reshape(global_min_fmap_patches, tuple(prototype_shape))
    model_module.prototype_vectors.data.copy_(torch.tensor(prototype_update, dtype=torch.float32).cuda())
    
    _save_and_visualize_prototypes(
        dataloader, model, root_dir_for_saving_prototypes, proto_rf_boxes,
        epoch_number, percentile
    )

    logging.info(f"Push finished in {time.time() - start:.2f} seconds.")


def _save_and_visualize_prototypes(
    dataloader, model, root_dir_for_saving_prototypes, proto_rf_boxes,
    epoch_number, percentile
):
    """保存和可视化找到的原型。(最终完整修正版)"""
    model_module = model.module if isinstance(model, torch.nn.DataParallel) else model
    
    proto_epoch_dir = os.path.join(root_dir_for_saving_prototypes, f"epoch-{epoch_number}")
    makedir(proto_epoch_dir)

    with torch.no_grad():
        for j in range(model_module.num_prototypes):
            proto_dir = os.path.join(proto_epoch_dir, f"prototype-{j}")
            makedir(proto_dir)
            
            np.save(os.path.join(proto_dir, 'rf_box.npy'), proto_rf_boxes[j])
            
            # --- 1. 获取并预处理源图像 ---
            img_idx = int(proto_rf_boxes[j, 0])
            original_img_raw, _ = dataloader.dataset[img_idx]

            img_batch_raw = np.expand_dims(original_img_raw, axis=0)
            processed_img_tensor = preprocess_batch(img_batch_raw)
            processed_img = processed_img_tensor.squeeze(0).cpu().numpy()

            # --- 2. 切片感受野区域 ---
            rf_indices = proto_rf_boxes[j].astype(int)
            # processed_img: (C, H, W) -> rf_img: (C, h, w)
            rf_img_patch = processed_img[:, rf_indices[1]:rf_indices[2], rf_indices[3]:rf_indices[4]]
            
            # --- 3. 可视化感受野区域 ---
            # 计算通道平均值，将 (C, h, w) 降维为 (h, w) 的灰度图
            rf_img_visual = np.mean(rf_img_patch, axis=0)
            save_prototype_image(os.path.join(proto_dir, 'rf_original.png'), rf_img_visual)
            
            # --- 4. 可视化原型自身 ---
            # 从模型中获取原型 (C, H_p, W_p)
            proto_self_img_raw = model_module.prototype_vectors[j].detach().cpu().numpy()
            
            # 计算通道平均值，将 (C, H_p, W_p) 降维为 (H_p, W_p) 的灰度图
            proto_self_img_visual = np.mean(proto_self_img_raw, axis=0)
            save_prototype_image(os.path.join(proto_dir, f'prototype_self.png'), proto_self_img_visual)