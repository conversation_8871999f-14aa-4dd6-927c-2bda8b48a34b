
# -*- coding: utf-8 -*-
"""
感受野计算模块 (重构)

功能:
- 为原型层的感受野提供精确的计算。
- 这是理解模型决策区域在原始输入中对应位置的关键。
"""
import math
from collections import namedtuple

# Named tuples for clarity
ReceptiveField = namedtuple('ReceptiveField', 'j r s')
LayerInfo = namedtuple('LayerInfo', 'n k p s')

def compute_proto_layer_rf_info(img_height, img_width, layer_filter_sizes, layer_strides, layer_paddings, prototype_kernel_size):
    """
    Computes the receptive field information for a prototype layer.
    
    Args:
        img_height (int): The height of the input image.
        img_width (int): The width of the input image.
        layer_filter_sizes (list): List of filter sizes for each layer.
        layer_strides (list): List of strides for each layer.
        layer_paddings (list): List of paddings for each layer.
        prototype_kernel_size (int): The kernel size of the prototype layer.
    """
    
    # Check if lists have the same length
    if not (len(layer_filter_sizes) == len(layer_strides) == len(layer_paddings)):
        raise ValueError("Input lists must have the same length.")
        
    # Convert layer info to LayerInfo named tuples
    layer_infos = [LayerInfo(n=i+1, k=k, p=p, s=s) for i, (k, p, s) in enumerate(zip(layer_filter_sizes, layer_paddings, layer_strides))]

    # Compute receptive field info for the center pixel of the prototype
    rf_info = compute_receptive_field_for_center_pixel(
        net_input_height=img_height,
        net_input_width=img_width,
        layer_infos=layer_infos,
        prototype_kernel_size=prototype_kernel_size
    )

    return rf_info

def compute_receptive_field_for_center_pixel(net_input_height, net_input_width, layer_infos, prototype_kernel_size):
    """
    Computes the receptive field parameters for the center pixel of a feature map.
    """
    input_dim_h = net_input_height
    input_dim_w = net_input_width
    
    # Calculate feature map dimensions for height and width
    output_dim_h = input_dim_h
    output_dim_w = input_dim_w
    for info in layer_infos:
        output_dim_h = (output_dim_h - info.k + 2 * info.p) // info.s + 1
        output_dim_w = (output_dim_w - info.k + 2 * info.p) // info.s + 1
        
    # Center pixel index of the feature map
    center_h = (output_dim_h - 1) // 2
    center_w = (output_dim_w - 1) // 2
    
    # Receptive field analysis for height
    rf_h = ReceptiveField(j=1, r=1, s=1)
    for info in reversed(layer_infos):
        rf_h = update_receptive_field(rf_h, info)
    
    # Receptive field analysis for width
    rf_w = ReceptiveField(j=1, r=1, s=1)
    for info in reversed(layer_infos):
        rf_w = update_receptive_field(rf_w, info)
        
    # Start and end indices of the receptive field in the input image
    start_h = center_h * rf_h.j - (rf_h.r - 1) // 2
    end_h = start_h + rf_h.r -1
    
    start_w = center_w * rf_w.j - (rf_w.r - 1) // 2
    end_w = start_w + rf_w.r -1

    return [
        output_dim_h,
        output_dim_w,
        rf_h.j, # jump
        rf_h.r, # receptive field size
        start_h # start
    ]


# Helper functions from https://gist.github.com/Nik-V/2961d0f324e90243493777f40441481c
# (Adapted for clarity and our specific use case)

def update_receptive_field(current_rf, layer_info):
    """
    Updates the current receptive field based on the layer information.
    """
    # Calculate the new jump (j)
    new_j = current_rf.j * layer_info.s
    
    # Calculate the new receptive field size (r)
    new_r = current_rf.r + (layer_info.k - 1) * current_rf.j
    
    # Calculate the new start (s)
    new_s = current_rf.s + ((layer_info.k - 1) / 2) * current_rf.j
    
    return ReceptiveField(j=new_j, r=new_r, s=new_s) 