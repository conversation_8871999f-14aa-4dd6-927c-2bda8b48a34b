
# -*- coding: utf-8 -*-
"""
模型训练器模块 (重构版)

功能:
- 封装了完整的模型训练、评估、和原型更新（Push）的逻辑。
- 管理优化器、学习率调度器和损失函数。
- 提供一个高级API来运行整个训练流程。
"""
import os
import time
import torch
import numpy as np
import logging
from tqdm import tqdm
import shutil

from .push import push_prototypes

class Trainer:
    """
    封装了模型训练、评估、原型更新等所有核心逻辑的类。
    """
    def __init__(self, model, dataloaders, config, run_dir, class_weights=None):
        """
        Args:
            model (nn.Module): 要训练的LEXNet模型。
            dataloaders (dict): 包含 'train', 'val', 'test' 的数据加载器字典。
            config (dict): 包含所有超参数的配置字典。
            run_dir (str): 本次运行的实验结果保存目录。
            class_weights (list or None): 用于损失函数计算的类别权重。
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = model.to(self.device)
        # 如果有多个GPU，使用DataParallel
        if torch.cuda.device_count() > 1:
            logging.info(f"使用 {torch.cuda.device_count()} 个 GPUs!")
            self.model = torch.nn.DataParallel(self.model)
        
        self.dataloaders = dataloaders
        self.config = config
        self.run_dir = run_dir
        
        self.optimizers = self._create_optimizers()
        self.lr_schedulers = self._create_lr_schedulers()
        
        # --- 续训相关状态 ---
        self.start_epoch = 1
        self.best_val_acc = 0.0

        # --- 加载检查点 (如果需要) ---
        # `resume_from_dir` 将由 train.py 脚本在配置中设置
        if 'resume_from_dir' in self.config and self.config['resume_from_dir']:
             self._load_checkpoint(self.config['resume_from_dir'])

        # 处理类别权重
        if class_weights:
            self.class_weights = torch.FloatTensor(class_weights).to(self.device)
            logging.info(f"已应用类别权重: {self.class_weights.cpu().numpy().tolist()}")
        else:
            self.class_weights = None
            logging.info("未使用类别权重。")

        # 确保运行目录存在
        os.makedirs(self.run_dir, exist_ok=True)
        
        logging.info("Trainer 初始化完成。")
        logging.info(f"设备: {self.device}")

# trainer.py
    def _create_optimizers(self):
        """根据配置文件创建不同的优化器"""
        cfg_lrs = self.config['learning_rates']
        model_module = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model
        
        joint_optimizer = torch.optim.Adam([
            {'params': model_module.features.parameters(), 'lr': cfg_lrs['features']},
            {'params': model_module.prototype_vectors, 'lr': cfg_lrs['prototype_vectors']},
            # --- 核心修正：将 last_layer 加入联合优化器 ---
            {'params': model_module.last_layer.parameters(), 'lr': cfg_lrs['last_layer']},
        ], weight_decay=1e-3)

        warm_optimizer = torch.optim.Adam([
            {'params': model_module.prototype_vectors, 'lr': cfg_lrs['prototype_vectors']},
        ])

        last_layer_optimizer = torch.optim.Adam([
            {'params': model_module.last_layer.parameters(), 'lr': cfg_lrs['last_layer']},
        ])
        
        return {
            'joint': joint_optimizer,
            'warm': warm_optimizer,
            'last_layer': last_layer_optimizer
        }

    def _create_lr_schedulers(self):
        """创建学习率调度器"""
        return {
            'joint': torch.optim.lr_scheduler.StepLR(
                self.optimizers['joint'],
                step_size=self.config['train_params']['joint_lr_step_size'],
                gamma=self.config['train_params']['gamma_lr']
            )
        }

    def _compute_loss(self, output, target, min_distances):
        """计算总损失"""
        cfg_coefs = self.config['loss_coefficients']
        
        # 1. Cross-Entropy Loss
        cross_entropy = torch.nn.functional.cross_entropy(output, target, weight=self.class_weights)

        # 2. Prototype-related losses (Clst, Sep)
        model_module = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model
        max_dist = model_module.prototype_shape[1] * model_module.prototype_shape[2] * model_module.prototype_shape[3]
        
        prototypes_of_correct_class = torch.t(model_module.prototype_class_identity[:, target]).to(self.device)
        inverted_distances, _ = torch.max((max_dist - min_distances) * prototypes_of_correct_class, dim=1)
        cluster_cost = torch.mean(max_dist - inverted_distances)

        prototypes_of_wrong_class = 1 - prototypes_of_correct_class
        inverted_distances_to_nontarget, _ = torch.max((max_dist - min_distances) * prototypes_of_wrong_class, dim=1)
        separation_cost = torch.mean(max_dist - inverted_distances_to_nontarget)
        
        # 3. L2 Regularization
        l2_protos = model_module.prototype_vectors.norm(p=2)
        l2_last_layer = model_module.last_layer.weight.norm(p=2)

        # 4. Total Loss
        loss = (
            cfg_coefs['crs_ent'] * cross_entropy +
            cfg_coefs['clst'] * cluster_cost +
            cfg_coefs['sep'] * separation_cost +
            cfg_coefs['reg_protos'] * l2_protos +
            cfg_coefs['reg_last'] * l2_last_layer
        )
        
        return loss, {
            'total_loss': loss.item(),
            'cross_entropy': cross_entropy.item(),
            'cluster_cost': cluster_cost.item(),
            'separation_cost': separation_cost.item()
        }

    def _run_epoch(self, dataloader, optimizer, phase='train'):
        """
        运行一个训练或评估的 epoch。
        """
        is_train = phase == 'train'
        self.model.train(is_train)
        
        total_samples = 0
        total_correct = 0
        total_loss_vals = {}

        pbar = tqdm(dataloader, desc=f"Epoch {self.current_epoch:03d} [{phase.capitalize()}]")
        for inputs, labels in pbar:
            inputs, labels = inputs.to(self.device), labels.to(self.device)
            
            with torch.set_grad_enabled(is_train):
                outputs, min_distances = self.model(inputs)
                loss, loss_dict = self._compute_loss(outputs, labels, min_distances)
            
            if is_train:
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            _, preds = torch.max(outputs, 1)
            total_correct += torch.sum(preds == labels.data)
            total_samples += labels.size(0)
            
            for k, v in loss_dict.items():
                total_loss_vals[k] = total_loss_vals.get(k, 0) + v * labels.size(0)

            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}", 
                'Acc': f"{(total_correct/total_samples):.4f}"
            })

        avg_loss = {k: v / total_samples for k, v in total_loss_vals.items()}
        accuracy = total_correct.double() / total_samples
        
        return accuracy.item(), avg_loss

    def _save_checkpoint(self, epoch, is_best):
        """保存模型检查点"""
        checkpoint_path = os.path.join(self.run_dir, 'latest_checkpoint.pth')
        # 获取底层模型状态，无论是否使用 DataParallel
        model_state = self.model.module.state_dict() if isinstance(self.model, torch.nn.DataParallel) else self.model.state_dict()

        state = {
            'epoch': epoch,
            'best_val_acc': self.best_val_acc,
            'model_state_dict': model_state,
            'joint_optimizer_state_dict': self.optimizers['joint'].state_dict(),
            'warm_optimizer_state_dict': self.optimizers['warm'].state_dict(),
            'last_layer_optimizer_state_dict': self.optimizers['last_layer'].state_dict(),
            'joint_lr_scheduler_state_dict': self.lr_schedulers['joint'].state_dict(),
        }
        torch.save(state, checkpoint_path)
        logging.info(f"已在 {checkpoint_path} 保存最新的检查点 (Epoch {epoch})")

        if is_best:
            best_model_path = os.path.join(self.run_dir, 'best_model.pth')
            # 保存最佳模型时也只保存 state_dict
            torch.save(model_state, best_model_path)
            logging.info(f"🎉 新的最佳模型权重已保存至 {best_model_path} (Val Acc: {self.best_val_acc:.4f})")
    
    def _load_checkpoint(self, resume_dir):
        """从目录加载检查点"""
        checkpoint_path = os.path.join(resume_dir, 'latest_checkpoint.pth')
        if not os.path.exists(checkpoint_path):
            logging.warning(f"在 {resume_dir} 中未找到 'latest_checkpoint.pth'。将从头开始训练。")
            # 尝试加载旧版的 best_model.pth 作为备用
            old_best_model = os.path.join(resume_dir, 'best_model.pth')
            if os.path.exists(old_best_model):
                try:
                    # 获取底层模型引用
                    model_to_load = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model
                    model_to_load.load_state_dict(torch.load(old_best_model, map_location=self.device))
                    logging.info(f"已从旧版 {old_best_model} 成功加载模型权重。优化器状态和epoch将从头开始。")
                except Exception as e:
                    logging.error(f"加载旧版 best_model.pth 失败: {e}。将从头开始训练。")
            return

        logging.info(f"正在从 {checkpoint_path} 加载检查点...")
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 获取底层模型引用
        model_to_load = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model
        model_to_load.load_state_dict(checkpoint['model_state_dict'])
        
        self.optimizers['joint'].load_state_dict(checkpoint['joint_optimizer_state_dict'])
        self.optimizers['warm'].load_state_dict(checkpoint['warm_optimizer_state_dict'])
        self.optimizers['last_layer'].load_state_dict(checkpoint['last_layer_optimizer_state_dict'])
        self.lr_schedulers['joint'].load_state_dict(checkpoint['joint_lr_scheduler_state_dict'])
        
        self.start_epoch = checkpoint['epoch'] + 1
        self.best_val_acc = checkpoint.get('best_val_acc', 0.0) # 兼容旧的检查点
        
        logging.info(f"检查点加载成功。将从 Epoch {self.start_epoch} 继续训练。")
        logging.info(f"已恢复的最佳验证集准确率为: {self.best_val_acc:.4f}")


    def train(self):
        """
        执行完整的训练流程。
        """
        logging.info("="*30)
        logging.info("         开始模型训练         ")
        logging.info("="*30)

        cfg_train = self.config['train_params']
        # best_val_acc 已在 __init__ 中初始化或从检查点加载

        # 获取对底层模型的引用，无论是否使用DataParallel
        model_module = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model

        for epoch in range(self.start_epoch, cfg_train['epochs'] + 1):
            self.current_epoch = epoch
            
            # --- 选择优化器和模式 ---
            if epoch <= cfg_train['warm_epochs']:
                logging.info(f"Epoch {epoch}/{cfg_train['epochs']} [Warm-up Phase]")

                current_optimizer = self.optimizers['warm']
            else:
                logging.info(f"Epoch {epoch}/{cfg_train['epochs']} [Joint Training Phase]")
           
                current_optimizer = self.optimizers['joint']

            # --- 训练和验证 ---
            train_acc, train_loss = self._run_epoch(self.dataloaders['train'], current_optimizer, phase='train')
            if self.dataloaders.get('val'):
                val_acc, val_loss = self._run_epoch(self.dataloaders['val'], optimizer=None, phase='validate')
                logging.info(f"Epoch {epoch} Summary | Train Acc: {train_acc:.4f} | Val Acc: {val_acc:.4f}")
            else:
                val_acc = train_acc # 如果没有验证集，用训练集acc代替
                logging.info(f"Epoch {epoch} Summary | Train Acc: {train_acc:.4f}")

            # 更新学习率
            if epoch > cfg_train['warm_epochs'] and self.lr_schedulers.get('joint'):
                self.lr_schedulers['joint'].step()
                current_lr = self.optimizers['joint'].param_groups[0]['lr']
                logging.info(f"Joint LR updated to: {current_lr}")

            # --- 原型更新 (Push)  ---
            # 'Push' 操作对帮助模型学习有意义的原型至关重要
            if epoch >= cfg_train['push_start'] and epoch % cfg_train['push_freq'] == 0:
                self._push_prototypes()
                # Push后，对最后一层进行微调
                self._finetune_last_layer()

            # --- 保存检查点和最佳模型 ---
            is_best = False
            if self.dataloaders.get('val'):
                # 只有在有验证集时才更新最佳准确率
                if val_acc > self.best_val_acc:
                    self.best_val_acc = val_acc
                    is_best = True
            elif train_acc > self.best_val_acc: 
                 # 没有验证集时，以训练集准确率作为标准
                 self.best_val_acc = train_acc
                 is_best = True

            self._save_checkpoint(epoch, is_best)
        
        logging.info("="*30)
        logging.info("         训练完成         ")
        logging.info("="*30)

    def _push_prototypes(self):
        logging.info("--- 开始原型更新 (Push) ---")
        model_module = self.model.module if isinstance(self.model, torch.nn.DataParallel) else self.model
        push_prototypes(
            self.dataloaders['train'],
            prototype_network_parallel=self.model,
            root_dir_for_saving_prototypes=os.path.join(self.run_dir, "prototypes"),
            epoch_number=self.current_epoch
        )
        # --- 核心修正：在 Push 完成后，立即重置 last_layer 的连接权重 ---
        # 这为接下来的微调提供一个干净的起点
        logging.info("Push后，重置最后一层的权重...")
        model_module.set_last_layer_incorrect_connection(-0.5)

    def _finetune_last_layer(self):
        logging.info("--- 微调最后一层 ---")
        optimizer = self.optimizers['last_layer']
        for i in range(self.config['train_params']['push_last_epochs']):
            self.current_epoch = f"Push_tune_{i+1}"
            self._run_epoch(self.dataloaders['train'], optimizer, phase='train')

    def evaluate(self, use_best_model=True):
        """
        在测试集上评估模型。
        
        Args:
            use_best_model (bool): 是否加载训练过程中保存的最佳模型进行评估。
        
        Returns:
            tuple: (test_accuracy, test_loss)
        """
        if use_best_model:
            model_path = os.path.join(self.run_dir, "best_model.pth")
            if os.path.exists(model_path):
                logging.info(f"正在从 {model_path} 加载最佳模型进行评估...")
                self.model.load_state_dict(torch.load(model_path))
            else:
                logging.warning("找不到最佳模型文件，将使用当前模型状态进行评估。")

        logging.info("="*30)
        logging.info("         开始模型评估         ")
        logging.info("="*30)

        test_acc, test_loss = self._run_epoch(self.dataloaders['test'], optimizer=None, phase='test')
        
        logging.info(f"评估结果 | Test Acc: {test_acc:.4f}")
        logging.info("="*30)
        
        return test_acc, test_loss 