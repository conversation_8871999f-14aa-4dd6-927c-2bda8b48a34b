
# -*- coding: utf-8 -*-
"""
PCAP 数据清洗脚本 (重构版)

功能:
- 扫描指定的pcap根目录，验证每个pcap/pcapng文件的可读性。
- 将无法被 scapy 正常读取的损坏文件移动到隔离区。
- 保证后续数据生成流程的顺利进行。
"""
import os
import sys
import yaml
import argparse
import logging
import shutil
from scapy.utils import PcapReader
from tqdm import tqdm
from multiprocessing import Pool, TimeoutError, cpu_count

# --- 将项目根目录添加到Python路径中 ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def is_pcap_valid(pcap_path):
    """
    检查单个pcap文件是否可以被scapy成功读取。
    (V2: 进行彻底的全文件读取以保证准确性)
    返回 True 如果文件有效，否则返回 False。
    """
    try:
        # 强制对整个文件进行迭代读取。
        # a simple act of iterating through all packets is the validation.
        # This will trigger any parsing errors, no matter where they are in the file.
        with PcapReader(pcap_path) as pcap_reader:
            for _ in pcap_reader:
                pass
        return True
    except Exception as e:
        # 捕获scapy在读取时可能抛出的任何异常
        # The line break '\n' ensures the warning is not overwritten by the tqdm progress bar.
        logging.warning(f"\n发现损坏文件 '{os.path.basename(pcap_path)}'。错误: {type(e).__name__}")
        return False

def main():
    """主执行函数"""
    setup_logging()
    
    # --- 1. 加载配置以获取pcap根目录 ---
    parser = argparse.ArgumentParser(description="PCAP Data Cleaning Script")
    parser.add_argument(
        '--config',
        type=str,
        default='config/config.yml',
        help="Path to the configuration file (relative to project root)."
    )
    args = parser.parse_args()

    config_path = os.path.join(PROJECT_ROOT, args.config)
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        logging.error(f"配置文件未找到: '{config_path}'")
        sys.exit(1)

    pcap_root = config['data']['pcap_root_directory']
    if not os.path.isdir(pcap_root):
        logging.error(f"配置中指定的PCAP根目录不存在: {pcap_root}")
        sys.exit(1)

    # --- 2. 创建隔离目录 ---
    quarantine_dir = os.path.join(pcap_root, '_quarantined_pcaps')
    os.makedirs(quarantine_dir, exist_ok=True)
    logging.info(f"损坏的文件将被移动到: {quarantine_dir}")

    # --- 3. 收集所有待检查的pcap文件 ---
    all_pcap_files = []
    for dirpath, _, filenames in os.walk(pcap_root):
        # 确保我们不会检查隔离区内的文件
        if os.path.abspath(dirpath).startswith(os.path.abspath(quarantine_dir)):
            continue
        
        for filename in filenames:
            if filename.lower().endswith(('.pcap', '.pcapng')):
                all_pcap_files.append(os.path.join(dirpath, filename))

    if not all_pcap_files:
        logging.info("在指定目录中未找到任何pcap文件。")
        return

    # --- 4. 使用带超时的并行处理来验证每个文件 ---
    num_processes = cpu_count() - 1 if cpu_count() > 1 else 1
    timeout_seconds = 60  # 为每个文件设置60秒的处理超时
    logging.info(f"找到 {len(all_pcap_files)} 个pcap文件。使用 {num_processes} 个核心并行验证 (每个文件超时: {timeout_seconds}秒)...")
    
    corrupted_count = 0
    with Pool(processes=num_processes) as pool:
        # 提交所有任务并获取异步结果对象
        async_results = [pool.apply_async(is_pcap_valid, (pcap_file,)) for pcap_file in all_pcap_files]

        # 使用tqdm来跟踪已完成的任务
        pbar = tqdm(zip(all_pcap_files, async_results), total=len(all_pcap_files), desc="清洗PCAP文件")
        for pcap_file, result in pbar:
            is_valid = True
            try:
                # 等待结果，但有超时限制
                is_valid = result.get(timeout=timeout_seconds)
            except TimeoutError:
                logging.warning(f"\n文件 '{os.path.basename(pcap_file)}' 处理超时，判定为损坏。")
                is_valid = False
            except Exception as e:
                logging.error(f"\n处理 '{os.path.basename(pcap_file)}' 时发生未知错误: {e}")
                is_valid = False

            if not is_valid:
                corrupted_count += 1
                try:
                    # 在移动文件前，确保原始路径还存在（以防万一）
                    if os.path.exists(pcap_file):
                        destination_path = os.path.join(quarantine_dir, os.path.basename(pcap_file))
                        shutil.move(pcap_file, destination_path)
                except Exception as e:
                    logging.error(f"\n无法移动文件 {pcap_file}。错误: {e}")

    # --- 5. 输出最终报告 ---
    logging.info("\n" + "="*50)
    logging.info("          数据清洗完成！")
    logging.info("="*50)
    logging.info(f"  总共检查文件数: {len(all_pcap_files)}")
    logging.info(f"  发现并隔离损坏文件数: {corrupted_count}")
    if corrupted_count > 0:
        logging.info(f"  所有损坏文件已移至 '{quarantine_dir}' 供您检查。")
    logging.info("\n您现在可以安全地运行特征生成脚本了。")
    logging.info("="*50)

if __name__ == '__main__':
    main() 