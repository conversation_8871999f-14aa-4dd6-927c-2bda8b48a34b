
# -*- coding: utf-8 -*-
"""
数据准备模块 (重构版)

功能:
- 从 PCAP 文件中提取数据包长度序列。
- 将序列分块并进行全局随机化。
- 生成内存高效的 .npy 数据集 (train_x, train_y, test_x, test_y)。

改进:
- 配置与逻辑分离，所有配置项由外部传入。
- 函数式编程风格，增强可读性和可测试性。
- 使用 logging 模块记录详细信息。
- 移除硬编码路径，提高可移植性。
"""
import os
import sys
import random
import numpy as np
import logging
from collections import defaultdict
import shutil
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import argparse
import yaml
import multiprocessing
from functools import partial
import uuid

# Scapy会产生很多警告，我们在这里抑制它们
logging.getLogger("scapy.runtime").setLevel(logging.ERROR)
try:
    from scapy.all import rdpcap, IP, TCP, UDP, Raw, PcapReader
except ImportError:
    print("错误：Scapy库未安装。请运行 'pip install scapy' 进行安装。")
    sys.exit(1)


# --- 日志配置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    stream=sys.stdout)
logger = logging.getLogger(__name__)


def process_single_pcap(pcap_path, chunk_size):
    """
    处理单个pcap文件。为每个流提取前 M (chunk_size) 个字节的载荷作为特征。
    不再进行随机抽样，而是处理所有数据包，为每个检测到的流生成一个特征向量。
    """
    logger.debug(f"开始处理文件: {os.path.basename(pcap_path)}")
    packet_counter, ip_packet_counter, transport_layer_counter, payload_counter = 0, 0, 0, 0

    try:
        # 1. 按五元组提取所有流量
        flows = defaultdict(list)
        with PcapReader(pcap_path) as pcap_reader:
            for packet in pcap_reader:
                packet_counter += 1
                if IP in packet:
                    ip_packet_counter += 1
                    if TCP in packet or UDP in packet:
                        transport_layer_counter += 1
                        try:
                            src_ip = packet[IP].src
                            dst_ip = packet[IP].dst
                            proto = packet[IP].proto

                            if TCP in packet:
                                src_port = packet[TCP].sport
                                dst_port = packet[TCP].dport
                            else:  # UDP
                                src_port = packet[UDP].sport
                                dst_port = packet[UDP].dport

                            key_part1 = tuple(sorted(((src_ip, src_port), (dst_ip, dst_port))))
                            flow_key = key_part1 + (proto,)

                            if packet.haslayer(Raw):
                                payload_counter += 1
                                flows[flow_key].append(bytes(packet[Raw].load))
                        except Exception:
                            continue
    except Exception as e:
        logger.error(f"读取 pcap 文件 {pcap_path} 时出错: {e}", exc_info=False)
        return []

    # 打印详细的统计日志
    logger.info(
        f"Pcap Summary for {os.path.basename(pcap_path)}: "
        f"Total Packets={packet_counter}, IP Packets={ip_packet_counter}, "
        f"TCP/UDP Packets={transport_layer_counter}, Packets with Payload={payload_counter}"
    )

    if not flows:
        logger.debug(f"文件 {os.path.basename(pcap_path)} 中未提取到有效流量。")
        return []

    # 2. 为每个流提取前 M 字节的载荷
    flow_features = []
    for _, payloads in flows.items():
        if not payloads:
            continue

        full_payload = b''.join(payloads)

        # 提取前 M (chunk_size) 字节
        feature_payload = full_payload[:chunk_size]

        # 如果不足，则用0填充
        if len(feature_payload) < chunk_size:
            feature_payload += b'\x00' * (chunk_size - len(feature_payload))

        flow_features.append(np.frombuffer(feature_payload, dtype=np.uint8))

    logger.debug(f"文件 {os.path.basename(pcap_path)} 处理完毕，从 {len(flows)} 个流中提取了 {len(flow_features)} 个特征。")
    return flow_features


def generate_dataset(pcap_root, output_dir, chunk_size=784, test_split=0.2, file_sample_rate=0.5):
    """
    遍历pcap_root下的所有子目录（每个子目录代表一个类别），并行处理pcap文件，
    生成训练集和测试集。
    """
    if not os.path.isdir(pcap_root):
        logger.error(f"指定的PCAP根目录不存在或不是一个目录: {pcap_root}")
        return

    # 创建一个临时目录来存放中间结果
    temp_output_dir = os.path.join(output_dir, "temp_files")
    if os.path.exists(temp_output_dir):
        shutil.rmtree(temp_output_dir)
    os.makedirs(temp_output_dir)
    logger.info(f"创建临时目录: {temp_output_dir}")

    # 获取所有类别文件夹
    try:
        folders = [d for d in os.listdir(pcap_root) if os.path.isdir(os.path.join(pcap_root, d))]
    except FileNotFoundError:
        logger.error(f"无法访问PCAP根目录: {pcap_root}")
        return
        
    if not folders:
        logger.error(f"在 {pcap_root} 下没有找到任何类别子目录。")
        shutil.rmtree(temp_output_dir)
        return

    logger.info(f"找到 {len(folders)} 个类别: {folders}")

    # --- 并行处理 ---
    # 1. 准备任务列表
    tasks = []
    for label, folder_name in enumerate(folders):
        class_dir = os.path.join(pcap_root, folder_name)
        pcap_files = [f for f in os.listdir(class_dir) if f.endswith(('.pcap', '.pcapng'))]

        # 随机抽样pcap文件以降低内存压力
        random.shuffle(pcap_files)
        num_files_to_sample = int(len(pcap_files) * file_sample_rate)
        sampled_pcap_files = pcap_files[:num_files_to_sample]
        
        logger.info(f"类别 '{folder_name}': 找到 {len(pcap_files)} 个 pcap 文件, 将随机抽样其中的 {len(sampled_pcap_files)} 个 ({file_sample_rate*100:.0f}%) 进行处理。")

        for file_name in sampled_pcap_files:
            pcap_path = os.path.join(class_dir, file_name)
            tasks.append({'path': pcap_path, 'label': label})

    if not tasks:
        logger.error(f"在 {pcap_root} 的任何子目录中都未找到 .pcap 或 .pcapng 文件，或采样后任务列表为空。")
        shutil.rmtree(temp_output_dir)
        return

    # 2. 使用多进程池处理
    num_workers = max(1, multiprocessing.cpu_count() - 3)
    logger.info(f"使用 {num_workers} 个CPU核心进行并行处理...")
    
    # 使用 functools.partial 传递固定的 chunk_size 参数
    worker_func = partial(process_pcap_wrapper, chunk_size=chunk_size, temp_dir=temp_output_dir)

    with multiprocessing.Pool(processes=num_workers) as pool:
        # 使用 imap_unordered 以便通过 tqdm 显示进度条，并可能提高效率
        list(tqdm(pool.imap_unordered(worker_func, tasks), total=len(tasks), desc="处理PCAP文件"))

    logger.info("所有PCAP文件处理完成，开始合并临时文件...")

    # 3. 合并所有临时文件
    all_data = []
    all_labels = []
    temp_files = [os.path.join(temp_output_dir, f) for f in os.listdir(temp_output_dir) if f.endswith('.npz')]
    
    if not temp_files:
        logger.error("未能从任何pcap文件中提取任何数据。请检查pcap文件内容、采样率和路径。")
        shutil.rmtree(temp_output_dir)
        return

    for temp_file in tqdm(temp_files, desc="合并结果"):
        try:
            with np.load(temp_file) as data:
                all_data.append(data['x'])
                all_labels.append(data['y'])
        except Exception as e:
            logger.error(f"加载临时文件 {temp_file} 时出错: {e}")

    # 清理临时文件
    logger.info(f"正在清理临时目录: {temp_output_dir}")
    shutil.rmtree(temp_output_dir)


    if not all_data:
        logger.error("未能从任何pcap文件中提取任何数据。请检查pcap文件内容和路径。")
        return

    # 转换为Numpy数组
    X = np.vstack(all_data)
    y = np.concatenate(all_labels)
    
    logger.info(f"数据生成完毕。共生成 {len(X)} 个样本。")
    logger.info(f"类别分布: {np.unique(y, return_counts=True)}")

    # 分割训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_split, random_state=42, stratify=y)
    
    logger.info(f"训练集样本数: {len(X_train)}")
    logger.info(f"测试集样本数: {len(X_test)}")

    # 保存文件
    os.makedirs(output_dir, exist_ok=True)
    np.save(os.path.join(output_dir, 'train_x.npy'), X_train)
    np.save(os.path.join(output_dir, 'train_y.npy'), y_train)
    np.save(os.path.join(output_dir, 'test_x.npy'), X_test)
    np.save(os.path.join(output_dir, 'test_y.npy'), y_test)

    logger.info(f"数据集已成功保存到目录: {output_dir}")


def process_pcap_wrapper(task, temp_dir, chunk_size):
    """
    包装器函数，用于多进程池。
    接收一个任务字典，调用核心处理函数，并将结果保存到临时文件。
    """
    pcap_path = task['path']
    label = task['label']
    try:
        flow_chunks = process_single_pcap(pcap_path, chunk_size)
        if flow_chunks:
            # 为每个文件生成一个唯一的临时文件名
            temp_filename = os.path.join(temp_dir, f"{uuid.uuid4()}.npz")
            
            try:
                x_data = np.array(flow_chunks)
                y_data = np.array([label] * len(x_data))
                np.savez_compressed(temp_filename, x=x_data, y=y_data)
            except Exception as e:
                logger.error(f"保存临时文件 {temp_filename} 时出错: {e}")

    except Exception as e:
        logger.error(f"处理 pcap 文件 {os.path.basename(pcap_path)} 时发生未知错误: {e}", exc_info=False)


def main():
    parser = argparse.ArgumentParser(description="从PCAP文件生成特征数据集。")
    
    # 默认配置文件路径
    default_config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.yml')

    parser.add_argument('--config', type=str, default=default_config_path,
                        help=f'配置文件路径 (默认: {default_config_path})')

    # 解析一次以获取配置文件路径
    args, _ = parser.parse_known_args()

    # 从YAML文件加载配置
    try:
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f).get('data_preparation', {})
    except FileNotFoundError:
        print(f"警告: 配置文件 {args.config} 未找到，将使用代码默认值。")
        config = {}
    except yaml.YAMLError as e:
        print(f"警告: 解析配置文件 {args.config} 出错: {e}。将使用代码默认值。")
        config = {}

    # 重新设置parser，这次包含从配置文件加载的默认值
    parser = argparse.ArgumentParser(description="从PCAP文件生成特征数据集。",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    
    parser.add_argument('--pcap_root', type=str, default=config.get('pcap_root', './pcaps'),
                        help='包含类别子目录的PCAP文件的根目录。')
    parser.add_argument('--output_dir', type=str, default=config.get('output_dir', './data'),
                        help='保存生成的.npy文件的输出目录。')
    parser.add_argument('--chunk_size', type=int, default=config.get('chunk_size', 784),
                        help='每个流量样本的特征维度（字节数）。')
    parser.add_argument('--test_split', type=float, default=config.get('test_split', 0.2),
                        help='用于测试集的数据比例。')
    parser.add_argument('--file_sample_rate', type=float, default=config.get('file_sample_rate', 0.5),
                        help='对于每个类别，随机抽样处理的PCAP文件的比例。')

    args = parser.parse_args()

    generate_dataset(
        pcap_root=args.pcap_root,
        output_dir=args.output_dir,
        chunk_size=args.chunk_size,
        test_split=args.test_split,
        file_sample_rate=args.file_sample_rate
    )


if __name__ == '__main__':
    main() 