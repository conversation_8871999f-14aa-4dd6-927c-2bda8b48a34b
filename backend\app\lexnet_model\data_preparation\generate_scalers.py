# -*- coding: utf-8 -*-
"""
生成并保存全局数据缩放器 (Scaler)

功能:
- 加载完整的训练数据集。
- 计算用于归一化的全局 Min-Max 缩放器。
- 将缩放器对象保存到文件，以便在预测时重用。
"""
import os
import sys
import yaml
import numpy as np
import joblib
from sklearn import preprocessing
import logging

# --- 项目根目录设置 ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
LEXNET_ROOT = os.path.join(PROJECT_ROOT, 'lexnet_refactored')
sys.path.insert(0, LEXNET_ROOT)

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_and_save_scalers(config_path):
    """
    加载训练数据，计算并保存全局缩放器。

    Args:
        config_path (str): 主配置文件的路径。
    """
    # --- 1. 加载配置文件 ---
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"成功加载配置文件: {config_path}")
    except FileNotFoundError:
        logger.error(f"错误: 配置文件未找到 at '{config_path}'")
        sys.exit(1)

    # --- 2. 加载训练数据 ---
    data_dir = os.path.join(PROJECT_ROOT, config['data']['output_dir_refactored'])
    train_x_path = os.path.join(data_dir, 'train_x.npy')

    if not os.path.exists(train_x_path):
        logger.error(f"错误: 训练数据文件 'train_x.npy' 未在 '{data_dir}' 目录中找到。")
        sys.exit(1)

    logger.info(f"正在从 {train_x_path} 加载完整训练数据...")
    train_x = np.load(train_x_path)
    
    # 确保使用float32以匹配训练流程
    train_x_float = train_x.astype(np.float32)

    # --- 3. 计算缩放器 ---
    logger.info("正在计算全局缩放器...")
    
    # 分离大小和方向
    X_magnitude = np.abs(train_x_float)
    X_direction = np.sign(train_x_float)
    
    # 拟合缩放器
    scaler_magnitude = preprocessing.MinMaxScaler().fit(X_magnitude)
    scaler_direction = preprocessing.MinMaxScaler().fit(X_direction)
    
    logger.info("缩放器计算完成。")

    # --- 4. 保存缩放器 ---
    output_dir = os.path.join(data_dir, 'scalers')
    os.makedirs(output_dir, exist_ok=True)
    
    scaler_magnitude_path = os.path.join(output_dir, 'scaler_magnitude.joblib')
    scaler_direction_path = os.path.join(output_dir, 'scaler_direction.joblib')
    
    joblib.dump(scaler_magnitude, scaler_magnitude_path)
    joblib.dump(scaler_direction, scaler_direction_path)
    
    logger.info(f"Magnitude scaler 已保存至: {scaler_magnitude_path}")
    logger.info(f"Direction scaler 已保存至: {scaler_direction_path}")
    logger.info("\n任务完成！现在可以在预测脚本中加载这些缩放器了。")


def main():
    # 使用主配置文件
    config_path = os.path.join(LEXNET_ROOT, 'config', 'config.yml')
    generate_and_save_scalers(config_path)


if __name__ == '__main__':
    main() 