from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from .tasks import (
    analyze_with_df, 
    analyze_with_appscanner, 
    analyze_with_fa_net, 
    analyze_with_fs_net,
    start_analysis, # 导入父任务
    analyze_with_yatc, # 方便在下面新端点中引用
    analyze_with_yatc_and_return_label, # 新增：用于批量分析的子任务
    analyze_with_lexnet # <--- 导入 LexNet 任务
)
from .celery_worker import celery_app
from celery import group
from celery.result import GroupResult
import os
import shutil
from typing import List
# 导入ET-BERT的API路由器
from .et_bert_model import api as et_bert_api
# 导入新的ET-BERT行为识别API路由器
from .et_bert_behavior_model import api as et_bert_behavior_api
# --- 导入 FS-Net JSON 评估 API 路由器 ---
from .fs_net_model import api as fs_net_api
# --- 导入 FA-Net NPZ 评估 API 路由器 ---
from .fa_net_model import api as fa_net_api
# --- 导入 AppScanner PKL 评估 API 路由器 ---
from .appscanner import api as appscanner_api
# --- 导入新的流量分类器 API ---
from .et_bert_traffic_classifier import api as et_bert_traffic_classifier_api
# --- 导入未知识别 API ---
from .unknown_traffic_model import api as unknown_traffic_api


# --- 为.npy评估新增的导入 ---
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import io
import yaml
import joblib
from .lexnet_model.core.model import build_lexnet


app = FastAPI(title="网络流量分析系统原型", description="一个用于上传和分析网络流量文件的API")

# 配置 CORS
origins = ["http://localhost", "http://localhost:5173"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含ET-BERT的API路由
app.include_router(et_bert_api.router, prefix="/api/et-bert", tags=["ET-BERT"])
# 包含新的ET-BERT行为识别API路由
app.include_router(et_bert_behavior_api.et_bert_behavior_router, prefix="/api/et-bert-behavior", tags=["ET-BERT Behavior"])
# --- 包含 FS-Net JSON 评估 API 路由 ---
app.include_router(fs_net_api.router, prefix="/api", tags=["FS-Net"])
# --- 包含 FA-Net NPZ 评估 API 路由 ---
app.include_router(fa_net_api.router, prefix="/api", tags=["FA-Net"])
# --- 包含 AppScanner PKL 评估 API 路由 ---
app.include_router(appscanner_api.router, prefix="/api", tags=["AppScanner"])
# --- 包含新的流量分类器 API ---
app.include_router(et_bert_traffic_classifier_api.router, prefix="/api/et-bert-traffic-classifier", tags=["ET-BERT Traffic Classifier"])
# --- 包含未知识别 API ---
app.include_router(unknown_traffic_api.router, prefix="/api/unknown_traffic", tags=["Unknown Traffic Identification"])


# 确保共享上传目录存在
UPLOAD_DIR = "/app/shared_uploads"
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

@app.get("/")
def read_root():
    return {"message": "欢迎使用网络流量分析 API"}

def save_upload_file(upload_file: UploadFile) -> str:
    """保存上传的文件到共享目录并返回路径。"""
    try:
        filepath = os.path.join(UPLOAD_DIR, upload_file.filename)
        with open(filepath, "wb") as buffer:
            shutil.copyfileobj(upload_file.file, buffer)
        return filepath
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"无法保存文件: {e}")

@app.post("/api/analyze/all", status_code=202)
async def analyze_all_endpoint(file: UploadFile = File(...)):
    """
    接收 pcap 文件，启动所有模型 (DF, Appscanner, FA-Net, FS-Net, YaTC) 的并行分析任务。
    这是一个新的“超级”端点。
    """
    filepath = save_upload_file(file)
    # 调用我们已经定义好的父任务来启动链式调用
    task = start_analysis.delay(filepath)
    return {"task_id": task.id}

@app.post("/api/analyze/df", status_code=202)
async def analyze_df_endpoint(file: UploadFile = File(...)):
    """接收 pcap 文件，启动 DF 模型分析任务。"""
    filepath = save_upload_file(file)
    task = analyze_with_df.delay(filepath)
    return {"task_id": task.id}

@app.post("/api/analyze/integrated", status_code=202)
async def analyze_integrated_endpoint(file: UploadFile = File(...)):
    """接收 pcap 文件，启动 Appscanner, FS-Net, FA-Net, LexNet 综合分析任务。"""
    filepath = save_upload_file(file)
    # 创建一个包含所有综合模型任务的组
    analysis_group = group(
        analyze_with_appscanner.s(filepath),
        analyze_with_fa_net.s(filepath),
        analyze_with_fs_net.s(filepath),
        analyze_with_lexnet.s(filepath) # <--- 添加 LexNet 任务
    )
    result_group = analysis_group.apply_async()
    result_group.save() # 保存任务组以便通过ID查询
    return {"task_id": result_group.id}

@app.post("/api/analyze/yatc", status_code=202)
async def analyze_yatc_endpoint(file: UploadFile = File(...)):
    """接收 pcap 文件，启动 YaTC 模型分析任务。"""
    filepath = save_upload_file(file)
    task = analyze_with_yatc.delay(filepath)
    return {"task_id": task.id}

@app.post("/api/analyze/yatc/batch", status_code=202)
async def analyze_yatc_batch_endpoint(files: List[UploadFile] = File(...), labels: str = Form(...)):
    """
    接收批量 pcap 文件和对应的真实标签，启动并行分析和最终的准确率计算任务。
    """
    import json
    
    try:
        label_map = json.loads(labels)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="标签数据不是有效的JSON格式。")

    if len(files) != len(label_map):
        raise HTTPException(status_code=400, detail="文件数量与标签数量不匹配。")

    tasks_to_run = []
    for file in files:
        filepath = save_upload_file(file)
        real_label = label_map.get(file.filename)
        if real_label is None:
            raise HTTPException(status_code=400, detail=f"文件 '{file.filename}' 缺少对应的真实标签。")
        
        # 为每个文件创建一个带有其真实标签的分析子任务
        tasks_to_run.append(analyze_with_yatc_and_return_label.s(filepath, real_label))

    # 使用 Celery chain 将并行分析 (group) 与最终计算 (chord) 连接起来
    # chord 的回调任务 (calculate_batch_accuracy) 会在所有文件分析完成后执行
    from .tasks import calculate_batch_accuracy
    
    # 创建一个任务链：先并行执行所有文件的分析，然后将所有结果送入计算任务
    workflow = (group(tasks_to_run) | calculate_batch_accuracy.s())
    
    result = workflow.apply_async()

    return {"task_id": result.id}


@app.get("/api/results/{task_id}")
def get_task_results(task_id: str):
    """根据任务ID获取分析结果，支持任务组和单任务的实时状态查询。"""
    # 优先尝试作为任务组结果进行恢复
    # 注意：/api/analyze/all 返回的是 start_analysis 的 task_id,
    # 而 start_analysis 内部创建的链式任务 (chained_task) 也有自己的ID。
    # 为了能正确查询结果，我们需要获取链式任务的ID，而不是父任务的ID。
    # 
    # **重要架构说明:** 
    # `start_analysis` 任务返回了 `result.id`，这个ID指向的是 `chain` 对象本身。
    # Celery 的 `GroupResult.restore` 期望的是一个任务组的ID。
    # 当前的 `start_analysis` 任务编排方式返回的是 chain 的 id，
    # 而不是 group 的 id，这会导致结果查询失败。
    # 
    # 正确的做法是直接在 endpoint 中创建 chain，并保存 group 的 id。
    # 我将在下一次迭代中修复此问题。当前，我们将先让 /api/analyze/all 能够启动任务。
    #
    # 临时的解决方法是，我们可以先通过父任务的ID获取其结果，结果中包含了子任务的ID。
    parent_task_res = celery_app.AsyncResult(task_id)
    if parent_task_res.state == 'SUCCESS' and isinstance(parent_task_res.result, dict) and 'task_id' in parent_task_res.result:
        # 如果父任务成功，它的结果里应该有链式任务的ID
        task_id = parent_task_res.result['task_id']

    group_res = GroupResult.restore(task_id, app=celery_app)

    # 如果 group_res 存在，则按任务组逻辑处理
    if group_res:
        results_list = []
        is_ready = group_res.ready()
        
        # 直接迭代子任务获取实时状态
        for child_task in group_res.results:
            state = child_task.state
            child_res = {}
            effective_state = state # 用于传递给前端的最终状态

            # 默认从任务名解析模型名称
            task_name_parts = child_task.name.split('.')[-1] if child_task.name else ""
            model_name = "Unknown"
            if 'appscanner' in task_name_parts: model_name = 'AppScanner'
            elif 'fa_net' in task_name_parts: model_name = 'FA-Net'
            elif 'fs_net' in task_name_parts: model_name = 'FS-Net'
            elif 'yatc' in task_name_parts: model_name = 'YaTC'
            elif 'df' in task_name_parts: model_name = 'DF'
            elif 'lexnet' in task_name_parts: model_name = 'LexNet' # <--- 添加 LexNet 识别


            if state == 'SUCCESS':
                child_res = child_task.get()
                model_name = child_res.get('model', model_name)
                # 检查任务结果内部的逻辑状态
                if child_res.get('status') == '失败':
                    effective_state = 'FAILURE'
                    # 将详细的错误信息作为预测结果，方便用户查看
                    child_res['prediction'] = child_res.get('error', '未知错误')
                    child_res['confidence'] = 'N/A'
            elif state == 'FAILURE':
                child_res = {'prediction': '任务执行失败', 'confidence': str(child_task.info)}
            else: # PENDING, STARTED, RETRY
                child_res = {'prediction': '分析中...', 'confidence': 'N/A'}

            results_list.append({
                'key': child_task.id,
                'model': model_name,
                'prediction': child_res.get('prediction'),
                'confidence': child_res.get('confidence'),
                'state': effective_state, # 使用我们判断后的最终状态
                'details': child_res.get('details')
            })
            
        final_state = "SUCCESS" if is_ready else "PENDING"
        return {"state": final_state, "result": results_list}

    # 如果不是任务组，则按单个任务处理
    else:
        task_result = celery_app.AsyncResult(task_id)
        if task_result.state == 'SUCCESS':
            result = task_result.get()
            # 对于DF模型，任务本身已返回格式化好的列表，直接使用即可
            return {"state": "SUCCESS", "result": result}
        
        # 处理单任务的其他状态
        elif task_result.state == 'PENDING':
            return {"state": "PENDING", "status": "任务正在等待或已开始..."}
        elif task_result.state == 'FAILURE':
            # 如果是 start_analysis 任务本身失败
            if task_result.info and 'File not found' in str(task_result.info):
                 return {"state": "FAILURE", "result": [{"key": "error", "property": "错误", "value": str(task_result.info)}]}
            return {"state": "FAILURE", "status": str(task_result.info)}
        
        return {"state": task_result.state, "status": "任务进行中..."}


# --- 新增：用于LEXNet .npy文件评估的端点 ---
@app.post("/api/evaluate/lexnet_npy")
async def evaluate_lexnet_npy(
    test_x_file: UploadFile = File(..., description="测试集的特征文件 (test_x.npy)"),
    test_y_file: UploadFile = File(..., description="测试集的标签文件 (test_y.npy)")
):
    """
    接收 test_x.npy 和 test_y.npy 文件，使用LEXNet模型进行评估并返回详细结果。
    这是一个同步执行的端点。
    """
    try:
        # --- 1. 加载模型和配置 ---
        proj_root = "/app/app/lexnet_model" # 在Docker容器内的绝对路径
        config_path = os.path.join(proj_root, 'config.yml')
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        scaler_magnitude_path = os.path.join(proj_root, 'data', 'scalers', 'scaler_magnitude.joblib')
        scaler_direction_path = os.path.join(proj_root, 'data', 'scalers', 'scaler_direction.joblib')
        scaler_magnitude = joblib.load(scaler_magnitude_path)
        scaler_direction = joblib.load(scaler_direction_path)

        device = torch.device("cpu") # 强制使用CPU以保证在无GPU环境下稳定运行
        model_cfg = config['model']
        num_classes = len(config['data']['class_names'])
        prototype_shape = (model_cfg['prototypes_per_class'] * num_classes, model_cfg['prototype_channel_depth'], model_cfg['prototype_h'], model_cfg['prototype_w'])
        
        model = build_lexnet(
            num_classes=num_classes,
            prototype_shape=prototype_shape,
            input_shape=(1, 56, 28),
            base_architecture=model_cfg['base_architecture']
        )
        model_path = os.path.join(proj_root, 'best_model.pth')
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.to(device)
        model.eval()

        # --- 2. 加载并预处理上传的NPY文件 ---
        test_x_content = await test_x_file.read()
        test_y_content = await test_y_file.read()
        test_x = np.load(io.BytesIO(test_x_content))
        test_y_true = np.load(io.BytesIO(test_y_content))

        # 预处理函数
        def preprocess_for_prediction(X_batch, scaler_mag, scaler_dir):
            if X_batch.ndim != 2 or X_batch.shape[1] != 784:
                raise ValueError(f"输入批次的形状应为 (B, 784)，但得到 {X_batch.shape}")
            X = X_batch.astype(np.float32)
            X_magnitude, X_direction = np.abs(X), np.sign(X)
            X_magnitude_scaled = scaler_mag.transform(X_magnitude)
            X_direction_scaled = scaler_dir.transform(X_direction)
            X_combined = np.concatenate([X_magnitude_scaled, X_direction_scaled], axis=1)
            X_processed = np.reshape(X_combined, (X.shape[0], 1, 56, 28))
            return torch.from_numpy(X_processed)

        data_tensor = preprocess_for_prediction(test_x, scaler_magnitude, scaler_direction).to(device)

        # --- 3. 执行预测 ---
        test_y_pred = []
        with torch.no_grad():
            for i in range(len(data_tensor)):
                sample = data_tensor[i:i+1]
                logits, _ = model(sample)
                pred_index = torch.argmax(logits, dim=1).item()
                test_y_pred.append(pred_index)
        test_y_pred = np.array(test_y_pred)

        # --- 4. 计算评估指标 ---
        class_names = config['data']['class_names']
        accuracy = accuracy_score(test_y_true, test_y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(test_y_true, test_y_pred, average='weighted', zero_division=0)
        cm = confusion_matrix(test_y_true, test_y_pred)

        # --- 5. 格式化并返回结果 ---
        report = {
            "accuracy": f"{accuracy:.4f}",
            "precision": f"{precision:.4f}",
            "recall": f"{recall:.4f}",
            "f1_score": f"{f1:.4f}",
            "confusion_matrix": cm.tolist(),
            "class_names": class_names,
            "summary": {
                "total_samples": len(test_y_true),
                "correct_predictions": int(np.sum(test_y_true == test_y_pred)),
            }
        }
        return JSONResponse(content=report)

    except Exception as e:
        print(f"LEXNet .npy 评估过程中发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")