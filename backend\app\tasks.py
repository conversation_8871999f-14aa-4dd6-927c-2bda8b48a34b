import time
from celery import group, chain
from celery.exceptions import SoftTimeLimitExceeded
from .celery_worker import celery_app
from .inference import predict_df, predict_appscanner, predict_fa_net, predict_lexnet # <--- 从 inference 导入 predict_lexnet
from .fs_net_model.inference import predict as predict_fs_net
from .yatc_model.inference import predict_yatc
from celery import Celery
from celery.exceptions import Ignore
# 导入 ET-BERT 微调函数
from .et_bert_model.finetune import run_finetuning
from .et_bert_model.preprocessor import run_preprocessing_logic
from pathlib import Path
import os

# --- 新增：导入未知识别模块的任务 ---
from .unknown_traffic_model.tasks import extract_features_task, run_prediction_task

@celery_app.task(name='tasks.finetune_et_bert_task', bind=True)
def finetune_et_bert_task(self, task_dir_str: str):
    """
    一个用于执行 ET-BERT 模型微调的 Celery 后台任务。
    它现在接收一个包含数据集的目录路径作为直接输入。
    """
    task_dir = Path(task_dir_str)
    
    def update_status_callback(message: str, progress: str):
        """用于从训练脚本向Celery传递状态更新的回调函数。"""
        self.update_state(state='PROGRESS', meta={'message': message, 'progress': progress})

    print(f"[Celery Worker] Starting ET-BERT finetuning for task_id: {self.request.id}")
    
    # 将 self.request.id (即 task_id) 传给微调函数
    try:
        def update_progress(progress, message):
            self.update_state(state='PROGRESS', meta={'progress': progress, 'message': message})

        result = run_finetuning(
            task_id=self.request.id,
            task_dir=task_dir,
            update_status_callback=update_progress
        )
        return result
    except Exception as e:
        # Log the exception
        return {'status': 'failed', 'error': str(e)}

@celery_app.task(bind=True)
def analyze_with_df(self, filepath: str):
    """Celery task to analyze with DF-Master model."""
    print(f"Worker: Starting DF-Master analysis for {filepath}")
    raw_result = predict_df(filepath)
    print(f"Worker: DF-Master analysis finished for {filepath}")
    
    # Check for failure first
    if raw_result.get("status") == "失败":
        return [
            {"key": "1", "property": "模型状态", "value": "失败"},
            {"key": "2", "property": "错误信息", "value": raw_result.get("error")}
        ]

    # Format successful result for the frontend table and chart
    formatted_result = [
        {"key": "1", "property": "预测类别", "value": raw_result.get("prediction")},
        {"key": "2", "property": "置信度", "value": f"{raw_result.get('confidence', 0):.2%}"},
        # Add a non-displayable item to carry chart data
        {"key": "chart", "details": raw_result.get("details")}
    ]
    
    return formatted_result

@celery_app.task(bind=True)
def analyze_with_appscanner(self, filepath: str):
    """Celery task to analyze with AppScanner model."""
    print(f"Worker: Starting AppScanner analysis for {filepath}")
    result = predict_appscanner(filepath)
    print(f"Worker: AppScanner analysis finished for {filepath}")
    result['task_id'] = self.request.id
    return result

@celery_app.task(bind=True, soft_time_limit=60) # 改为软超时并绑定实例
def analyze_with_yatc(self, filepath: str):
    """Celery task to analyze with YaTC model."""
    print(f"Worker: Starting YaTC analysis for {filepath}")
    try:
        raw_result = predict_yatc(filepath)
        print(f"Worker: YaTC analysis finished for {filepath}")

        if raw_result.get("status") == "失败":
            return {
                "model": "YaTC",
                "status": "失败",
                "error": raw_result.get("error")
            }
        
        # 将结果格式化为包含详情的字典，以适应前端
        # YaTCPage.jsx 的轮询逻辑会直接使用这个结果对象
        return {
            "model": "YaTC",
            "status": "成功",
            "prediction": raw_result.get("prediction"),
            "confidence": raw_result.get("confidence"),
            "details": raw_result.get("details", {}) # 确保details总是存在
        }

    except SoftTimeLimitExceeded:
        print("Worker: YaTC analysis exceeded soft time limit (60s).")
        return {
            "task_id": self.request.id,
            "model": "YaTC",
            "status": "失败",
            "error": "分析超时 (超过60秒)"
        }

@celery_app.task(bind=True)
def analyze_with_yatc_and_return_label(self, filepath: str, real_label: str):
    """
    分析单个文件并附带返回其真实标签，用于批量处理流程。
    """
    print(f"Worker: Starting YaTC analysis for {filepath} with real label {real_label}")
    # 直接调用我们已有的单文件预测函数
    result = predict_yatc(filepath)
    # 将真实标签附加到结果中，以便后续计算
    result['real_label'] = real_label
    return result

@celery_app.task(bind=True)
def calculate_batch_accuracy(self, results: list):
    """
    接收一组分析结果，计算并返回详细的准确率报告。
    """
    if not results:
        return {"status": "FAILURE", "error": "没有收到任何分析结果。"}

    class_stats = {}
    total_files = len(results)
    total_correct = 0

    # 初始化统计字典
    for res in results:
        real_label = res.get('real_label')
        if real_label and real_label not in class_stats:
            class_stats[real_label] = {'total': 0, 'correct': 0}

    # 遍历结果进行统计
    for res in results:
        real_label = res.get('real_label')
        predicted_label = res.get('prediction')
        status = res.get('status')

        if not real_label:
            continue

        if status == '成功':
            class_stats[real_label]['total'] += 1
            if real_label == predicted_label:
                class_stats[real_label]['correct'] += 1
                total_correct += 1
    
    # 计算准确率并格式化输出
    report = []
    for label, stats in class_stats.items():
        accuracy = (stats['correct'] / stats['total']) * 100 if stats['total'] > 0 else 0
        report.append({
            "key": label,
            "category": label,
            "correct_predictions": stats['correct'],
            "total_samples": stats['total'],
            "accuracy": f"{accuracy:.2f}%"
        })

    overall_accuracy = (total_correct / total_files) * 100 if total_files > 0 else 0
    
    summary = {
        "total_files_analyzed": total_files,
        "total_correct_predictions": total_correct,
        "overall_accuracy": f"{overall_accuracy:.2f}%"
    }

    return {"status": "SUCCESS", "summary": summary, "report": report}

@celery_app.task(bind=True)
def analyze_with_fa_net(self, filepath: str):
    """Celery task to analyze with FA-Net model."""
    print(f"Worker: Starting FA-Net analysis for {filepath}")
    result = predict_fa_net(filepath)
    print(f"Worker: FA-Net raw result: {result}")
    print(f"Worker: FA-Net analysis finished for {filepath}")
    result['task_id'] = self.request.id
    return result

@celery_app.task(bind=True)
def analyze_with_fs_net(self, filepath: str):
    """Celery task to analyze with FS-Net model."""
    print(f"Worker: Starting FS-Net analysis for {filepath}")
    # predict_fs_net now returns a dictionary with the final aggregated result
    raw_result = predict_fs_net(filepath)
    print(f"Worker: FS-Net analysis finished for {filepath}")

    # Adapt the new result format to the expected format
    if raw_result and raw_result.get("status") == "success":
        final_prediction = raw_result.get("final_prediction", {})
        # This is a mapping from your model's class indices to names.
        # You need to verify and complete this mapping based on your model's training.
        class_mapping = {
            0: "勒索软件",
            1: "恶意文件",
            2: "扫描探测",
            3: "木马流量",
            4: "致瘫攻击",
            5: "良性",
            6: "隐蔽传输",
            7: "高危漏洞"
        }
        # Safely access dictionary keys
        class_idx = final_prediction.get("class_idx") if isinstance(final_prediction, dict) else None
        prediction_name = "未知类别"
        if class_idx is not None:
            prediction_name = class_mapping.get(class_idx, "未知类别")
        
        confidence = final_prediction.get("confidence") if isinstance(final_prediction, dict) else None

        return {
            "task_id": self.request.id,
            "model": "FS-Net",
            "status": "成功",
            "prediction": prediction_name,
            "confidence": confidence
        }
    else:
        # Safely access error message
        error_msg = raw_result.get("error") if isinstance(raw_result, dict) else "未知错误"
        return {
            "task_id": self.request.id,
            "model": "FS-Net",
            "status": "失败",
            "error": error_msg
        }

@celery_app.task(bind=True)
def analyze_with_lexnet(self, filepath: str):
    """Celery task to analyze with LexNet model."""
    print(f"Worker: Starting LexNet analysis for {filepath}")
    # 现在调用 inference.py 中的包装器函数，它会返回正确格式的数据
    result = predict_lexnet(filepath)
    print(f"Worker: LexNet analysis finished for {filepath}")
    
    # 结果已经包含了 model, status, prediction, confidence, details 等字段
    # 我们只需将 task_id 添加进去即可
    if isinstance(result, dict):
        result['task_id'] = self.request.id
    
    return result

@celery_app.task(bind=True)
def finalize_group(self, results):
    """
    收集所有并行的分析任务的结果，并作为最终结果。
    """
    final_results = {}
    for res in results:
        # Add a check to ensure 'res' is a dictionary before calling .get()
        if isinstance(res, dict) and res.get('model'):
            final_results[res.get('model')] = res
    
    return {'status': 'COMPLETED', 'results': final_results}

@celery_app.task(bind=True)
def start_analysis(self, filepath: str):
    """
    一个父任务，用于编排分析子任务。
    """
    if not os.path.exists(filepath):
        return {'status': 'FAILED', 'result': f'File not found at worker: {filepath}'}

    # The group of tasks to run in parallel.
    analysis_group = group(
        analyze_with_df.s(filepath),
        analyze_with_appscanner.s(filepath),
        analyze_with_fa_net.s(filepath),
        analyze_with_fs_net.s(filepath),
        analyze_with_yatc.s(filepath) # Add YaTC to the group
    )

    # The chain that executes the group and then the finalizer.
    chained_task = chain(analysis_group, finalize_group.s())
    result = chained_task.apply_async()
    
    return {'task_id': result.id} 

@celery_app.task(name='tasks.preprocess_et_bert_data_task', bind=True)
def preprocess_et_bert_data_task(self, task_dir_str: str):
    """
    Celery task to run the pcap to tsv preprocessing.
    """
    try:
        # 更新初始状态
        self.update_state(state='PROGRESS', meta={'message': '预处理任务已开始...', 'progress': '0%'})
        
        # 定义进度回调
        def update_progress(percent, message):
            self.update_state(state='PROGRESS', meta={'progress': f'{percent:.0f}%', 'message': message})
        
        # 调用核心处理逻辑
        run_preprocessing_logic(task_dir_str, update_progress)
        
        # 构造正确的返回字典，以符合下一个任务的期望
        dataset_output_path = os.path.join(task_dir_str, 'dataset')
        
        # 返回包含数据集路径的字典
        return {
            'status': 'completed',
            'dataset_path': dataset_output_path
        }
    except Exception as e:
        # 记录异常
        return {'status': 'failed', 'error': str(e)} 