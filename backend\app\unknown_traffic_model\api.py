
import os
import uuid
import re
import shutil
from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import FileResponse, JSONResponse

# Import celery tasks
from .tasks import extract_features_task, run_prediction_task, extract_contrastive_features_task, perform_clustering_task
from ..celery_worker import celery_app

# Define the router
router = APIRouter()

# Use absolute paths within the container/app context, following project conventions.
UPLOAD_FOLDER = '/app/shared_uploads/unknown_traffic_uploads'
GENERATED_FOLDER = '/app/app/unknown_traffic_model/generated_files'

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(GENERATED_FOLDER, exist_ok=True)

_filename_unsafe_chars = re.compile(r'[^\w.\u4e00-\u9fa5-]')

def secure_filename_custom(filename: str) -> str:
    """
    A simplified, dependency-free version of Werkzeug's secure_filename.
    It removes unsafe characters and path components from a filename.
    Now supports Chinese characters.
    """
    filename = os.path.basename(filename)
    filename = _filename_unsafe_chars.sub('', filename).strip('._')
    return filename

async def save_upload_file_stream(upload_file: UploadFile, destination: str):
    """Saves an uploaded file to a destination by streaming."""
    try:
        with open(destination, "wb") as buffer:
            shutil.copyfileobj(upload_file.file, buffer)
    finally:
        upload_file.file.close()

@router.post("/extract_features", status_code=202)
async def extract_features(file: UploadFile = File(...)):
    if not file.filename.endswith('.zip'):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload a .zip file.")

    session_id = str(uuid.uuid4())
    safe_filename = secure_filename_custom(file.filename)
    zip_filename = f"{session_id}_{safe_filename}"
    zip_path = os.path.join(UPLOAD_FOLDER, zip_filename)
    
    await save_upload_file_stream(file, zip_path)

    output_tsv_filename = f"{session_id}_features.tsv"
    output_tsv_path = os.path.join(GENERATED_FOLDER, output_tsv_filename)
    
    task = extract_features_task.delay(zip_path, output_tsv_path)
    
    return {"task_id": task.id}

@router.post("/predict", status_code=202)
async def predict(file: UploadFile = File(...)):
    if not file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="Please upload a valid .tsv file.")

    session_id = str(uuid.uuid4())
    safe_filename = secure_filename_custom(file.filename)
    input_tsv_filename = f"{session_id}_{safe_filename}"
    input_tsv_path = os.path.join(UPLOAD_FOLDER, input_tsv_filename)

    await save_upload_file_stream(file, input_tsv_path)

    unknown_output_filename = f"{session_id}_unknown_traffic.tsv"
    unknown_output_path = os.path.join(GENERATED_FOLDER, unknown_output_filename)

    task = run_prediction_task.delay(input_tsv_path, unknown_output_path)
    
    return {"task_id": task.id}

@router.get("/results/{task_id}")
async def get_task_status(task_id: str):
    """Polls for the status of a Celery task."""
    task_result = celery_app.AsyncResult(task_id)
    
    response_data = {
        "task_id": task_id,
        "state": task_result.state,
        "result": None
    }

    if task_result.state == 'PENDING':
        response_data['result'] = {'status': '任务正在等待队列中...'}
    elif task_result.state == 'PROGRESS':
        response_data['result'] = task_result.info # .info contains the meta data from update_state
    elif task_result.state == 'SUCCESS':
        result = task_result.get()
        # Add download urls to the final result if applicable
        if 'filename' in result:
             result['download_url'] = f"/api/unknown_traffic/download/{result['filename']}"
        if 'unknown_samples_file' in result and result['unknown_samples_file']:
             result['unknown_download_url'] = f"/api/unknown_traffic/download/{result['unknown_samples_file']}"
        response_data['result'] = result
    elif task_result.state == 'FAILURE':
        response_data['result'] = {
            'status': '任务执行失败',
            'error': str(task_result.info) # .info contains the exception
        }

    return JSONResponse(content=response_data)

@router.post("/extract_contrastive_features", status_code=202)
async def extract_contrastive_features(file: UploadFile = File(...)):
    """提取对比学习特征"""
    if not file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="Please upload a valid .tsv file.")

    session_id = str(uuid.uuid4())
    safe_filename = secure_filename_custom(file.filename)
    input_tsv_filename = f"{session_id}_{safe_filename}"
    input_tsv_path = os.path.join(UPLOAD_FOLDER, input_tsv_filename)

    await save_upload_file_stream(file, input_tsv_path)

    features_output_filename = f"{session_id}_contrastive_features.tsv"
    features_output_path = os.path.join(GENERATED_FOLDER, features_output_filename)

    task = extract_contrastive_features_task.delay(input_tsv_path, features_output_path)

    return {"task_id": task.id}

@router.post("/perform_clustering", status_code=202)
async def perform_clustering(file: UploadFile = File(...)):
    """执行聚类分析"""
    if not file.filename.endswith('.tsv'):
        raise HTTPException(status_code=400, detail="Please upload a valid .tsv file with features.")

    session_id = str(uuid.uuid4())
    safe_filename = secure_filename_custom(file.filename)
    input_features_filename = f"{session_id}_{safe_filename}"
    input_features_path = os.path.join(UPLOAD_FOLDER, input_features_filename)

    await save_upload_file_stream(file, input_features_path)

    clustered_output_filename = f"{session_id}_clustered_results.tsv"
    clustered_output_path = os.path.join(GENERATED_FOLDER, clustered_output_filename)

    # 使用指定的参数：min_cluster_size=25, umap_neighbors=5, umap_dims=64
    task = perform_clustering_task.delay(input_features_path, clustered_output_path,
                                       umap_dims=64, umap_neighbors=5, min_cluster_size=25)

    return {"task_id": task.id}

@router.get("/download/{filename}")
async def download_file(filename: str):
    file_path = os.path.join(GENERATED_FOLDER, filename)
    if os.path.exists(file_path):
        return FileResponse(path=file_path, filename=filename, media_type='application/octet-stream')
    raise HTTPException(status_code=404, detail="File not found.")