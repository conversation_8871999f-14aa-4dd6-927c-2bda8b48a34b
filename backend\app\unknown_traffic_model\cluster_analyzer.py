#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚类分析器
基于UMAP降维和HDBSCAN聚类的未知流量聚类分析
"""

import os
import numpy as np
import pandas as pd
from sklearn.metrics import adjusted_rand_score, silhouette_score
from collections import Counter
import ast
import random

def set_seed(seed=42):
    """设置所有随机种子以确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    print(f"聚类随机种子已设置为: {seed}")

def calculate_purity(y_true, y_pred):
    """
    计算聚类结果的纯度分数
    """
    contingency_matrix = pd.crosstab(y_pred, y_true)
    return np.sum(np.amax(contingency_matrix.values, axis=1)) / np.sum(contingency_matrix.values)

def perform_clustering_analysis(input_features_path, output_clustered_path,
                               umap_dims=64, umap_neighbors=5, min_cluster_size=25):
    """
    执行聚类分析
    
    Args:
        input_features_path: 包含特征的TSV文件路径
        output_clustered_path: 输出聚类结果的TSV文件路径
        umap_dims: UMAP降维目标维度
        umap_neighbors: UMAP邻居数量
        min_cluster_size: HDBSCAN最小聚类大小
    
    Returns:
        dict: 聚类结果统计信息
    """
    # 设置随机种子
    set_seed(42)
    
    # 动态导入，避免在系统启动时就要求这些依赖
    try:
        import umap
        import hdbscan
    except ImportError as e:
        error_msg = f"Required clustering libraries not found: {e}. Please install the following packages:\n"
        error_msg += "pip install umap-learn hdbscan\n"
        error_msg += "Note: These packages require additional system dependencies and may take time to install."
        raise ImportError(error_msg)
    
    print("Loading data from combined TSV file...")
    try:
        # 读取TSV文件
        combined_data = pd.read_csv(input_features_path, sep='\t')
        
        # 提取嵌入特征并从字符串表示转换为浮点数列表
        # 使用 ast.literal_eval 安全解析字符串表示的列表
        features = np.array(combined_data['embedding'].apply(ast.literal_eval).tolist())
        
        # 数据集的其余部分用于真实标签和其他信息
        dataset = combined_data

    except FileNotFoundError as e:
        raise FileNotFoundError(f"Error loading file: {e}. Please check the path.")
    except KeyError as e:
        raise KeyError(f"Error: Required column missing. {e}. Ensure 'embedding' column exists in {input_features_path}")
    except Exception as e:
        raise Exception(f"An unexpected error occurred during data loading: {e}")

    # 提取真实标签用于评估
    true_labels = None
    if "original_ground_truth_name" in dataset.columns:
        true_labels = dataset['original_ground_truth_name']
        print("Found 'original_ground_truth_name' column for evaluation (for Purity/ARI).")
    elif "true_label" in dataset.columns:
        true_labels = dataset['true_label']
        print("Found 'true_label' column for evaluation (for Purity/ARI). Note: This is the binary label (0/1).")
    else:
        print("Warning: No suitable ground truth column ('original_ground_truth_name' or 'true_label') found in the dataset. Cannot calculate purity/ARI.")
        # 我们仍然可以继续聚类，只是没有纯度评估
            
    print(f"Loaded {len(features)} unknown samples for clustering.")
    if true_labels is not None:
        print(f"Number of unique true labels in the set: {true_labels.nunique()}")

    # UMAP 降维
    print(f"\nStep 1: Performing UMAP dimensionality reduction from {features.shape[1]} to {umap_dims} dimensions...")
    reducer = umap.UMAP(
        n_neighbors=umap_neighbors,
        n_components=umap_dims,
        min_dist=0.0,  # 紧密打包点
        random_state=42,
        metric='euclidean'  # 标准距离度量
    )
    embedding = reducer.fit_transform(features)
    print("UMAP reduction complete. New data shape:", embedding.shape)

    # HDBSCAN 聚类
    print(f"\nStep 2: Performing HDBSCAN clustering with min_cluster_size={min_cluster_size}...")
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=min_cluster_size,
        gen_min_span_tree=True
    )
    clusterer.fit(embedding)
    cluster_labels = clusterer.labels_

    # 保存带有聚类标签的结果
    print(f"\nSaving dataset with cluster labels to {output_clustered_path}...")
    output_df = dataset.copy()
    output_df['cluster_label'] = cluster_labels
    
    # 如果输出目录不存在则创建
    output_dir = os.path.dirname(output_clustered_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        
    output_df.to_csv(output_clustered_path, sep='\t', index=False)
    print("Save complete.")

    # 评估聚类结果
    print("\nEvaluating clustering results...")
    
    # 计算聚类数量，忽略噪声点
    n_clusters_ = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
    n_noise_ = list(cluster_labels).count(-1)

    print(f'Estimated number of clusters: {n_clusters_}')
    print(f'Estimated number of noise points: {n_noise_}')

    print("\n--- Clustering Evaluation Metrics ---")
    
    # 计算纯度和ARI（如果有真实标签）
    purity = None
    ari = None
    if true_labels is not None:
        # 对于HDBSCAN，过滤掉噪声点进行纯度计算
        non_noise_indices = cluster_labels != -1
        if np.sum(non_noise_indices) > 0:
            purity = calculate_purity(true_labels.to_numpy()[non_noise_indices], cluster_labels[non_noise_indices])
            ari = adjusted_rand_score(true_labels.to_numpy()[non_noise_indices], cluster_labels[non_noise_indices])
        else:
            purity, ari = 0, 0  # 没有找到聚类

        print(f"Purity Score: {purity:.4f}")
        print(f"Adjusted Rand Index (ARI): {ari:.4f}")

    # 轮廓系数只能在找到超过1个聚类时计算
    silhouette = None
    if n_clusters_ > 1:
        try:
            # 我们在降维后的UMAP嵌入上计算轮廓系数，以获得更好的性能和相关性
            silhouette = silhouette_score(embedding, cluster_labels)
            print(f"Silhouette Score (on UMAP embedding): {silhouette:.4f}")
        except ValueError as e:
            print(f"Could not compute Silhouette Score: {e}")
    elif n_clusters_ == 1:
        print("Silhouette Score is not defined for a single cluster.")
    else:  # n_clusters_ == 0
        print("No clusters found. Cannot compute Silhouette Score.")

    print("-------------------------------------\n")
    
    print("Cluster distribution:")
    cluster_counts = Counter(cluster_labels)
    # 首先打印噪声
    if -1 in cluster_counts:
        print(f"  Noise (-1): {cluster_counts[-1]:>5} samples")
    # 然后打印实际聚类，按顺序排列
    for i in sorted([k for k in cluster_counts.keys() if k != -1]):
        print(f"  Cluster {i}: {cluster_counts.get(i, 0):>5} samples")

    # 为散点图准备数据：使用UMAP降维到2D用于可视化
    try:
        import umap
        # 降维到2D用于可视化
        umap_2d = umap.UMAP(n_components=2, random_state=42, n_neighbors=min(15, len(features)-1))
        coords_2d = umap_2d.fit_transform(features)

        # 准备散点图数据
        scatter_data = []
        for i, (coord, cluster_id) in enumerate(zip(coords_2d, cluster_labels)):
            point_data = {
                'x': float(coord[0]),
                'y': float(coord[1]),
                'cluster_id': int(cluster_id),
                'index': int(i)
            }

            # 添加SNI信息（如果存在）
            if 'raw_sni' in dataset.columns:
                point_data['sni'] = str(dataset.iloc[i]['raw_sni']) if pd.notna(dataset.iloc[i]['raw_sni']) else 'N/A'

            # 添加真实标签信息（如果存在）
            if 'original_ground_truth_name' in dataset.columns:
                point_data['true_label'] = str(dataset.iloc[i]['original_ground_truth_name']) if pd.notna(dataset.iloc[i]['original_ground_truth_name']) else 'Unknown'

            # 添加原始文本信息（如果存在）
            if 'original_text_a' in dataset.columns:
                text_preview = str(dataset.iloc[i]['original_text_a'])[:50] + '...' if len(str(dataset.iloc[i]['original_text_a'])) > 50 else str(dataset.iloc[i]['original_text_a'])
                point_data['text_preview'] = text_preview

            scatter_data.append(point_data)

        print(f"Generated {len(scatter_data)} scatter points for visualization")

    except Exception as e:
        print(f"Warning: Could not generate 2D coordinates for visualization: {e}")
        scatter_data = []

    # 准备返回结果
    results = {
        'n_clusters': int(n_clusters_),
        'n_noise': int(n_noise_),
        'total_samples': int(len(features)),
        'clustered_file': os.path.basename(output_clustered_path),
        'cluster_distribution': {str(k): int(v) for k, v in cluster_counts.items()},
        'scatter_data': scatter_data  # 新增：散点图数据
    }

    if purity is not None:
        results['purity'] = float(purity)
    if ari is not None:
        results['ari'] = float(ari)
    if silhouette is not None:
        results['silhouette'] = float(silhouette)
    
    return results

def analyze_cluster_details(clustered_file_path):
    """
    分析聚类详细信息
    
    Args:
        clustered_file_path: 包含聚类结果的TSV文件路径
    
    Returns:
        dict: 详细的聚类分析结果
    """
    try:
        df = pd.read_csv(clustered_file_path, sep='\t')
        
        # 基本统计
        total_samples = len(df)
        unique_clusters = df['cluster_label'].nunique()
        noise_samples = len(df[df['cluster_label'] == -1])
        
        # 每个聚类的详细信息
        cluster_details = []
        for cluster_id in sorted(df['cluster_label'].unique()):
            cluster_data = df[df['cluster_label'] == cluster_id]
            cluster_size = len(cluster_data)
            
            cluster_info = {
                'cluster_id': int(cluster_id),
                'size': int(cluster_size),
                'percentage': float((cluster_size / total_samples) * 100)
            }

            # 如果有真实标签，分析聚类的组成
            if 'original_ground_truth_name' in df.columns:
                true_labels_in_cluster = cluster_data['original_ground_truth_name'].value_counts()
                cluster_info['true_label_distribution'] = {str(k): int(v) for k, v in true_labels_in_cluster.to_dict().items()}
                cluster_info['dominant_label'] = str(true_labels_in_cluster.index[0]) if len(true_labels_in_cluster) > 0 else None
                cluster_info['purity'] = float(true_labels_in_cluster.iloc[0] / cluster_size) if len(true_labels_in_cluster) > 0 else 0.0
            
            cluster_details.append(cluster_info)
        
        return {
            'total_samples': int(total_samples),
            'unique_clusters': int(unique_clusters),
            'noise_samples': int(noise_samples),
            'cluster_details': cluster_details
        }
        
    except Exception as e:
        raise Exception(f"Error analyzing cluster details: {e}")

if __name__ == "__main__":
    # 测试代码
    print("Cluster analyzer module loaded successfully.")
