#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比学习特征提取器
基于ET-BERT的对比学习模型，用于提取高质量特征
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import LabelEncoder
from collections import Counter
import random

# 确保本地 'uer' 模块在 python 路径中
module_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, module_dir)

# 移除可能冲突的路径
paths_to_remove = []
for path in sys.path:
    if 'et_bert_model' in path or 'et_bert_behavior_model' in path or 'et_bert_traffic_classifier' in path:
        paths_to_remove.append(path)
for path in paths_to_remove:
    sys.path.remove(path)

# 导入本地uer模块
from uer.layers.embeddings import WordEmbedding, WordPosEmbedding, WordPosSegEmbedding, WordSinusoidalposEmbedding
from uer.encoders.transformer_encoder import TransformerEncoder
from uer.encoders.rnn_encoder import RnnEncoder, LstmEncoder, GruEncoder, BirnnEncoder, BilstmEncoder, BigruEncoder
from uer.encoders.cnn_encoder import GatedcnnEncoder
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils.config import load_hyperparam
from uer.model_loader import load_model
from uer.utils.tokenizers import BertTokenizer, CharTokenizer, SpaceTokenizer

# 创建本地映射字典
str2embedding = {"word": WordEmbedding, "word_pos": WordPosEmbedding, "word_pos_seg": WordPosSegEmbedding,
                 "word_sinusoidalpos": WordSinusoidalposEmbedding}
str2encoder = {"transformer": TransformerEncoder, "rnn": RnnEncoder, "lstm": LstmEncoder,
               "gru": GruEncoder, "birnn": BirnnEncoder, "bilstm": BilstmEncoder, "bigru": BigruEncoder,
               "gatedcnn": GatedcnnEncoder}
str2tokenizer = {"bert": BertTokenizer, "char": CharTokenizer, "space": SpaceTokenizer}

def set_seed(seed=42):
    """设置所有随机种子以确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

class Classifier(nn.Module):
    """基础分类器类"""
    def __init__(self, args):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.tokenizer.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, self.labels_num)

    def forward(self, src, seg):
        emb = self.embedding(src, seg)
        output = self.encoder(emb, seg)
        
        if self.pooling == "mean":
            pooled_output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            pooled_output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            pooled_output = output[:, -1, :]
        else: # first
            pooled_output = output[:, 0, :]
            
        output_for_classification = torch.tanh(self.output_layer_1(pooled_output))
        logits = self.output_layer_2(output_for_classification)
        return logits

class ImprovedContrastiveBERT(Classifier):
    """
    改进的对比学习模型
    """
    def __init__(self, args, projection_dim=64):
        super(ImprovedContrastiveBERT, self).__init__(args)
        
        # 更小的投影头，减少过拟合
        self.projection_head = nn.Sequential(
            nn.Linear(args.hidden_size, projection_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(projection_dim * 2, projection_dim),
            nn.LayerNorm(projection_dim)
        )

    def forward(self, src, tgt, seg, return_features=False):
        # ET-BERT特征提取
        emb = self.embedding(src, seg)
        output = self.encoder(emb, seg)
        
        # 池化
        if self.pooling == "mean":
            pooled_output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            pooled_output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            pooled_output = output[:, -1, :]
        else:  # first (CLS token)
            pooled_output = output[:, 0, :]
        
        # 投影
        projected_features = self.projection_head(pooled_output)
        
        if return_features:
            return pooled_output, projected_features
        return projected_features

class InfoNCELoss(nn.Module):
    """
    InfoNCE损失函数 - 更稳定的对比学习损失
    """
    def __init__(self, temperature=0.1):
        super(InfoNCELoss, self).__init__()
        self.temperature = temperature

    def forward(self, features, labels):
        batch_size = features.shape[0]

        # 调试信息
        unique_labels = torch.unique(labels)
        print(f"Batch size: {batch_size}, Unique labels: {len(unique_labels)}, Labels: {unique_labels.tolist()}")

        # 归一化特征
        features = nn.functional.normalize(features, dim=1)

        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature

        # 创建正样本mask
        labels = labels.contiguous().view(-1, 1)
        mask = torch.eq(labels, labels.T).float()

        # 移除对角线（自己与自己的相似度）
        mask.fill_diagonal_(0)

        # 检查是否有正样本对
        positive_pairs = mask.sum().item()
        print(f"Positive pairs in batch: {positive_pairs}")

        if positive_pairs == 0:
            # 如果没有正样本对，使用简单的分类损失
            print("No positive pairs found, using simple contrastive loss")
            # 计算特征的方差作为损失，鼓励特征多样性
            feature_var = torch.var(features, dim=0).mean()
            loss = torch.clamp(1.0 - feature_var, min=0.1)  # 最小损失0.1
            return loss

        # 计算InfoNCE损失
        exp_sim = torch.exp(similarity_matrix)

        # 分母：与所有样本的相似度（除了自己）
        exp_sim_sum = exp_sim.sum(dim=1) - torch.diag(exp_sim)

        # 分子：与正样本的相似度
        pos_sim = (exp_sim * mask).sum(dim=1)

        # 避免除零
        pos_sim = torch.clamp(pos_sim, min=1e-8)
        exp_sim_sum = torch.clamp(exp_sim_sum, min=1e-8)

        # InfoNCE损失
        loss = -torch.log(pos_sim / exp_sim_sum)

        # 只计算有正样本的anchor的损失
        valid_mask = (mask.sum(dim=1) > 0)
        if valid_mask.sum() > 0:
            loss = loss[valid_mask].mean()
            print(f"InfoNCE loss: {loss.item():.4f}")
        else:
            # 备用损失
            loss = torch.tensor(0.1, device=features.device, requires_grad=True)
            print("No valid anchors, using backup loss: 0.1")

        return loss

def train_contrastive_model(model, dataloader, device, epochs=5, learning_rate=5e-5, temperature=0.1):
    """
    对比学习微调
    """
    print(f"Starting contrastive fine-tuning for {epochs} epochs...")
    print(f"Device: {device}, Batch size: {len(next(iter(dataloader))[0])}")

    # 设置优化器和损失函数
    criterion = InfoNCELoss(temperature=temperature)
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)

    model.train()
    total_batches = len(dataloader)

    for epoch in range(epochs):
        total_loss = 0
        num_batches = 0

        print(f"Epoch {epoch+1}/{epochs} - Processing {total_batches} batches...")
        for batch_idx, (src_batch, seg_batch, labels_batch) in enumerate(dataloader):
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            labels_batch = labels_batch.to(device)

            optimizer.zero_grad()

            # 前向传播
            projected_features = model(src_batch, labels_batch, seg_batch)

            # 计算损失
            loss = criterion(projected_features, labels_batch)

            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"Warning: Invalid loss detected: {loss}")
                continue

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            # 显示进度
            if (batch_idx + 1) % max(1, total_batches // 4) == 0:
                print(f"  Batch {batch_idx + 1}/{total_batches}, Current Loss: {loss.item():.4f}")

        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        print(f"Epoch {epoch+1}/{epochs}, Average Loss: {avg_loss:.4f}")

    print("Contrastive fine-tuning completed!")
    return model

class ContrastiveDataset(Dataset):
    """对比学习数据集"""
    def __init__(self, args, texts, labels):
        self.args = args
        self.texts = texts
        self.labels = labels
        self.tokenizer = args.tokenizer
        self.seq_length = args.max_seq_length

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]
        
        # 分词
        tokens = self.tokenizer.tokenize(text)
        src = self.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + tokens)
        seg = [1] * len(src)

        # 截断或填充
        if len(src) > self.seq_length:
            src = src[:self.seq_length]
            seg = seg[:self.seq_length]
        while len(src) < self.seq_length:
            src.append(0)
            seg.append(0)

        return torch.LongTensor(src), torch.LongTensor(seg), torch.LongTensor([label])

def collate_fn_contrastive(batch):
    """对比学习数据集的collate函数"""
    src_list, seg_list, label_list = [], [], []
    for src, seg, label in batch:
        src_list.append(src)
        seg_list.append(seg)
        label_list.append(label)
    
    return (torch.stack(src_list),
            torch.stack(seg_list),
            torch.stack(label_list).squeeze())

def load_or_initialize_parameters(args, model):
    """加载预训练参数，处理序列长度不匹配问题"""
    if args.pretrained_model_path:
        try:
            # 尝试正常加载
            model = load_model(model, args.pretrained_model_path)
        except RuntimeError as e:
            if "size mismatch" in str(e) and "position_embedding" in str(e):
                print(f"Position embedding size mismatch detected. Loading with strict=False...")
                # 加载时忽略位置嵌入的尺寸不匹配
                checkpoint = torch.load(args.pretrained_model_path, map_location="cpu")
                model_dict = model.state_dict()

                # 过滤掉不匹配的位置嵌入权重
                filtered_checkpoint = {}
                for k, v in checkpoint.items():
                    if k in model_dict:
                        if "position_embedding" in k and v.shape != model_dict[k].shape:
                            print(f"Skipping {k} due to shape mismatch: {v.shape} vs {model_dict[k].shape}")
                            continue
                        filtered_checkpoint[k] = v

                # 加载过滤后的权重
                model.load_state_dict(filtered_checkpoint, strict=False)
                print("Model loaded with position embedding reinitialized")
            else:
                raise e
    return model

def extract_features_from_tsv(input_tsv_path, output_features_path,
                             pretrained_model_path=None,
                             vocab_path=None,
                             config_path=None,
                             projection_dim=64,
                             max_seq_length=64,
                             batch_size=8,
                             min_class_samples=10,
                             fine_tune_epochs=5,
                             learning_rate=5e-5,
                             temperature=0.1):
    """
    从TSV文件中提取特征
    """
    # 设置随机种子
    set_seed(42)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 设置默认路径
    if pretrained_model_path is None:
        pretrained_model_path = os.path.join(module_dir, "pre-trained_model.bin")
    if vocab_path is None:
        vocab_path = os.path.join(module_dir, "encryptd_vocab.txt")
    if config_path is None:
        config_path = os.path.join(module_dir, "contrastive_bert_config.json")
    
    # 创建参数对象
    class Args:
        pass
    args = Args()
    
    # 设置基本参数
    args.pretrained_model_path = pretrained_model_path
    args.vocab_path = vocab_path
    args.config_path = config_path
    args.max_seq_length = max_seq_length
    args.batch_size = batch_size
    args.projection_dim = projection_dim
    
    # 设置模型参数
    args.embedding = "word_pos_seg"
    args.encoder = "transformer"
    args.pooling = "first"
    args.tokenizer_type = "space"
    
    # 设置必要属性
    args.spm_model_path = None
    args.remove_embedding_layernorm = False
    args.remove_transformer_bias = False
    args.layernorm_positioning = "post"
    args.layernorm = "normal"
    args.relative_position_embedding = False
    args.relative_attention_buckets_num = 32
    args.remove_attention_scale = False
    args.parameter_sharing = False
    args.factorized_embedding_parameterization = False
    args.feed_forward = "dense"
    args.has_residual_attention = True
    args.bidirectional = False
    args.seq_length = max_seq_length
    args.device = device
    args.mask = "fully_visible"  # 添加缺失的mask属性
    
    # 加载配置
    args = load_hyperparam(args)
    args.attention_head_size = args.hidden_size // args.heads_num

    # 确保所有必需的属性都存在
    if not hasattr(args, 'dropout'):
        args.dropout = 0.1
    if not hasattr(args, 'emb_size'):
        args.emb_size = args.hidden_size
    if not hasattr(args, 'feedforward_size'):
        args.feedforward_size = args.hidden_size * 4
    
    # 构建分词器
    if args.tokenizer_type == "space":
        args.tokenizer = SpaceTokenizer(args)
    elif args.tokenizer_type == "char":
        args.tokenizer = CharTokenizer(args)
    elif args.tokenizer_type == "bert":
        args.tokenizer = BertTokenizer(args)
    else:
        raise ValueError(f"Unknown tokenizer type: {args.tokenizer_type}")
    
    print("Loading data...")
    df = pd.read_csv(input_tsv_path, sep='\t')

    # 检查列名并适配不同的输入格式
    # 支持两种格式：预测器输出格式和原始格式
    text_col = 'text_a' if 'text_a' in df.columns else 'original_text_a'
    ground_truth_col = 'ground_truth_name' if 'ground_truth_name' in df.columns else 'original_ground_truth_name'

    # 筛选未知样本
    df_filtered = df[df[ground_truth_col].notna() & (df[ground_truth_col] != '')]

    # 如果有预测标签列，只保留被预测为未知的样本
    if 'predicted_label' in df_filtered.columns:
        df_filtered = df_filtered[df_filtered['predicted_label'] == 1].copy()
    elif 'label' in df_filtered.columns:
        # 如果没有predicted_label但有label列，假设label=1表示未知
        df_filtered = df_filtered[df_filtered['label'] == 1].copy()

    if df_filtered.empty:
        raise ValueError("No valid unknown samples found in the input file.")

    # 提取数据
    texts = df_filtered[text_col].tolist()
    ground_truth_names = df_filtered[ground_truth_col].tolist()
    
    # 过滤掉样本数太少的类别
    label_counts = Counter(ground_truth_names)
    valid_classes = {cls for cls, count in label_counts.items() if count >= min_class_samples}
    
    if len(valid_classes) < len(label_counts):
        print(f"Filtering out {len(label_counts) - len(valid_classes)} classes with < {min_class_samples} samples")
        mask = [cls in valid_classes for cls in ground_truth_names]
        texts = [t for t, m in zip(texts, mask) if m]
        ground_truth_names = [g for g, m in zip(ground_truth_names, mask) if m]
        df_filtered = df_filtered[mask].reset_index(drop=True)
    
    # 编码标签
    label_encoder = LabelEncoder()
    labels = label_encoder.fit_transform(ground_truth_names)
    num_classes = len(label_encoder.classes_)
    
    # 设置标签数量
    args.labels_num = num_classes
    
    print(f"Loaded {len(texts)} samples with {num_classes} classes")
    print(f"Classes: {label_encoder.classes_}")
    
    # 创建数据集
    dataset = ContrastiveDataset(args, texts, labels)
    # 训练时需要shuffle=True以确保批次中有正样本对，drop_last=True避免不完整批次
    train_dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, drop_last=True, collate_fn=collate_fn_contrastive)
    
    # 创建模型
    print("Loading pretrained ET-BERT model...")
    model = ImprovedContrastiveBERT(args, args.projection_dim)
    
    # 加载预训练权重
    model = load_or_initialize_parameters(args, model)
    model = model.to(device)
    print("Pretrained ET-BERT model loaded successfully")

    # 对比学习微调
    if fine_tune_epochs > 0:
        print(f"Starting contrastive fine-tuning for {fine_tune_epochs} epochs...")
        model = train_contrastive_model(model, train_dataloader, device,
                                      epochs=fine_tune_epochs,
                                      learning_rate=learning_rate,
                                      temperature=temperature)
    else:
        print("Skipping fine-tuning (epochs=0)")

    # 切换到评估模式
    model.eval()

    # 创建用于特征提取的dataloader（不drop_last，确保所有样本都被处理）
    eval_dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=False, collate_fn=collate_fn_contrastive)

    # 提取特征
    print("Extracting features...")
    bert_features_list = []
    projected_features_list = []

    with torch.no_grad():
        for src_batch, seg_batch, labels_batch in eval_dataloader:
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            
            # 提取特征
            bert_features, projected_features = model(src_batch, labels_batch, seg_batch, return_features=True)
            
            bert_features_list.append(bert_features.cpu().numpy())
            projected_features_list.append(projected_features.cpu().numpy())
    
    # 合并特征
    bert_features = np.vstack(bert_features_list)
    projected_features = np.vstack(projected_features_list)
    
    print(f"BERT features shape: {bert_features.shape}")
    print(f"Projected features shape: {projected_features.shape}")
    
    # 检查特征质量
    if np.isnan(projected_features).any():
        print("⚠️  Warning: NaN values detected in features!")
    else:
        print("✅ Features look good - no NaN values")
    
    # 保存特征
    df_output = df_filtered.copy()
    df_output['embedding'] = ['[' + ', '.join(map(str, feat)) + ']' for feat in projected_features]

    # 标准化列名以便聚类分析使用
    if 'ground_truth_name' in df_output.columns and 'original_ground_truth_name' not in df_output.columns:
        df_output['original_ground_truth_name'] = df_output['ground_truth_name']
    if 'text_a' in df_output.columns and 'original_text_a' not in df_output.columns:
        df_output['original_text_a'] = df_output['text_a']

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_features_path), exist_ok=True)
    df_output.to_csv(output_features_path, sep='\t', index=False)
    
    print(f"Features saved to {output_features_path}")
    
    return {
        'features_file': os.path.basename(output_features_path),
        'num_samples': int(len(texts)),
        'num_classes': int(num_classes),
        'feature_dim': int(projected_features.shape[1])
    }
