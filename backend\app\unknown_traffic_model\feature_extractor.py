
import os
import subprocess
import csv
import zipfile
import tempfile
import shutil
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# This path might need to be configured if Wireshark is installed elsewhere.
# Use the absolute path found within the container to ensure it's always found.
TSHARK_EXE_PATH = "/usr/bin/tshark"
LABEL_KNOWN = 0
LABEL_UNKNOWN = 1

def tokenize_sni(sni_string):
    """
    Tokenizes an SNI string by converting it to a space-separated sequence of hex bytes.
    """
    if not sni_string:
        return ""
    hex_representation = sni_string.encode('utf-8').hex()
    return " ".join(hex_representation[i:i+2] for i in range(0, len(hex_representation), 2))

def worker_get_sni_from_fields(task):
    """
    Worker function to extract SNI from a single pcap file.
    """
    pcap_path, app_name, label_id = task
    found_snis = set()
    try:
        if not os.path.exists(TSHARK_EXE_PATH):
            raise FileNotFoundError(f"TShark not found at: {TSHARK_EXE_PATH}")

        cmd = [
            TSHARK_EXE_PATH, "-r", str(pcap_path), "-n",
            "-Y", "tls.handshake.type == 1 && tls.handshake.extensions_server_name",
            "-T", "fields",
            "-e", "tls.handshake.extensions_server_name"
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if result.stderr and not result.stdout:
            # Using print instead of tqdm.write as we are not in a TQDM loop here.
            print(f"  -> TSHARK WARNING for {pcap_path}: {result.stderr.strip()}")

        for line in result.stdout.strip().splitlines():
            sni = line.strip()
            for single_sni in sni.split(','):
                if single_sni:
                    found_snis.add(single_sni)

        final_features = []
        for sni in found_snis:
            # We use a placeholder label; ground_truth_name is what matters.
            final_features.append((label_id, tokenize_sni(sni), app_name, sni))
        
        return final_features
    
    except Exception as e:
        print(f"  -> An unexpected exception in worker for {pcap_path}: {e}")
        return []

def process_pcap_files_from_zip(zip_path, output_tsv_path):
    """
    Extracts pcaps from a zip file, processes them to extract SNI features,
    and saves the results to a TSV file.
    This version uses a ThreadPool to process files concurrently.
    """
    temp_dir = tempfile.mkdtemp(prefix="pcap_extract_")
    
    try:
        # --- Step 1: Unzip the file ---
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # --- Step 2: Discover all PCAP files ---
        pcap_tasks = []
        print(f"Scanning for pcap files in {temp_dir}...")
        for root, _, files in os.walk(temp_dir):
            for file in files:
                if file.lower().endswith(('.pcap', '.pcapng')):
                    full_path = os.path.join(root, file)
                    # Use the pcap's parent directory name as the "app_name"
                    app_name = os.path.basename(root)
                    # Assign label based on folder name convention
                    label_id = LABEL_UNKNOWN if app_name.endswith("_未知") else LABEL_KNOWN
                    pcap_tasks.append((full_path, app_name, label_id))
        
        if not pcap_tasks:
            raise ValueError("No pcap files found in the uploaded zip archive.")
            
        print(f"Found {len(pcap_tasks)} pcap files to process.")

        # --- Step 3: Process PCAPs concurrently using a ThreadPool ---
        all_results = []
        # For I/O bound tasks, we can use more threads than CPU cores.
        # Let's cap it at 32 to be safe.
        num_threads = min(32, (os.cpu_count() or 1) + 4)
        print(f"Starting SNI extraction with {num_threads} threads...")

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # executor.map is a convenient way to apply a function to a list of items in parallel.
            results_iterator = executor.map(worker_get_sni_from_fields, pcap_tasks)
            
            # Use tqdm to show progress for the submitted tasks.
            for sni_list in tqdm(results_iterator, total=len(pcap_tasks), desc="Extracting SNI from pcaps"):
                if sni_list:
                    all_results.extend(sni_list)

        # --- Step 4: Write results to TSV file ---
        os.makedirs(os.path.dirname(output_tsv_path), exist_ok=True)
        with open(output_tsv_path, "w", encoding='utf-8', newline='') as f_out:
            writer = csv.writer(f_out, delimiter='\t')
            writer.writerow(["label", "text_a", "ground_truth_name", "raw_sni"])
            for label, feature_string, app_name, raw_sni in all_results:
                writer.writerow([label, feature_string, app_name, raw_sni])
        
        print(f"Processing complete! {len(all_results)} SNIs written to {output_tsv_path}")
        return len(all_results)

    finally:
        # --- Step 5: Clean up the temporary directory ---
        print(f"Cleaning up temporary directory: {temp_dir}")
        shutil.rmtree(temp_dir) 