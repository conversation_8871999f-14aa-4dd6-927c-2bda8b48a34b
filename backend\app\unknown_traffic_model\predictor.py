
import os
import sys
import torch
import torch.nn as nn
import pandas as pd
from tqdm import tqdm
from torch.utils.data import DataLoader, IterableDataset

# Ensure the local 'uer' module is in the python path
# This makes the script runnable from the 'backend' directory, e.g., for testing
module_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, module_dir)

from uer.model_loader import load_model
from uer.utils.config import load_hyperparam
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.encoders import str2encoder
from uer.layers import str2embedding

# --- Model Definition (copied and adapted from run_classifier.py) ---

class Classifier(nn.Module):
    def __init__(self, args):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.tokenizer.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, self.labels_num)

    def forward(self, src, seg):
        emb = self.embedding(src, seg)
        output = self.encoder(emb, seg)
        
        if self.pooling == "mean":
            pooled_output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            pooled_output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            pooled_output = output[:, -1, :]
        else: # first
            pooled_output = output[:, 0, :]
            
        output_for_classification = torch.tanh(self.output_layer_1(pooled_output))
        logits = self.output_layer_2(output_for_classification)
        return logits

# --- Data Loading (copied and adapted from run_classifier.py) ---

def process_line(line, tokenizer, seq_length, columns):
    line_parts = line.strip().split("\t")
    
    label = int(line_parts[columns["label"]])
    text_a = line_parts[columns["text_a"]]
    ground_truth_name = line_parts[columns.get("ground_truth_name", -1)]
    raw_sni = line_parts[columns.get("raw_sni", -1)]

    src = tokenizer.convert_tokens_to_ids([CLS_TOKEN] + tokenizer.tokenize(text_a))
    seg = [1] * len(src)

    if len(src) > seq_length:
        src = src[:seq_length]
        seg = seg[:seq_length]
    while len(src) < seq_length:
        src.append(0)
        seg.append(0)
        
    return (src, seg, label, text_a, ground_truth_name, raw_sni)

class PredictionDataset(IterableDataset):
    def __init__(self, path, tokenizer, seq_length):
        self.path = path
        self.tokenizer = tokenizer
        self.seq_length = seq_length

    def __iter__(self):
        with open(self.path, "r", encoding="utf-8") as f:
            header = f.readline()
            columns = {name: i for i, name in enumerate(header.strip().split("\t"))}
            for line in f:
                yield process_line(line, self.tokenizer, self.seq_length, columns)

def prediction_collate_fn(batch):
    src_list, seg_list, label_list, text_a_list, gtn_list, rs_list = [], [], [], [], [], []
    for sample in batch:
        src_list.append(sample[0])
        seg_list.append(sample[1])
        label_list.append(sample[2])
        text_a_list.append(sample[3])
        gtn_list.append(sample[4])
        rs_list.append(sample[5])
        
    return (torch.LongTensor(src_list),
            torch.LongTensor(seg_list),
            torch.LongTensor(label_list),
            text_a_list,
            gtn_list,
            rs_list)

# --- Main Prediction Logic ---

def run_prediction(input_tsv_path, unknown_output_path):
    
    # --- Configuration ---
    # We create a mock 'args' object to hold the configuration
    class Args:
        pass
    args = Args()

    # Paths are now relative to this script's location
    args.config_path = os.path.join(module_dir, "bert_base_config.json")
    args.load_model_path = os.path.join(module_dir, "finetuned_model_sni_hex.bin")
    args.vocab_path = os.path.join(module_dir, "vocab.txt")
    
    # Model & Tokenizer parameters (should match the fine-tuning setup)
    args.embedding = "word_pos_seg"
    args.encoder = "transformer" # Corrected from "bert"
    args.pooling = "first"
    args.labels_num = 2 # Known (0) vs Unknown (1)
    args.max_seq_length = 512 # Renamed from seq_length to match model_opts
    args.batch_size = 32
    
    # --- Add missing model_opts defaults to prevent AttributeErrors ---
    args.relative_position_embedding = False
    args.relative_attention_buckets_num = 32
    args.remove_embedding_layernorm = False
    args.remove_attention_scale = False
    args.mask = "fully_visible" # Added missing mask parameter
    args.layernorm_positioning = "post"
    args.feed_forward = "dense"
    args.remove_transformer_bias = False
    args.layernorm = "normal"
    args.bidirectional = False
    args.factorized_embedding_parameterization = False
    args.parameter_sharing = False
    
    args = load_hyperparam(args)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # --- Build Tokenizer (Corrected Order) ---
    # 1. First, load the vocabulary.
    vocab = Vocab()
    vocab.load(args.vocab_path)
    
    # 2. The SpaceTokenizer needs an object with a 'vocab' attribute.
    #    We create a temporary simple object for this, following uer library pattern.
    class TokenizerArgs:
        pass
    tokenizer_args = TokenizerArgs()
    tokenizer_args.vocab = vocab.w2i  # Pass the vocab dictionary, not the Vocab object
    
    # 3. Now, create the tokenizer instance.
    tokenizer = SpaceTokenizer(tokenizer_args)

    # 4. Finally, assign the created tokenizer to the main args object.
    args.tokenizer = tokenizer

    # --- Build Model ---
    model = Classifier(args)
    model = load_model(model, args.load_model_path)
    model = model.to(device)
    model.eval()

    # --- Load Data ---
    dataset = PredictionDataset(input_tsv_path, args.tokenizer, args.max_seq_length)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, collate_fn=prediction_collate_fn)
    
    # --- Run Prediction ---
    all_predictions = []
    all_gold_labels = []
    unknown_samples = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Predicting"):
            src_batch, seg_batch, gold_labels_batch, text_a_batch, gtn_batch, rs_batch = batch
            
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            
            logits = model(src_batch, seg_batch)
            predictions = torch.argmax(logits, dim=1).cpu().numpy()
            
            all_predictions.extend(predictions)
            all_gold_labels.extend(gold_labels_batch.cpu().numpy())
            
            # Filter for unknown samples
            for i in range(len(predictions)):
                if predictions[i] == 1: # Label 1 is 'Unknown'
                    unknown_samples.append({
                        "label": gold_labels_batch[i].item(),
                        "text_a": text_a_batch[i],
                        "ground_truth_name": gtn_batch[i],
                        "raw_sni": rs_batch[i]
                    })
    
    # --- Save Unknown Samples ---
    if unknown_samples:
        unknown_df = pd.DataFrame(unknown_samples)
        os.makedirs(os.path.dirname(unknown_output_path), exist_ok=True)
        unknown_df.to_csv(unknown_output_path, sep='\t', index=False, header=["label", "text_a", "ground_truth_name", "raw_sni"])

    # --- Calculate Metrics ---
    gold_tensor = torch.LongTensor(all_gold_labels)
    pred_tensor = torch.LongTensor(all_predictions)
    
    total = len(gold_tensor)
    correct = (pred_tensor == gold_tensor).sum().item()
    accuracy = correct / total if total > 0 else 0
    
    confusion = torch.zeros(args.labels_num, args.labels_num, dtype=torch.long)
    for i in range(total):
        confusion[pred_tensor[i], gold_tensor[i]] += 1
        
    TP = confusion[0, 0].item() # True Known
    FN = confusion[1, 0].item() # Known predicted as Unknown (漏报)
    FP = confusion[0, 1].item() # Unknown predicted as Known (误报)
    TN = confusion[1, 1].item() # True Unknown
    
    fnr = FN / (TP + FN) if (TP + FN) > 0 else 0 # Leakage Rate
    fdr = FP / (TN + FP) if (TN + FP) > 0 else 0 # False Discovery Rate
    
    results = {
        "total_samples": total,
        "accuracy": accuracy,
        "correct_predictions": correct,
        "confusion_matrix": {
            "true_known_pred_known": TP,
            "true_known_pred_unknown": FN,
            "true_unknown_pred_known": FP,
            "true_unknown_pred_unknown": TN,
        },
        "leakage_rate_fnr": fnr,
        "false_discovery_rate_fdr": fdr,
        "unknown_samples_found": len(unknown_samples),
        "unknown_samples_file": os.path.basename(unknown_output_path) if unknown_samples else None
    }
    
    return results

# Helper class from uer.utils.tokenizer
class SpaceTokenizer(object):
    def __init__(self, args):
        self.vocab = args.vocab

    def tokenize(self, text, use_spm=False):
        if use_spm:
            return text.split(" ")
        else:
            return [token for token in text.split(" ") if token in self.vocab]

    def convert_tokens_to_ids(self, tokens):
        # 将token列表转换为id列表，未知词用0
        return [self.vocab.get(token, 0) for token in tokens]