import os
from ..celery_worker import celery_app
from .feature_extractor import process_pcap_files_from_zip
from .predictor import run_prediction
from .contrastive_feature_extractor import extract_features_from_tsv
from .cluster_analyzer import perform_clustering_analysis
from celery.exceptions import Ignore

@celery_app.task(bind=True)
def extract_features_task(self, zip_path: str, output_tsv_path: str):
    """
    Celery task to perform feature extraction from a zip file of pcaps.
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '正在提取特征...'})
        print(f"Celery Worker: Starting feature extraction for {zip_path}")
        
        total_snis = process_pcap_files_from_zip(zip_path, output_tsv_path)
        
        print(f"Celery Worker: Feature extraction finished. Found {total_snis} SNIs.")
        return {
            'status': 'SUCCESS',
            'message': f'成功提取 {total_snis} 个 SNI 特征。',
            'filename': os.path.basename(output_tsv_path),
        }
    except Exception as e:
        print(f"Celery Worker: Feature extraction failed for {zip_path}. Error: {e}")
        # Let Celery handle the failure state. It will automatically set the state to FAILURE.
        # We re-raise the exception to ensure Celery's default error handling takes over.
        raise e

@celery_app.task(bind=True)
def run_prediction_task(self, input_tsv_path: str, unknown_output_path: str):
    """
    Celery task to run prediction on a tsv file.
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '模型正在分析中...'})
        print(f"Celery Worker: Starting prediction for {input_tsv_path}")
        
        results = run_prediction(input_tsv_path, unknown_output_path)
        
        print(f"Celery Worker: Prediction finished for {input_tsv_path}")
        
        results['status'] = 'SUCCESS'
        return results

    except Exception as e:
        print(f"Celery Worker: Prediction failed for {input_tsv_path}. Error: {e}")
        # Re-raise the exception for Celery to handle.
        raise e

@celery_app.task(bind=True)
def extract_contrastive_features_task(self, input_tsv_path: str, output_features_path: str):
    """
    Celery task to extract contrastive features from a tsv file.
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '正在提取对比学习特征...'})
        print(f"Celery Worker: Starting contrastive feature extraction for {input_tsv_path}")

        # 使用2个epoch进行对比学习微调
        # 可以调整batch_size来测试性能：4, 8, 16
        results = extract_features_from_tsv(input_tsv_path, output_features_path,
                                          fine_tune_epochs=5,
                                          learning_rate=5e-5,
                                          temperature=0.1,
                                          batch_size=8)  
        print(f"Celery Worker: Contrastive feature extraction finished for {input_tsv_path}")

        results['status'] = 'SUCCESS'
        results['message'] = f'成功完成5轮对比学习微调并提取 {results["num_samples"]} 个样本的特征，共 {results["num_classes"]} 个类别。'
        results['filename'] = os.path.basename(output_features_path)
        return results

    except Exception as e:
        print(f"Celery Worker: Contrastive feature extraction failed for {input_tsv_path}. Error: {e}")
        # Re-raise the exception for Celery to handle.
        raise e

@celery_app.task(bind=True)
def perform_clustering_task(self, input_features_path: str, output_clustered_path: str,
                          umap_dims=64, umap_neighbors=5, min_cluster_size=25):
    """
    Celery task to perform clustering analysis on features.
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '正在执行聚类分析...'})
        print(f"Celery Worker: Starting clustering analysis for {input_features_path}")

        results = perform_clustering_analysis(
            input_features_path,
            output_clustered_path,
            umap_dims=umap_dims,
            umap_neighbors=umap_neighbors,
            min_cluster_size=min_cluster_size
        )

        print(f"Celery Worker: Clustering analysis finished for {input_features_path}")

        results['status'] = 'SUCCESS'
        results['message'] = f'聚类分析完成，发现 {results["n_clusters"]} 个聚类，{results["n_noise"]} 个噪声点。'
        results['filename'] = os.path.basename(output_clustered_path)
        return results

    except Exception as e:
        print(f"Celery Worker: Clustering analysis failed for {input_features_path}. Error: {e}")
        # Re-raise the exception for Celery to handle.
        raise e