import torch
import os


def save_model(model, output_model_path):
    # Create the output directory if it doesn't exist
    output_dir = os.path.dirname(output_model_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    if hasattr(model, "module"):
        torch.save(model.module.state_dict(), output_model_path)
    else:
        torch.save(model.state_dict(), output_model_path)
