import os
import binascii
from threading import Lock

import torch
import numpy as np
from PIL import Image
try:
    import scapy.all as scapy
except ImportError:
    # Handle cases where scapy might not be installed or has issues
    scapy = None
from torchvision import transforms
import io
import base64

# Assuming models_YaTC is in the same directory
from . import models_YaTC

# --- Global variables for the model, loaded on first prediction ---
model_lock = Lock()
yatc_model = None
device = None
class_names = [
    '勒索软件', '恶意文件', '扫描探测', '木马流量', 
    '致瘫攻击', '良性', '隐蔽传输', '高危漏洞'
]
nb_classes = len(class_names)

# --- Pre-defined model configuration ---
MODEL_NAME = 'TraFormer_YaTC'
MODEL_CHECKPOINT_REL_PATH = 'yatc_model/checkpoint-best.pth'

def get_model_path():
    """Constructs the absolute path to the model checkpoint."""
    return os.path.join(os.path.dirname(__file__), 'checkpoint-best.pth')

def load_model():
    """
    Loads the YaTC model into memory. This function is designed to be
    called once and the result cached. It is thread-safe.
    """
    global yatc_model, device

    with model_lock:
        if yatc_model is not None:
            return yatc_model, device

        print("Worker: Initializing YaTC model for the first time...")
        
        # Determine device
        _device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Worker: YaTC model will use device: {_device}")

        # Instantiate the model, ensuring all hard-coded parameters from the original
        # TraFormer_YaTC function are included to guarantee identical model architecture.
        _model = models_YaTC.__dict__[MODEL_NAME](
            num_classes=nb_classes,
            drop_path_rate=0.1
        )

        # Load checkpoint
        model_path = get_model_path()
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"YaTC model checkpoint not found at: {model_path}")
            
        # Set weights_only=False to load older checkpoints that contain non-tensor objects.
        # This is necessary due to a security update in PyTorch >= 2.6.
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        model_state_dict = checkpoint.get('model', checkpoint)
        
        # Handle state dict keys if they have 'module.' prefix
        if any(key.startswith('module.') for key in model_state_dict.keys()):
            model_state_dict = {k.replace('module.', ''): v for k, v in model_state_dict.items()}

        _model.load_state_dict(model_state_dict, strict=False)
        print(f"Worker: YaTC model loaded successfully from '{model_path}'.")

        _model.to(_device)
        _model.eval()

        # Assign to global variables
        yatc_model = _model
        device = _device
        
        return yatc_model, device

def read_5hp_list(pcap_dir):
    """
    Reads a pcap file, extracts headers and payloads of the first 5 IP packets,
    and processes them into a fixed-length hexadecimal string.
    """
    if scapy is None:
        raise ImportError("Scapy is not installed or could not be imported, cannot process pcap files.")
        
    try:
        packets = scapy.rdpcap(pcap_dir)
    except Exception as e:
        print(f"Error: Could not read pcap file {pcap_dir} with scapy: {e}")
        return None
        
    data = []
    for packet in packets:
        if 'IP' in packet:
            # --- FIX STARTS HERE: Revert to the exact logic from the working standalone script ---
            header = (binascii.hexlify(bytes(packet['IP']))).decode()
            try:
                payload = (binascii.hexlify(bytes(packet['Raw']))).decode()
                header = header.replace(payload, '')
            except:
                payload = ''
            # --- FIX ENDS HERE ---

            header = (header + '0' * 160)[:160]
            payload = (payload + '0' * 480)[:480]
            
            data.append((header, payload))
            if len(data) >= 5:
                break
    
    while len(data) < 5:
        data.append(('0' * 160, '0' * 480))
            
    return ''.join([h + p for h, p in data])

def pcap_to_image_and_tensor(pcap_path, transform):
    """
    Processes a single pcap file into both a PIL Image for display
    and a tensor suitable for the model.
    """
    hex_data = read_5hp_list(pcap_path)
    if hex_data is None:
        return None, None

    if len(hex_data) != 3200:
        print(f"Warning: Hex data length is {len(hex_data)}, not 3200.")

    int_data = np.array([int(hex_data[i:i + 2], 16) for i in range(0, len(hex_data), 2)])
    matrix_data = np.reshape(int_data, (40, 40))
    matrix_data = np.uint8(matrix_data)
    image = Image.fromarray(matrix_data)
    
    tensor = transform(image)
    return image, tensor

def predict_yatc(filepath: str):
    """
    Main prediction function for the YaTC model.
    Takes a file path, performs all necessary steps, and returns a dict
    including a base64 encoded image of the traffic.
    """
    try:
        model, dev = load_model()

        transform = transforms.Compose([
            transforms.Grayscale(num_output_channels=1),
            transforms.ToTensor(),
            transforms.Normalize([0.5], [0.5]),
        ])

        # Get both the image and the tensor
        image, tensor = pcap_to_image_and_tensor(filepath, transform)
        
        if tensor is None:
            return {
                "model": "YaTC",
                "status": "失败",
                "error": "PCAP文件处理失败，可能是文件格式问题或scapy无法读取。"
            }

        # Convert image to base64
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")

        tensor = tensor.unsqueeze(0).to(dev)

        with torch.no_grad():
            output = model(tensor)
            probabilities = torch.nn.functional.softmax(output, dim=1)
            top_prob, top_catid = torch.max(probabilities, 1)

        predicted_class = class_names[top_catid.item()]
        confidence = top_prob.item()

        return {
            "model": "YaTC",
            "status": "成功",
            "prediction": predicted_class,
            "confidence": confidence,
            "details": {
                "image_base64": img_str,
                "model_name": MODEL_NAME,
                "info": "已处理前5个IP数据包生成40x40像素灰度图像。"
            }
        }

    except FileNotFoundError as e:
        print(f"Error during YaTC prediction: {e}")
        return {"model": "YaTC", "status": "失败", "error": str(e)}
    except Exception as e:
        print(f"An unexpected error occurred during YaTC prediction: {e}")
        # Optionally, log the full traceback here for debugging
        return {"model": "YaTC", "status": "失败", "error": "模型推理时发生未知错误。"} 