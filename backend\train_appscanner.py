import os
import pickle
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

# ------------------- 配置 -------------------
# 注意：这些路径都是容器内的路径

# 您的特征文件需要被挂载到容器的这个路径下
# 我们将在 docker-compose.yml 中设置这个挂载
FEATURES_INPUT_PATH = '/features/appscanner_features.pkl'

# 新模型保存的路径 (容器内的路径)
NEW_MODEL_OUTPUT_PATH = '/app/app/appscanner/appscanner_model.pkl'

# 新的兼容测试集保存路径 (容器内的路径)
NEW_TEST_SET_OUTPUT_PATH = '/app/generated_appscanner_test_set.pkl'
# --------------------------------------------

def train_from_features():
    """
    直接从现有的特征文件 .pkl 中加载数据并训练模型。
    """
    print("开始 AppScanner 模型再训练流程 (从现有特征文件)...")

    # --- 1. 加载特征数据 ---
    print(f"正在从 {FEATURES_INPUT_PATH} 加载特征数据...")
    if not os.path.exists(FEATURES_INPUT_PATH):
        print(f"错误: 特征文件 '{FEATURES_INPUT_PATH}' 不存在。")
        print("请确保您已在 docker-compose.yml 中正确挂载了您的特征文件。")
        return

    with open(FEATURES_INPUT_PATH, 'rb') as f:
        X, y = pickle.load(f)

    print("特征数据加载完成！")
    print(f"总样本数: {X.shape[0]}, 特征数: {X.shape[1]}")

    # --- 2. 划分训练集和测试集 ---
    print("\n正在划分数据集 (80%训练集, 20%测试集)...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    print("数据集划分完成。")

    # --- 3. 训练随机森林模型 ---
    print("\n开始训练 RandomForestClassifier 模型...")
    classifier = RandomForestClassifier(
        n_estimators=100, 
        random_state=42, 
        oob_score=True, 
        n_jobs=-1,
        class_weight='balanced'  # 平衡类别权重
    )
    classifier.fit(X_train, y_train)
    print("模型训练完成！")
    print(f"OOB (Out-of-Bag) Score: {classifier.oob_score_:.4f}")

    # --- 4. 在测试集上评估模型 ---
    print("\n正在测试集上评估新模型...")
    y_pred = classifier.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"新模型在测试集上的准确率: {accuracy:.4f}")

    # --- 5. 保存新训练的、完全兼容的模型 ---
    print(f"\n正在将新模型保存到: {NEW_MODEL_OUTPUT_PATH}")
    try:
        joblib.dump(classifier, NEW_MODEL_OUTPUT_PATH)
        print("模型已使用 joblib 成功保存！")
    except Exception as e:
        print(f"Joblib 保存失败: {e}。回退到使用 pickle 保存...")
        with open(NEW_MODEL_OUTPUT_PATH, 'wb') as f:
            pickle.dump(classifier, f)
        print("模型已使用 pickle 成功保存。")

    # --- 6. 保存与新模型兼容的测试集 ---
    print(f"\n正在将新生成的测试集保存到: {NEW_TEST_SET_OUTPUT_PATH}")
    try:
        with open(NEW_TEST_SET_OUTPUT_PATH, 'wb') as f:
            pickle.dump((X_test, y_test), f)
        print(f"测试集已成功保存！它位于宿主机的 'prototype_system/backend/generated_appscanner_test_set.pkl'")
    except Exception as e:
        print(f"测试集保存失败: {e}")


    print("\nAppScanner 模型再训练流程全部完成！")

if __name__ == '__main__':
    train_from_features() 