services:
  redis:
    image: "redis:alpine"
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    image: backend-image  # 为构建的镜像指定一个明确的名称
    ports:
      - "8000:8000"
    volumes:
      - ./backend/app:/app/app
      - ./shared_uploads:/app/shared_uploads
      - ../df-master/saved_trained_models:/app/saved_trained_models
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  worker:
    image: backend-image  # 不再使用 build，而是直接使用已命名的镜像
    volumes:
      - ./backend/app:/app/app
      - ./shared_uploads:/app/shared_uploads
      - ../df-master/saved_trained_models:/app/saved_trained_models
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: celery -A app.tasks worker --loglevel=info
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  trainer:
    image: backend-image  # 同样，直接使用已命名的镜像
    volumes:
      - ./backend:/app
      - ./backend/app:/app/app
      # 注意：如需重新训练AppScanner模型，请手动挂载特征文件
      # - /path/to/your/appscanner_features.pkl:/features/appscanner_features.pkl
    command: python train_appscanner.py
    restart: "no"
    profiles:
      - tools
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  shared_uploads: {} 