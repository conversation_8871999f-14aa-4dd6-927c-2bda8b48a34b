import React from 'react';
import { Routes, Route } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import DFModelPage from './pages/DFModelPage';
import IntegratedModelPage from './pages/IntegratedModelPage';
import YaTCPage from './pages/YaTCPage';
import ETBERTPage from './pages/ETBERTPage';
import ETBERTBehaviorPage from './pages/ETBERTBehaviorPage';
import SystemOverviewPage from './pages/SystemOverviewPage'; // 导入新的总览页面
import ETBERTTafficClassifierPage from './pages/ETBERTTafficClassifierPage'; // 导入新的流量分类页面
import UnknownTrafficPage from './pages/UnknownTrafficPage'; // 导入未知识别页面

function App() {
  return (
    <Routes>
      <Route path="/" element={<MainLayout />}>
        <Route index element={<SystemOverviewPage />} /> {/* 将根路径指向新的总览页面 */}
        <Route path="df" element={<DFModelPage />} />
        <Route path="integrated" element={<IntegratedModelPage />} />
        <Route path="yatc" element={<YaTCPage />} />
        <Route path="et-bert" element={<ETBERTPage />} />
        <Route path="et-bert-behavior" element={<ETBERTBehaviorPage />} />
        <Route path="et-bert-traffic-classifier" element={<ETBERTTafficClassifierPage />} />
        <Route path="unknown-traffic" element={<UnknownTrafficPage />} />
      </Route>
    </Routes>
  );
}

export default App;
