import React, { useState } from 'react';
import { Link, useLocation, Outlet } from 'react-router-dom'; // 重新导入 Outlet
import {
  HomeOutlined,
  ApiOutlined,
  ApartmentOutlined,
  CameraOutlined,
  ExperimentOutlined, // 保留ET-BERT的图标
  SolutionOutlined, // 新增图标
  SafetyCertificateOutlined, // 新增图标
  QuestionCircleOutlined, // 为新页面添加图标
} from '@ant-design/icons';
import { Layout, Menu, Typography, theme } from 'antd';

const { Header, Content, Footer, Sider } = Layout;
const { Title } = Typography;

const items = [
  { key: '/', icon: <HomeOutlined />, label: <Link to="/">系统总览</Link> },
  { key: '/df', icon: <ApiOutlined />, label: <Link to="/df">DF 模型</Link> },
  { key: '/integrated', icon: <ApartmentOutlined />, label: <Link to="/integrated">综合分析</Link> },
  { key: '/yatc', icon: <CameraOutlined />, label: <Link to="/yatc">YaTC 分析</Link> },
  { key: '/et-bert', icon: <ExperimentOutlined />, label: <Link to="/et-bert">ET-BERT 快速学习</Link> },
  { key: '/et-bert-behavior', icon: <SolutionOutlined />, label: <Link to="/et-bert-behavior">ET-BERT 行为识别支持协议统计</Link> },
  { key: '/et-bert-traffic-classifier', icon: <SafetyCertificateOutlined />, label: <Link to="/et-bert-traffic-classifier">ET-BERT 流量分类</Link> },
  { key: '/unknown-traffic', icon: <QuestionCircleOutlined />, label: <Link to="/unknown-traffic">未知识别</Link> },
];

const MainLayout = ({ children }) => {
    const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  return (
    <Layout style={{ minHeight: '100vh' }}>
            <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
                <div style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)', borderRadius: 6, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Title level={4} style={{ color: 'white', margin: 0, display: collapsed ? 'none' : 'block' }}>流量分析</Title>
                </div>
                <Menu theme="dark" selectedKeys={[location.pathname]} mode="inline" items={items} />
      </Sider>
      <Layout>
        <Content style={{ margin: '16px' }}>
          <div
            style={{
                            padding: 24,
                            minHeight: 'calc(100vh - 112px)',
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
                        <Outlet /> {/* Outlet 组件需要在这里渲染子路由 */}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
                    网络流量分析原型系统 ©{new Date().getFullYear()}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default MainLayout; 