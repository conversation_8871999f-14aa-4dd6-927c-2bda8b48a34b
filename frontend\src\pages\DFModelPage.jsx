import React, { useState, useEffect } from 'react';
import { InboxOutlined, ApiOutlined } from '@ant-design/icons';
import { message, Upload, Card, Table, Space, Spin, Typography } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import axios from 'axios';

const { Dragger } = Upload;
const { Title } = Typography;

const columns = [
  {
    title: '属性',
    dataIndex: 'property',
    key: 'property',
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
  },
];

const DFModelPage = () => {
  const [analysisResult, setAnalysisResult] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [pollingStatus, setPollingStatus] = useState('');

  // Effect for polling results
  useEffect(() => {
    let interval;
    if (taskId) {
      interval = setInterval(async () => {
        try {
          const response = await axios.get(`/api/results/${taskId}`);
          const { state, result, status } = response.data;
          
          setPollingStatus(status || state);

          if (state === 'SUCCESS') {
            // 将结果分离为表格数据和图表数据
            const tableData = result.filter(item => item.property);
            const chartDetails = result.find(item => item.details && item.details.chart_data);
            
            setAnalysisResult(tableData);
            if (chartDetails) {
              setChartData(chartDetails.details.chart_data);
            }
            
            setLoading(false);
            setTaskId(null); // Stop polling
            clearInterval(interval);
            message.success('分析完成！');
          } else if (state === 'FAILURE') {
            setLoading(false);
            setTaskId(null); // Stop polling
            clearInterval(interval);
            message.error(`分析失败: ${status}`);
          }
        } catch (error) {
          console.error('轮询结果失败:', error);
          setLoading(false);
          setTaskId(null);
          clearInterval(interval);
          message.error('无法获取分析结果。');
        }
      }, 2000); // Poll every 2 seconds
    }
    return () => clearInterval(interval); // Cleanup on component unmount
  }, [taskId]);

  const handleUpload = async (options) => {
    const { file } = options;
    const formData = new FormData();
    formData.append('file', file);

    setLoading(true);
    setAnalysisResult(null);
    setChartData([]); // 重置图表数据
    setPollingStatus('正在上传文件...');

    try {
      const response = await axios.post('/api/analyze/df', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      setTaskId(response.data.task_id);
      message.success(`${file.name} 文件已提交分析，请稍候...`);
    } catch (error) {
      console.error('上传失败:', error);
      message.error(`${file.name} 文件上传失败。`);
      setLoading(false);
    }
  };
  
  const props = {
    name: 'file',
    multiple: false,
    customRequest: handleUpload,
    showUploadList: false,
  };

  return (
    <Space direction="vertical" size="large" style={{ display: 'flex', alignItems: 'center' }}>
      <Title level={2}>
        <ApiOutlined /> DF 模型分析
      </Title>
      <Dragger {...props} disabled={loading} style={{width: '700px'}}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽 PCAP 文件到此区域以上传</p>
        <p className="ant-upload-hint">
          支持单个文件的上传分析。
        </p>
      </Dragger>
      
      <Card title="分析结果" style={{width: '700px', marginTop: 20 }}>
        {loading ? (
            <div style={{textAlign: 'center'}}>
                <Spin />
                <p style={{marginTop: 10}}>{pollingStatus}</p>
            </div>
        ) : analysisResult && (
          <>
            <Table columns={columns} dataSource={analysisResult} pagination={false} rowKey="key" />
            {chartData && chartData.length > 0 && (
              <div style={{ marginTop: 20 }}>
                <Title level={4} style={{ textAlign: 'center' }}>流量方向序列图</Title>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart
                    data={chartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="index" label={{ value: 'Packet Sequence', position: 'insideBottom', offset: -5 }} />
                    <YAxis domain={[-1.5, 1.5]} ticks={[-1, 0, 1]} label={{ value: 'Direction', angle: -90, position: 'insideLeft' }} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="direction" stroke="#8884d8" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
          </>
        )}
      </Card>
    </Space>
  );
};

export default DFModelPage; 