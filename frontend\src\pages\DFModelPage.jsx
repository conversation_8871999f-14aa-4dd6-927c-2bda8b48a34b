import React, { useState, useEffect } from 'react';
import { InboxOutlined, ApiOutlined, BarChartOutlined } from '@ant-design/icons';
import { message, Upload, Card, Table, Space, Spin, Typography, Tabs, Row, Col, Button, Statistic, Alert } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import axios from 'axios';

const { Dragger } = Upload;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

const columns = [
  {
    title: '属性',
    dataIndex: 'property',
    key: 'property',
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
  },
];

// 混淆矩阵组件
const ConfusionMatrix = ({ data, classNames }) => {
    const columns = [
        { title: '真实 \\ 预测', dataIndex: 'trueLabel', key: 'trueLabel', width: 120 },
        ...classNames.map((name) => ({
            title: name,
            dataIndex: name,
            key: name,
            align: 'center',
        }))
    ];
    const dataSource = data.map((row, i) => {
        const rowData = { key: i, trueLabel: classNames[i] };
        row.forEach((val, j) => {
            rowData[classNames[j]] = val;
        });
        return rowData;
    });

    return <Table columns={columns} dataSource={dataSource} pagination={false} bordered size="small" />;
};

const DFModelPage = () => {
  // 单文件分析的状态
  const [analysisResult, setAnalysisResult] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [pollingStatus, setPollingStatus] = useState('');

  // 批量评估的状态
  const [pklEvalResult, setPklEvalResult] = useState(null);
  const [pklLoading, setPklLoading] = useState(false);
  const [featureFile, setFeatureFile] = useState(null);
  const [labelFile, setLabelFile] = useState(null);
  const [pklError, setPklError] = useState('');

  // Effect for polling results
  useEffect(() => {
    let interval;
    if (taskId) {
      interval = setInterval(async () => {
        try {
          const response = await axios.get(`/api/results/${taskId}`);
          const { state, result, status } = response.data;
          
          setPollingStatus(status || state);

          if (state === 'SUCCESS') {
            // 将结果分离为表格数据和图表数据
            const tableData = result.filter(item => item.property);
            const chartDetails = result.find(item => item.details && item.details.chart_data);
            
            setAnalysisResult(tableData);
            if (chartDetails) {
              setChartData(chartDetails.details.chart_data);
            }
            
            setLoading(false);
            setTaskId(null); // Stop polling
            clearInterval(interval);
            message.success('分析完成！');
          } else if (state === 'FAILURE') {
            setLoading(false);
            setTaskId(null); // Stop polling
            clearInterval(interval);
            message.error(`分析失败: ${status}`);
          }
        } catch (error) {
          console.error('轮询结果失败:', error);
          setLoading(false);
          setTaskId(null);
          clearInterval(interval);
          message.error('无法获取分析结果。');
        }
      }, 2000); // Poll every 2 seconds
    }
    return () => clearInterval(interval); // Cleanup on component unmount
  }, [taskId]);

  const handleUpload = async (options) => {
    const { file } = options;
    const formData = new FormData();
    formData.append('file', file);

    setLoading(true);
    setAnalysisResult(null);
    setChartData([]); // 重置图表数据
    setPollingStatus('正在上传文件...');

    try {
      const response = await axios.post('/api/analyze/df', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      setTaskId(response.data.task_id);
      message.success(`${file.name} 文件已提交分析，请稍候...`);
    } catch (error) {
      console.error('上传失败:', error);
      message.error(`${file.name} 文件上传失败。`);
      setLoading(false);
    }
  };
  
  // 批量评估的处理函数
  const handlePklSubmit = async () => {
    if (!featureFile || !labelFile) {
      setPklError('请同时上传特征文件 (X_test.pkl) 和标签文件 (y_test.pkl)。');
      return;
    }

    const formData = new FormData();
    formData.append('test_x_file', featureFile);
    formData.append('test_y_file', labelFile);

    setPklLoading(true);
    setPklError('');
    setPklEvalResult(null);

    try {
      const response = await axios.post('/api/evaluate/df_pkl', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      setPklEvalResult(response.data);
      message.success('DF模型批量评估成功！');
    } catch (err) {
      setPklError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
      message.error('DF模型批量评估失败！');
    } finally {
      setPklLoading(false);
    }
  };

  const props = {
    name: 'file',
    multiple: false,
    customRequest: handleUpload,
    showUploadList: false,
  };

  return (
    <Space direction="vertical" size="large" style={{ display: 'flex' }}>
      <Title level={2}>
        <ApiOutlined /> DF 模型分析
      </Title>

      <Tabs defaultActiveKey="1" type="card">
        <TabPane tab="PCAP 文件分析" key="1">
          <Space direction="vertical" size="large" style={{ display: 'flex', alignItems: 'center' }}>
            <Dragger {...props} disabled={loading} style={{width: '700px'}}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽 PCAP 文件到此区域以上传</p>
              <p className="ant-upload-hint">
                支持单个文件的上传分析。
              </p>
            </Dragger>

            <Card title="分析结果" style={{width: '700px', marginTop: 20 }}>
              {loading ? (
                  <div style={{textAlign: 'center'}}>
                      <Spin />
                      <p style={{marginTop: 10}}>{pollingStatus}</p>
                  </div>
              ) : analysisResult && (
                <>
                  <Table columns={columns} dataSource={analysisResult} pagination={false} rowKey="key" />
                  {chartData && chartData.length > 0 && (
                    <div style={{ marginTop: 20 }}>
                      <Title level={4} style={{ textAlign: 'center' }}>流量方向序列图</Title>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart
                          data={chartData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="index" label={{ value: 'Packet Sequence', position: 'insideBottom', offset: -5 }} />
                          <YAxis domain={[-1.5, 1.5]} ticks={[-1, 0, 1]} label={{ value: 'Direction', angle: -90, position: 'insideLeft' }} />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="direction" stroke="#8884d8" activeDot={{ r: 8 }} />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                </>
              )}
            </Card>
          </Space>
        </TabPane>

        <TabPane tab={<span><BarChartOutlined /> DF 模型 PKL 评估</span>} key="2">
          <Space direction="vertical" size="large" style={{width: '700px'}}>
            <Card title="上传评估文件">
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>特征文件 (X_test.pkl)</Text>
                  <Upload maxCount={1} onChange={({file}) => setFeatureFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!featureFile} onRemove={() => setFeatureFile(null)}>
                    <Button>选择文件</Button>
                  </Upload>
                </Col>
                <Col span={12}>
                  <Text strong>标签文件 (y_test.pkl)</Text>
                  <Upload maxCount={1} onChange={({file}) => setLabelFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!labelFile} onRemove={() => setLabelFile(null)}>
                    <Button>选择文件</Button>
                  </Upload>
                </Col>
              </Row>
              <Button
                type="primary"
                onClick={handlePklSubmit}
                disabled={pklLoading || !featureFile || !labelFile}
                loading={pklLoading}
                style={{ marginTop: 20 }}
              >
                {pklLoading ? '正在评估...' : '开始评估'}
              </Button>
            </Card>

            {pklError && <Alert message={pklError} type="error" showIcon style={{marginTop: 20}} />}

            {pklEvalResult && (
               <Card title="DF 模型评估报告" style={{marginTop: 20}}>
                   <Row gutter={16} style={{ marginBottom: 24 }}>
                       <Col span={6}><Card><Statistic title="准确率" value={pklEvalResult.accuracy} /></Card></Col>
                       <Col span={6}><Card><Statistic title="精确率" value={pklEvalResult.precision} /></Card></Col>
                       <Col span={6}><Card><Statistic title="召回率" value={pklEvalResult.recall} /></Card></Col>
                       <Col span={6}><Card><Statistic title="F1分数" value={pklEvalResult.f1_score} /></Card></Col>
                   </Row>
                   <Title level={4} style={{marginTop: 24}}>混淆矩阵</Title>
                   <ConfusionMatrix data={pklEvalResult.confusion_matrix} classNames={pklEvalResult.class_names} />
               </Card>
            )}
          </Space>
        </TabPane>
      </Tabs>
    </Space>
  );
};

export default DFModelPage; 