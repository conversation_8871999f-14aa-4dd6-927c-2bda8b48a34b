import React, { useState } from 'react';
import { Upload, Button, message, Card, Typography } from 'antd';
import { UploadOutlined, SolutionOutlined } from '@ant-design/icons';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

const { Title, Paragraph } = Typography;

const ETBERTBehaviorPage = () => {
  const [file, setFile] = useState(null);
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [protocolCounts, setProtocolCounts] = useState(null);

  const handleFileChange = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-1);

    if (newFileList.length > 0) {
      const isTsv = newFileList[0].name.endsWith('.tsv');
      if (!isTsv) {
        message.error('你只能上传 .tsv 文件!');
        setFileList([]);
        setFile(null);
        return;
      }
    }

    setFileList(newFileList);

    if (newFileList.length > 0) {
      setFile(newFileList[0].originFileObj);
    } else {
      setFile(null);
    }
  };

  const handlePredict = async () => {
    if (!file) {
      message.error('请先选择一个文件!');
      return;
    }

    setLoading(true);
    setProtocolCounts(null);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post('http://localhost:8000/api/et-bert-behavior/predict', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      setProtocolCounts(response.data.protocol_counts);
      message.success('预测成功!');
    } catch (error) {
      const errorMessage = error.response ? error.response.data.detail : '预测请求失败';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getChartOptions = () => {
    if (!protocolCounts) {
      return {};
    }

    const protocols = Object.keys(protocolCounts);
    const counts = Object.values(protocolCounts);

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: protocols,
        axisLabel: {
          rotate: 45,
          interval: 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: counts,
        type: 'bar',
        barWidth: '60%',
      }]
    };
  };

  const props = {
    fileList,
    onChange: handleFileChange,
    onRemove: () => {
      setFileList([]);
      setFile(null);
    },
    beforeUpload: () => false,
    multiple: false,
    accept: ".tsv",
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '0px' }}>
        <SolutionOutlined /> ET-BERT 行为识别支持协议统计
      </Title>
      <Paragraph type="secondary" style={{ marginBottom: '24px' }}>
        上传一个包含网络流量行为序列的 .tsv 文件，系统将使用微调的 ET-BERT 模型统计支持的协议类别及相应个数。
      </Paragraph>

      <Card>
        <Upload {...props}>
          <Button icon={<UploadOutlined />}>选择 .tsv 文件</Button>
        </Upload>
        <Button
          type="primary"
          onClick={handlePredict}
          loading={loading}
          style={{ marginTop: 16 }}
        >
          开始预测
        </Button>
      </Card>

      {protocolCounts && (
        <Card title="协议统计结果" style={{ marginTop: 20 }}>
          <ReactECharts option={getChartOptions()} />
        </Card>
      )}
    </div>
  );
};

export default ETBERTBehaviorPage; 