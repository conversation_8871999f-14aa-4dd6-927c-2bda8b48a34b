import React, { useState, useEffect } from 'react';
import { 
    Upload, Button, Input, Table, message, Card, Row, Col, 
    Form, Modal, Progress, Tag, Space, Typography, Spin 
} from 'antd';
import { UploadOutlined, RocketOutlined, ExperimentOutlined, FileTextOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

const ETBERTPage = () => {
    // 页面整体逻辑的状态
    const [tasks, setTasks] = useState([]);
    const [models, setModels] = useState([]);
    const [loadingTasks, setLoadingTasks] = useState(false); // 初始化为false，避免刷新时卡在加载状态
    const [isPolling, setIsPolling] = useState(false);

    // 创建任务表单的状态
    const [form] = Form.useForm();
    const [fileListTrain, setFileListTrain] = useState([]);
    const [fileListTest, setFileListTest] = useState([]);
    const [isCreatingTask, setIsCreatingTask] = useState(false);

    // 预测模态框的状态
    const [isPredictModalVisible, setIsPredictModalVisible] = useState(false);
    const [predicting, setPredicting] = useState(false);
    const [predictionResult, setPredictionResult] = useState(null);
    const [currentModel, setCurrentModel] = useState(null);
    const [textToPredict, setTextToPredict] = useState('');

    // === 新增：预处理任务状态 ===
    const [preprocessFileList, setPreprocessFileList] = useState([]);
    const [isCreatingPreprocessTask, setIsCreatingPreprocessTask] = useState(false);
    const [preprocessTasks, setPreprocessTasks] = useState([]);

    // 文件上传的处理器
    const handleUploadChange = (info, setFileList) => {
        let newFileList = [...info.fileList];
        newFileList = newFileList.slice(-1); // 只保留最后一个文件
        setFileList(newFileList);
    };

    const uploadProps = (fileList, setFileList) => ({
        onRemove: () => setFileList([]),
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info) => handleUploadChange(info, setFileList),
        fileList,
    });

    // === 新增：预处理任务的上传props ===
    const uploadPreprocessProps = {
        onRemove: () => setPreprocessFileList([]),
        beforeUpload: () => false,
        onChange: (info) => handleUploadChange(info, setPreprocessFileList),
        fileList: preprocessFileList,
        accept: ".zip"
    };

    // 获取所有任务和模型 (此函数现在也需要获取预处理任务)
    const fetchTasksAndModels = async () => {
        setLoadingTasks(true);
        try {
            const modelsResponse = await axios.get(`/api/et-bert/models`);
            const completedModels = modelsResponse.data || [];
            setModels(completedModels);
        } catch (error) {
            message.error('获取已完成模型列表失败。');
        } finally {
            setLoadingTasks(false);
        }
    };

    // 轮询更新任务状态 (此effect现在也需要轮询预处理任务)
    useEffect(() => {
        // fetchTasksAndModels(); // <--- 移除此处的自动加载

        const interval = setInterval(async () => {
            const activeTasks = tasks.filter(t => t.status !== 'COMPLETED' && t.status !== 'FAILED');
            if (activeTasks.length > 0) {
                let tasksUpdated = false;
                const updatedTasks = [...tasks];
                for (const task of activeTasks) {
                    try {
                        const response = await axios.get(`/api/et-bert/finetune/${task.task_id}`);
                        const updatedTask = response.data;
                        const taskIndex = updatedTasks.findIndex(t => t.task_id === updatedTask.task_id);
                        if (taskIndex !== -1) {
                            updatedTasks[taskIndex] = updatedTask;
                            tasksUpdated = true;
                        }
                    } catch (error) { console.error(`Failed to fetch status for task ${task.task_id}`, error); }
                }
                if (tasksUpdated) {
                    const newTasks = updatedTasks.filter(t => t.status !== 'COMPLETED');
                    const newModels = updatedTasks.filter(t => t.status === 'COMPLETED');
                    setTasks(newTasks);
                    setModels(prevModels => {
                        const existingModelIds = new Set(prevModels.map(m => m.task_id));
                        const uniqueNewModels = newModels.filter(m => !existingModelIds.has(m.task_id));
                        return [...prevModels, ...uniqueNewModels];
                    });
                }
            }
        }, 5000);

        // === 新增：轮询预处理任务 ===
        const preprocessInterval = setInterval(async () => {
            const activePreprocessTasks = preprocessTasks.filter(t => t.status !== 'COMPLETED' && t.status !== 'FAILED');
            if (activePreprocessTasks.length === 0) return;

            let tasksUpdated = false;
            const updatedTasks = [...preprocessTasks];

            for (const task of activePreprocessTasks) {
                try {
                    const response = await axios.get(`/api/et-bert/preprocess-pcaps/status/${task.task_id}`);
                    const updatedTask = response.data;
                    const taskIndex = updatedTasks.findIndex(t => t.task_id === updatedTask.task_id);
                    if (taskIndex !== -1) {
                        updatedTasks[taskIndex] = updatedTask;
                        tasksUpdated = true;
                    }
                } catch (error) {
                    console.error(`Failed to fetch status for preprocess task ${task.task_id}`, error);
                    const taskIndex = updatedTasks.findIndex(t => t.task_id === task.task_id);
                    if (taskIndex !== -1) {
                        updatedTasks[taskIndex].status = 'FAILED';
                        updatedTasks[taskIndex].details = '无法从后端获取任务状态。';
                        tasksUpdated = true;
                    }
                }
            }
            if (tasksUpdated) {
                setPreprocessTasks(updatedTasks);
            }
        }, 5000);

        return () => {
            clearInterval(interval);
            clearInterval(preprocessInterval); // 清理新的计时器
        };
    }, [tasks, preprocessTasks]); // 依赖中加入preprocessTasks


    // 创建新任务的处理器
    const handleCreateTask = async (values) => {
        if (fileListTrain.length === 0 || fileListTest.length === 0) {
            message.error('请确保上传了训练和测试文件！');
            return;
        }

        const formData = new FormData();
        formData.append('task_name', values.task_name);
        formData.append('train_file', fileListTrain[0].originFileObj);
        formData.append('test_file', fileListTest[0].originFileObj);

        setIsCreatingTask(true);
        try {
            const response = await axios.post(`/api/et-bert/finetune`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            message.success(response.data.message || '任务创建成功！');
            form.resetFields();
            setFileListTrain([]);
            setFileListTest([]);
            
            // 将新任务添加到待轮询列表
            const newTask = {
                task_id: response.data.task_id,
                task_name: values.task_name,
                status: 'PENDING',
                details: '任务已提交，等待后端响应...'
            };
            setTasks(prevTasks => [...prevTasks, newTask]);

        } catch (error) {
            const errorMsg = error.response?.data?.detail || '任务创建失败，请检查后端服务是否可用。';
            message.error(errorMsg);
        } finally {
            setIsCreatingTask(false);
        }
    };

    // === 新增：创建预处理任务的处理器 ===
    const handleCreatePreprocessTask = async () => {
        if (preprocessFileList.length === 0) {
            message.error('请上传包含pcap文件夹的zip文件！');
            return;
        }

        const formData = new FormData();
        formData.append('file', preprocessFileList[0].originFileObj);

        setIsCreatingPreprocessTask(true);
        try {
            const response = await axios.post(`/api/et-bert/preprocess-pcaps`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            message.success(response.data.message || '预处理任务创建成功！');
            setPreprocessFileList([]);
            const newTask = { task_id: response.data.task_id, status: 'PENDING', details: '任务已提交...' };
            setPreprocessTasks(prev => [...prev, newTask]);
        } catch (error) {
            const errorMsg = error.response?.data?.detail || '任务创建失败，请检查后端服务。';
            message.error(errorMsg);
        } finally {
            setIsCreatingPreprocessTask(false);
        }
    };
    
    const taskColumns = [
        { title: '任务名称', dataIndex: 'task_name', key: 'task_name' },
        { title: '任务ID', dataIndex: 'task_id', key: 'task_id', render: (id) => <Text copyable style={{maxWidth: 150}} ellipsis>{id}</Text> },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status, record) => {
                let color = 'geekblue';
                if (status === 'PROGRESS') color = 'processing';
                if (status === 'COMPLETED') color = 'success';
                if (status === 'FAILED') color = 'error';
                if (status === 'PENDING') color = 'gold';
                return <Tag color={color}>{status}</Tag>;
            },
        },
        {
            title: '进度/详情',
            dataIndex: 'details',
            key: 'details',
            render: (details, record) => {
                if (record.status === 'PROGRESS' && record.progress) {
                    return <Progress percent={parseInt(record.progress, 10)} size="small" />;
                }
                return <Text style={{maxWidth: 300}} ellipsis={{ tooltip: details }}>{details}</Text>;
            }
        }
    ];

    const modelColumns = [
        { title: '模型名称', dataIndex: 'task_name', key: 'task_name' },
        { title: '准确率', dataIndex: 'accuracy', key: 'accuracy', render: (acc) => <Tag color="green">{acc}</Tag> },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Button type="primary" size="small" onClick={() => handleOpenPredictModal(record)}>
                    使用此模型预测
                </Button>
            ),
        },
    ];

    // 打开预测模态框
    const handleOpenPredictModal = (model) => {
        setCurrentModel(model);
        setPredictionResult(null);
        setTextToPredict('');
        setIsPredictModalVisible(true);
    };

    // 关闭预测模态框
    const handleClosePredictModal = () => {
        setIsPredictModalVisible(false);
    };

    // 执行预测
    const handlePredict = async () => {
        if (!textToPredict.trim()) {
            message.warn('请输入需要预测的文本内容。');
            return;
        }
        setPredicting(true);
        setPredictionResult(null);
        try {
            const payload = {
                task_id: currentModel.task_id,
                text: textToPredict,
            };
            const response = await axios.post(`/api/et-bert/predict`, payload);
            setPredictionResult(response.data);
            message.success('预测成功！');
        } catch (error) {
            const errorMsg = error.response?.data?.detail || '预测失败。';
            message.error(errorMsg);
        } finally {
            setPredicting(false);
        }
    };

    // === 新增：预处理任务表格的列定义 ===
    const preprocessTaskColumns = [
        { title: '任务ID', dataIndex: 'task_id', key: 'task_id', render: (id) => <Text copyable style={{maxWidth: 150}} ellipsis>{id}</Text> },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status) => {
                let color = 'geekblue';
                if (status === 'PROGRESS') color = 'processing';
                if (status === 'COMPLETED') color = 'success';
                if (status === 'FAILED') color = 'error';
                if (status === 'PENDING') color = 'gold';
                return <Tag color={color}>{status}</Tag>;
            },
        },
        {
            title: '进度/详情',
            key: 'progress',
            render: (_, record) => {
                if (record.status === 'PROGRESS' && record.progress) {
                    return <div style={{minWidth: 180}}>
                        <Progress percent={parseInt(record.progress, 10)} size="small" style={{width: 120, display: 'inline-block', marginRight: 8}} />
                        <span style={{fontSize: 12}}>{record.details}</span>
                    </div>;
                }
                return <Text style={{maxWidth: 300}} ellipsis={{ tooltip: record.details }}>{record.details}</Text>;
            }
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                record.status === 'COMPLETED' ? 
                <Button 
                    type="primary" 
                    size="small" 
                    href={`/api/et-bert/preprocess-pcaps/download/${record.task_id}`}
                >
                    下载TSV
                </Button> 
                : null
            ),
        },
    ];

    return (
        <div style={{ padding: '24px' }}>
            <Title level={2}><ExperimentOutlined /> ET-BERT 模型微调与推理平台</Title>
            <Text>在这里，您可以预处理pcap文件，微调ET-BERT模型，并使用微调后的模型进行推理预测。</Text>
            
            <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
                <Col xs={24} lg={10}>
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                        <Card title={<><FileTextOutlined /> 第一步：预处理 Pcap 数据</>} bordered={false}>
                            <Text type="secondary" style={{display: 'block', marginBottom: 16}}>
                                上传一个 .zip 文件，其中包含按类别分好的pcap文件夹，系统将自动为您生成可用于微调的 train_dataset.tsv 和 test_dataset.tsv 文件。
                            </Text>
                            <Upload {...uploadPreprocessProps}>
                                <Button icon={<UploadOutlined />}>选择 .zip 文件</Button>
                            </Upload>
                            <Button
                                type="primary"
                                onClick={handleCreatePreprocessTask}
                                loading={isCreatingPreprocessTask}
                                style={{ marginTop: 16 }}
                                block
                            >
                                开始转换
                            </Button>
                        </Card>
                         <Card title={<><RocketOutlined /> 第二步：创建微调任务</>} bordered={false}>
                            <Form form={form} layout="vertical" onFinish={handleCreateTask}>
                                <Form.Item name="task_name" label="任务名称" rules={[{ required: true, message: '请输入一个独特的任务名称!' }]}>
                                    <Input placeholder="例如：我的第一次微调" />
                                </Form.Item>
                                <Form.Item name="train_file" label="训练数据集 (train_dataset.tsv)" rules={[{ required: true, message: '请上传训练文件!' }]}>
                                    <Upload {...uploadProps(fileListTrain, setFileListTrain)}>
                                        <Button icon={<UploadOutlined />}>选择 .tsv 文件</Button>
                                    </Upload>
                                </Form.Item>
                                <Form.Item name="test_file" label="测试数据集 (test_dataset.tsv)" rules={[{ required: true, message: '请上传测试文件!' }]}>
                                    <Upload {...uploadProps(fileListTest, setFileListTest)}>
                                        <Button icon={<UploadOutlined />}>选择 .tsv 文件</Button>
                                    </Upload>
                                </Form.Item>
                                <Form.Item>
                                    <Button type="primary" htmlType="submit" loading={isCreatingTask} block>开始微调</Button>
                                </Form.Item>
                            </Form>
                        </Card>
                    </Space>
                </Col>
                <Col xs={24} lg={14}>
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                        <Card title="Pcap 预处理任务">
                             <Table
                                columns={preprocessTaskColumns}
                                dataSource={preprocessTasks}
                                rowKey="task_id"
                                loading={loadingTasks && preprocessTasks.length === 0}
                                pagination={{ pageSize: 5 }}
                            />
                        </Card>
                        <Card title="进行中的微调任务">
                            <Table
                                columns={taskColumns}
                                dataSource={tasks}
                                rowKey="task_id"
                                loading={loadingTasks && tasks.length === 0}
                                pagination={false}
                            />
                        </Card>
                        <Card title="我的模型 (微调完成)">
                           <Table
                                columns={modelColumns}
                                dataSource={models}
                                rowKey="task_id"
                                loading={loadingTasks && models.length === 0}
                                pagination={{ pageSize: 5 }}
                            />
                        </Card>
                    </Space>
                </Col>
            </Row>
            <Modal
                title={<>使用模型 <Tag>{currentModel?.task_name}</Tag> 进行预测</>}
                open={isPredictModalVisible}
                onCancel={handleClosePredictModal}
                footer={[ <Button key="back" onClick={handleClosePredictModal}>取消</Button>, <Button key="submit" type="primary" loading={predicting} onClick={handlePredict}>预测</Button> ]}
            >
                <Input.TextArea rows={6} value={textToPredict} onChange={(e) => setTextToPredict(e.target.value)} placeholder="请输入要分类的文本内容..."/>
                {predicting && <div style={{textAlign: 'center', marginTop: 20}}><Spin /></div>}
                {predictionResult && (
                     <Card style={{marginTop: 20}} title="预测结果">
                        <p><strong>预测类别索引:</strong> {predictionResult.prediction_index}</p>
                        <p><strong>置信度:</strong> <Progress percent={Math.round(predictionResult.confidence * 100)} size="small" /></p>
                    </Card>
                )}
            </Modal>
        </div>
    );
};

export default ETBERTPage; 