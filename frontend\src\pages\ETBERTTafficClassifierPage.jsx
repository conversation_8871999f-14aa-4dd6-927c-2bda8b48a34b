import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Spin,
  Alert,
  Row,
  Col,
  Table,
  Upload,
  message,
  Statistic
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

function ETBERTTafficClassifierPage() {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [results, setResults] = useState(null);

  const props = {
    name: 'file',
    accept: '.tsv',
    beforeUpload: (file) => {
      setFile(file);
      setResults(null);
      setError('');
      return false; // Prevent automatic upload
    },
    onRemove: () => {
        setFile(null);
    },
    maxCount: 1,
  };

  const handleUpload = async () => {
    if (!file) {
      message.error('请先选择一个文件。');
      return;
    }

    setLoading(true);
    setError('');
    setResults(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post('http://localhost:8000/api/et-bert-traffic-classifier/evaluate_traffic', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      setResults(response.data);
      message.success('评估成功！');
    } catch (err) {
      const errorMsg = err.response?.data?.detail || '评估过程中发生未知错误。';
      setError(errorMsg);
      message.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '流量摘要',
      dataIndex: 'text',
      key: 'text',
      render: (text) => <Text ellipsis={{ tooltip: text }}>{text.substring(0, 150)}</Text>,
    },
    {
      title: '真实标签',
      dataIndex: 'true_label_name',
      key: 'true_label_name',
      render: (text, record) => `${text} (${record.true_label_id})`,
    },
    {
      title: '预测标签',
      dataIndex: 'predicted_label_name',
      key: 'predicted_label_name',
      render: (text, record) => `${text} (${record.predicted_label_id})`,
    },
    {
      title: '结果',
      dataIndex: 'is_correct',
      key: 'is_correct',
      render: (is_correct) => (
        <Text color={is_correct ? 'green' : 'red'}>{is_correct ? '正确' : '错误'}</Text>
      ),
    },
  ];

  const recallColumns = [
    {
      title: '类别名称',
      dataIndex: 'label_name',
      key: 'label_name',
      render: (text, record) => `${text} (${record.label_id})`,
    },
    {
      title: '精确率 (Precision)',
      dataIndex: 'precision',
      key: 'precision',
    },
    {
      title: '召回率 (Recall)',
      dataIndex: 'recall',
      key: 'recall',
    },
    {
      title: 'F1 Score',
      dataIndex: 'f1',
      key: 'f1',
    }
  ];

  return (
    <div>
      <Title level={2}>ET-BERT 流量分类模型评估</Title>
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col>
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>选择 .tsv 文件</Button>
            </Upload>
          </Col>
          <Col>
            <Button
              type="primary"
              onClick={handleUpload}
              disabled={!file || loading}
              loading={loading}
            >
              {loading ? '正在评估' : '开始评估'}
            </Button>
          </Col>
        </Row>
        {error && !loading && (
          <Alert message="错误" description={error} type="error" showIcon style={{ marginTop: 16 }} />
        )}
      </Card>

      {loading && (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <Text style={{ display: 'block', marginTop: 16 }}>评估正在进行中，请稍候...</Text>
        </div>
      )}

      {results && (
        <Card>
          <Title level={4}>评估结果</Title>
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
                <Statistic title="文件名" value={results.filename} valueStyle={{fontSize: '1rem'}} />
            </Col>
            <Col span={6}>
                <Statistic title="总样本数" value={results.num_samples} />
            </Col>
            <Col span={6}>
                <Statistic title="正确预测数" value={results.correct_predictions} />
            </Col>
             <Col span={6}>
                <Statistic title="准确率" value={results.accuracy} />
            </Col>
          </Row>
          
          <Title level={5} style={{marginTop: 24}}>详细分类报告</Title>
          <Table
            columns={recallColumns}
            dataSource={results.precision_recall_f1.map((item, index) => ({ ...item, key: index }))}
            pagination={{ pageSize: 10 }}
            style={{ marginBottom: 24 }}
          />

          <Title level={5} style={{marginTop: 24}}>详细预测结果</Title>
          <Table
            columns={columns.filter(col => col.key !== 'text')}
            dataSource={results.details.map((item, index) => ({ ...item, key: index }))}
            rowClassName={(record) => record.is_correct ? 'table-row-correct' : 'table-row-incorrect'}
            pagination={{ pageSize: 10 }}
          />
           <style>{`
            .table-row-correct {
                background-color: rgba(0, 255, 0, 0.05) !important;
            }
            .table-row-incorrect {
                background-color: rgba(255, 0, 0, 0.05) !important;
            }
          `}</style>
        </Card>
      )}
    </div>
  );
}

export default ETBERTTafficClassifierPage; 