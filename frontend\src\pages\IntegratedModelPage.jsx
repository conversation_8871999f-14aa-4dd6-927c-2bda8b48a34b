import React, { useState, useEffect } from 'react';
import { InboxOutlined, ApartmentOutlined, BarChartOutlined } from '@ant-design/icons';
import { message, Upload, Card, Table, Space, Spin, Tag, Typography, Tabs, Row, Col, Button, Statistic, Alert } from 'antd';
import axios from 'axios';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const { Dragger } = Upload;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

// --- PCAP 分析功能的列定义 ---
const mainColumns = [
  {
    title: '模型',
    dataIndex: 'model',
    key: 'model',
  },
  {
    title: '预测类别',
    dataIndex: 'prediction',
    key: 'prediction',
    render: (text, record) => {
      if (record.state === 'PENDING' || record.state === 'STARTED') {
        return <><Spin size="small" /> 分析中...</>;
      }
      if (record.state === 'FAILURE') {
          return <Tag color="error">分析失败</Tag>;
      }
      return text;
    },
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    key: 'confidence',
    render: (text, record) => {
        if (record.state === 'PENDING' || record.state === 'STARTED') {
            return 'N/A';
        }
        if (record.state === 'FAILURE') {
            return <Tag color="error">N/A</Tag>;
        }
        return text;
    }
  },
];

// --- .npy 评估结果的混淆矩阵组件 ---
const ConfusionMatrix = ({ data, classNames }) => {
    const chartData = data.flatMap((row, i) => 
        row.map((value, j) => ({
            trueLabel: classNames[i],
            predictedLabel: classNames[j],
            count: value,
        }))
    );
    // 这里可以根据需要选择更合适的图表来可视化混淆矩阵
    // 简单的表格展示也是一个清晰的选择
    const columns = [
        { title: '真实 \\ 预测', dataIndex: 'trueLabel', key: 'trueLabel', width: 120 },
        ...classNames.map((name) => ({
            title: name,
            dataIndex: name,
            key: name,
            align: 'center',
        }))
    ];
    const dataSource = data.map((row, i) => {
        const rowData = { key: i, trueLabel: classNames[i] };
        row.forEach((val, j) => {
            rowData[classNames[j]] = val;
        });
        return rowData;
    });

    return <Table columns={columns} dataSource={dataSource} pagination={false} bordered size="small" />;
};

const IntegratedModelPage = () => {
    // --- PCAP 分析的状态 ---
    const [pcapAnalysisResult, setPcapAnalysisResult] = useState([]);
    const [pcapLoading, setPcapLoading] = useState(false);
    const [taskId, setTaskId] = useState(null);
    const [pollingStatus, setPollingStatus] = useState('');

    // --- .npy 评估的状态 ---
    const [npyEvalResult, setNpyEvalResult] = useState(null);
    const [npyLoading, setNpyLoading] = useState(false);
    const [featureFile, setFeatureFile] = useState(null);
    const [labelFile, setLabelFile] = useState(null);
    const [npyError, setNpyError] = useState('');

    // --- FS-Net JSON 评估的状态 ---
    const [fsnetJsonResult, setFsnetJsonResult] = useState(null);
    const [fsnetJsonLoading, setFsnetJsonLoading] = useState(false);
    const [jsonFile, setJsonFile] = useState(null);
    const [fsnetJsonError, setFsnetJsonError] = useState('');

    // --- FA-Net PKL 评估的状态 (修正变量名) ---
    const [faNetPklResult, setFaNetPklResult] = useState(null);
    const [faNetPklLoading, setFaNetPklLoading] = useState(false);
    const [pklFile, setPklFile] = useState(null);
    const [faNetPklError, setFaNetPklError] = useState('');

    // --- AppScanner PKL 评估的状态 ---
    const [appScannerPklResult, setAppScannerPklResult] = useState(null);
    const [appScannerPklLoading, setAppScannerPklLoading] = useState(false);
    const [appScannerPklFile, setAppScannerPklFile] = useState(null);
    const [appScannerPklError, setAppScannerPklError] = useState('');

    // --- DF 模型 PKL 评估的状态 ---
    const [dfPklResult, setDfPklResult] = useState(null);
    const [dfPklLoading, setDfPklLoading] = useState(false);
    const [dfFeatureFile, setDfFeatureFile] = useState(null);
    const [dfLabelFile, setDfLabelFile] = useState(null);
    const [dfPklError, setDfPklError] = useState('');

    // --- DF 模型 PKL 评估的状态 ---
    const [dfPklResult, setDfPklResult] = useState(null);
    const [dfPklLoading, setDfPklLoading] = useState(false);
    const [dfFeatureFile, setDfFeatureFile] = useState(null);
    const [dfLabelFile, setDfLabelFile] = useState(null);
    const [dfPklError, setDfPklError] = useState('');


    // --- PCAP 分析的逻辑 ---
    const expandedRowRender = (record) => {
        if (record.model === 'LexNet') {
          const lexnetDetailColumns = [
            { title: '分类', dataIndex: 'class', key: 'class' },
            { title: '流量计数', dataIndex: 'count', key: 'count' },
            { title: '百分比', dataIndex: 'percentage', key: 'percentage', render: p => `${p}%` },
          ];
          const data = record.details || [];
          return data.length > 0 ? <Table columns={lexnetDetailColumns} dataSource={data} pagination={false} rowKey="class" /> : <p>无详细分类信息</p>;
        }
        const detailColumns = [
          { title: '协议', dataIndex: 'protocol', key: 'protocol', render: proto => <Tag color="blue">{proto}</Tag> },
          { title: '源IP', dataIndex: 'source_ip', key: 'source_ip' },
          { title: '源端口', dataIndex: 'source_port', key: 'source_port' },
          { title: '目的IP', dataIndex: 'dest_ip', key: 'dest_ip' },
          { title: '目的端口', dataIndex: 'dest_port', key: 'dest_port' },
          { title: '特征数', dataIndex: 'feature_count', key: 'feature_count' },
        ];
        const data = record.details || [];
        return data.length > 0 ? <Table columns={detailColumns} dataSource={data} pagination={false} rowKey={(item, index) => `${record.key}-detail-${index}`} /> : <p>无详细流量信息</p>;
    };

    // **修复：恢复并修正轮询逻辑**
    useEffect(() => {
        let interval;
        if (taskId) {
            interval = setInterval(async () => {
                try {
                    const response = await axios.get(`/api/results/${taskId}`);
                    const { state, result, status } = response.data;

                    setPollingStatus(status || state);

                    if (result) {
                        setPcapAnalysisResult(result);
                    }

                    if (state === 'SUCCESS' || state === 'FAILURE') {
                        setPcapLoading(false);
                        setTaskId(null); // 停止轮询
                        clearInterval(interval);
                        if (state === 'SUCCESS') {
                            message.success('所有分析任务已完成！');
                        } else {
                            message.error(`分析任务失败: ${status}`);
                        }
                    }
                } catch (error) {
                    console.error('轮询结果失败:', error);
                    setPcapLoading(false);
                    setTaskId(null);
                    clearInterval(interval);
                    message.error('无法获取分析结果。');
                }
            }, 2000);
        }
        return () => clearInterval(interval);
    }, [taskId]);

    const handlePcapUpload = async (options) => {
        const { file } = options;
        const formData = new FormData();
        formData.append('file', file);

        setPcapLoading(true);
        setPcapAnalysisResult([]); // 清空旧结果
        setPollingStatus('正在上传文件...');

        try {
            const response = await axios.post('/api/analyze/integrated', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setTaskId(response.data.task_id);
            message.success(`${file.name} 文件已提交分析，请稍候...`);
        } catch (error) {
            console.error('上传失败:', error);
            message.error(`${file.name} 文件上传失败。`);
            setPcapLoading(false);
        }
    };
    
    // --- .npy 评估的逻辑 ---
    const handleNpySubmit = async () => {
        if (!featureFile || !labelFile) {
            setNpyError('请同时上传特征文件 (test_x.npy) 和标签文件 (test_y.npy)。');
            return;
        }

        const formData = new FormData();
        formData.append('test_x_file', featureFile);
        formData.append('test_y_file', labelFile);
        
        setNpyLoading(true);
        setNpyError('');
        setNpyEvalResult(null);

        try {
            const response = await axios.post('/api/evaluate/lexnet_npy', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setNpyEvalResult(response.data);
            message.success('评估成功！');
        } catch (err) {
            setNpyError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('评估失败！');
        } finally {
            setNpyLoading(false);
        }
    };

    // --- FS-Net JSON 评估的逻辑 ---
    const handleFsnetJsonSubmit = async () => {
        if (!jsonFile) {
            setFsnetJsonError('请上传一个 JSON 格式的测试集文件。');
            return;
        }

        const formData = new FormData();
        formData.append('json_file', jsonFile);
        
        setFsnetJsonLoading(true);
        setFsnetJsonError('');
        setFsnetJsonResult(null);

        try {
            // 注意: 后端端点 /api/evaluate/fsnet_json 需要后续实现
            const response = await axios.post('/api/evaluate/fsnet_json', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setFsnetJsonResult(response.data);
            message.success('FS-Net JSON 评估成功！');
        } catch (err) {
            setFsnetJsonError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('FS-Net JSON 评估失败！');
        } finally {
            setFsnetJsonLoading(false);
        }
    };

    // --- FA-Net PKL 评估的逻辑 (修正函数名和API地址) ---
    const handleFaNetPklSubmit = async () => {
        if (!pklFile) {
            setFaNetPklError('请上传一个 .pkl 格式的测试集缓存文件。');
            return;
        }

        const formData = new FormData();
        formData.append('pkl_file', pklFile);
        
        setFaNetPklLoading(true);
        setFaNetPklError('');
        setFaNetPklResult(null);

        try {
            const response = await axios.post('/api/evaluate/fanet_pkl', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setFaNetPklResult(response.data);
            message.success('FA-Net PKL 评估成功！');
        } catch (err) {
            setFaNetPklError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('FA-Net PKL 评估失败！');
        } finally {
            setFaNetPklLoading(false);
        }
    };

    // --- AppScanner PKL 评估的逻辑 ---
    const handleAppScannerPklSubmit = async () => {
        if (!appScannerPklFile) {
            setAppScannerPklError('请上传一个 .pkl 格式的 AppScanner 测试集文件。');
            return;
        }

        const formData = new FormData();
        formData.append('pkl_file', appScannerPklFile);
        
        setAppScannerPklLoading(true);
        setAppScannerPklError('');
        setAppScannerPklResult(null);

        try {
            const response = await axios.post('/api/evaluate/appscanner_pkl', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setAppScannerPklResult(response.data);
            message.success('AppScanner PKL 评估成功！');
        } catch (err) {
            setAppScannerPklError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('AppScanner PKL 评估失败！');
        } finally {
            setAppScannerPklLoading(false);
        }
    };

    // --- DF 模型 PKL 评估的逻辑 ---
    const handleDfPklSubmit = async () => {
        if (!dfFeatureFile || !dfLabelFile) {
            setDfPklError('请同时上传特征文件 (X_test.pkl) 和标签文件 (y_test.pkl)。');
            return;
        }

        const formData = new FormData();
        formData.append('test_x_file', dfFeatureFile);
        formData.append('test_y_file', dfLabelFile);

        setDfPklLoading(true);
        setDfPklError('');
        setDfPklResult(null);

        try {
            const response = await axios.post('/api/evaluate/df_pkl', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setDfPklResult(response.data);
            message.success('DF 模型 PKL 评估成功！');
        } catch (err) {
            setDfPklError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('DF 模型 PKL 评估失败！');
        } finally {
            setDfPklLoading(false);
        }
    };


    return (
        <Space direction="vertical" size="large" style={{ display: 'flex' }}>
            <Title level={2}><ApartmentOutlined /> 综合模型分析</Title>
            
            <Tabs defaultActiveKey="1" type="card">
                <TabPane tab="PCAP 综合分析" key="1">
                    <Space direction="vertical" size="large" style={{ display: 'flex' }}>
                        <Dragger 
                            name="file"
                            multiple={false}
                            customRequest={handlePcapUpload}
                            showUploadList={false}
                            disabled={pcapLoading} 
                            style={{width: '700px'}}
                        >
                            <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                            <p className="ant-upload-text">点击或拖拽 PCAP 文件到此区域以上传</p>
                            <p className="ant-upload-hint">支持单个文件的上传，将由 Appscanner, FS-Net, FA-Net, LexNet 模型进行综合分析。</p>
                        </Dragger>
                        
                        <Card title="综合分析结果" style={{width: '700px', marginTop: 20 }}>
                            {pcapLoading && (
                                <div style={{textAlign: 'center', marginBottom: '20px'}}>
                                    <Spin />
                                    <p style={{marginTop: 10}}>{pollingStatus}</p>
                                </div>
                            )}
                            <Table 
                                columns={mainColumns} 
                                dataSource={pcapAnalysisResult} 
                                pagination={false} 
                                rowKey={(record) => record.key}
                                expandable={{
                                    expandedRowRender,
                                    rowExpandable: record => record.details && record.details.length > 0,
                                }}
                            />
                        </Card>
                    </Space>
                </TabPane>

                <TabPane tab={<span><BarChartOutlined /> LEXNet 评估</span>} key="2">
                    <Space direction="vertical" size="large" style={{width: '700px'}}>
                        <Card title="上传评估文件">
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Text strong>特征文件 (test_x.npy)</Text>
                                    <Upload maxCount={1} onChange={({file}) => setFeatureFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!featureFile} onRemove={() => setFeatureFile(null)}>
                                        <Button>选择文件</Button>
                                    </Upload>
                                </Col>
                                <Col span={12}>
                                    <Text strong>标签文件 (test_y.npy)</Text>
                                    <Upload maxCount={1} onChange={({file}) => setLabelFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!labelFile} onRemove={() => setLabelFile(null)}>
                                        <Button>选择文件</Button>
                                    </Upload>
                                </Col>
                            </Row>
                            <Button
                                type="primary"
                                onClick={handleNpySubmit}
                                disabled={npyLoading || !featureFile || !labelFile}
                                loading={npyLoading}
                                style={{ marginTop: 20 }}
                            >
                                {npyLoading ? '正在评估...' : '开始评估'}
                            </Button>
                        </Card>

                        {npyError && <Alert message={npyError} type="error" showIcon style={{marginTop: 20}} />}

                        {npyEvalResult && (
                             <Card title="评估报告" style={{marginTop: 20}}>
                                 <Row gutter={16} style={{ marginBottom: 24 }}>
                                     <Col span={6}><Card><Statistic title="准确率" value={npyEvalResult.accuracy} /></Card></Col>
                                     <Col span={6}><Card><Statistic title="精确率" value={npyEvalResult.precision} /></Card></Col>
                                     <Col span={6}><Card><Statistic title="召回率" value={npyEvalResult.recall} /></Card></Col>
                                     <Col span={6}><Card><Statistic title="F1分数" value={npyEvalResult.f1_score} /></Card></Col>
                                 </Row>
                                 <Title level={4} style={{marginTop: 24}}>混淆矩阵</Title>
                                 <ConfusionMatrix data={npyEvalResult.confusion_matrix} classNames={npyEvalResult.class_names} />
                             </Card>
                        )}
                    </Space>
                </TabPane>

                <TabPane tab={<span><BarChartOutlined /> FS-Net JSON 评估</span>} key="3">
                    <Space direction="vertical" size="large" style={{width: '700px'}}>
                        <Card title="上传评估文件">
                            <Text strong>测试集文件 (test.json)</Text>
                            <Upload beforeUpload={file => { setJsonFile(file); return false; }} showUploadList={false} accept=".json">
                                <Button>选择 JSON 文件</Button>
                            </Upload>
                            {jsonFile && <Text style={{ marginLeft: 8 }}>{jsonFile.name}</Text>}
                            <Button type="primary" onClick={handleFsnetJsonSubmit} loading={fsnetJsonLoading} style={{ marginTop: 16 }}>开始评估</Button>
                            {fsnetJsonError && <Alert message={fsnetJsonError} type="error" style={{ marginTop: 16 }} />}
                        </Card>
                        
                        {fsnetJsonLoading && <div style={{textAlign: 'center', marginTop: 20}}><Spin tip="正在评估..." /></div>}

                        {fsnetJsonResult && (
                             <Card title="FS-Net 评估结果" style={{marginTop: 20}}>
                                <Row gutter={16}>
                                    <Col span={8}><Statistic title="准确率 (Accuracy)" value={fsnetJsonResult.accuracy} precision={4} /></Col>
                                    <Col span={8}><Statistic title="精确率 (Precision)" value={fsnetJsonResult.precision} precision={4} /></Col>
                                    <Col span={8}><Statistic title="召回率 (Recall)" value={fsnetJsonResult.recall} precision={4} /></Col>
                                    <Col span={8}><Statistic title="F1 分数" value={fsnetJsonResult.f1_score} precision={4} /></Col>
                                </Row>
                                <Title level={5} style={{marginTop: 20}}>混淆矩阵</Title>
                                <ConfusionMatrix data={fsnetJsonResult.confusion_matrix} classNames={fsnetJsonResult.class_names} />
                            </Card>
                        )}
                    </Space>
                </TabPane>

                <TabPane tab={<span><BarChartOutlined /> FA-Net PKL 评估</span>} key="4">
                    <Space direction="vertical" size="large" style={{width: '700px'}}>
                        <Card title="上传评估文件">
                            <Text strong>测试集缓存 (test_data.pkl)</Text>
                            <Upload beforeUpload={file => { setPklFile(file); return false; }} showUploadList={false} accept=".pkl">
                                <Button>选择 PKL 文件</Button>
                            </Upload>
                            {pklFile && <Text style={{ marginLeft: 8 }}>{pklFile.name}</Text>}
                            <Button type="primary" onClick={handleFaNetPklSubmit} loading={faNetPklLoading} style={{ marginTop: 16 }}>开始评估</Button>
                            {faNetPklError && <Alert message={faNetPklError} type="error" style={{ marginTop: 16 }} />}
                        </Card>
                        
                        {faNetPklLoading && <div style={{textAlign: 'center', marginTop: 20}}><Spin tip="正在评估..." /></div>}

                        {faNetPklResult && (
                             <Card title="FA-Net 评估结果" style={{marginTop: 20}}>
                                <Row gutter={16}>
                                    <Col span={8}><Statistic title="准确率 (Accuracy)" value={faNetPklResult.accuracy} precision={4} /></Col>
                                    <Col span={8}><Statistic title="精确率 (Precision)" value={faNetPklResult.precision} precision={4} /></Col>
                                    <Col span={8}><Statistic title="召回率 (Recall)" value={faNetPklResult.recall} precision={4} /></Col>
                                    <Col span={8}><Statistic title="F1 分数" value={faNetPklResult.f1_score} precision={4} /></Col>
                                </Row>
                                <Title level={5} style={{marginTop: 20}}>混淆矩阵</Title>
                                <ConfusionMatrix data={faNetPklResult.confusion_matrix} classNames={faNetPklResult.class_names} />
                            </Card>
                        )}
                    </Space>
                </TabPane>

                <TabPane tab={<span><BarChartOutlined /> AppScanner PKL 评估</span>} key="5">
                    <Space direction="vertical" size="large" style={{width: '700px'}}>
                        <Card title="上传评估文件">
                            <Text strong>测试集文件 (appscanner_test_set.pkl)</Text>
                            <Upload beforeUpload={file => { setAppScannerPklFile(file); return false; }} showUploadList={false} accept=".pkl">
                                <Button>选择 PKL 文件</Button>
                            </Upload>
                            {appScannerPklFile && <Text style={{ marginLeft: 8 }}>{appScannerPklFile.name}</Text>}
                            <Button type="primary" onClick={handleAppScannerPklSubmit} loading={appScannerPklLoading} style={{ marginTop: 16 }}>开始评估</Button>
                            {appScannerPklError && <Alert message={appScannerPklError} type="error" style={{ marginTop: 16 }} />}
                        </Card>
                        
                        {appScannerPklLoading && <div style={{textAlign: 'center', marginTop: 20}}><Spin tip="正在评估..." /></div>}

                        {appScannerPklResult && (
                             <Card title="AppScanner 评估结果" style={{marginTop: 20}}>
                                 {/* AppScanner 通常不提供单一的置信度，但提供准确率等指标 */}
                                <Row gutter={16}>
                                    <Col span={12}><Statistic title="准确率 (Accuracy)" value={appScannerPklResult.accuracy} precision={4} /></Col>
                                    <Col span={12}><Statistic title="样本总数" value={appScannerPklResult.total_samples} /></Col>
                                </Row>
                                <Title level={5} style={{marginTop: 20}}>分类报告</Title>
                                <pre style={{whiteSpace: 'pre-wrap', wordBreak: 'break-all', background: '#f5f5f5', padding: '12px'}}>
                                    {appScannerPklResult.classification_report}
                                </pre>
                                <Title level={5} style={{marginTop: 20}}>混淆矩阵</Title>
                                <ConfusionMatrix data={appScannerPklResult.confusion_matrix} classNames={appScannerPklResult.class_names} />
                            </Card>
                        )}
                    </Space>
                </TabPane>

                <TabPane tab={<span><BarChartOutlined /> DF 模型 PKL 评估</span>} key="6">
                    <Space direction="vertical" size="large" style={{width: '700px'}}>
                        <Card title="上传评估文件">
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Text strong>特征文件 (X_test.pkl)</Text>
                                    <Upload maxCount={1} onChange={({file}) => setDfFeatureFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!dfFeatureFile} onRemove={() => setDfFeatureFile(null)}>
                                        <Button>选择文件</Button>
                                    </Upload>
                                </Col>
                                <Col span={12}>
                                    <Text strong>标签文件 (y_test.pkl)</Text>
                                    <Upload maxCount={1} onChange={({file}) => setDfLabelFile(file.originFileObj || file)} beforeUpload={() => false} showUploadList={!!dfLabelFile} onRemove={() => setDfLabelFile(null)}>
                                        <Button>选择文件</Button>
                                    </Upload>
                                </Col>
                            </Row>
                            <Button
                                type="primary"
                                onClick={handleDfPklSubmit}
                                disabled={dfPklLoading || !dfFeatureFile || !dfLabelFile}
                                loading={dfPklLoading}
                                style={{ marginTop: 20 }}
                            >
                                {dfPklLoading ? '正在评估...' : '开始评估'}
                            </Button>
                        </Card>

                        {dfPklLoading && <div style={{textAlign: 'center', marginTop: 20}}><Spin tip="正在评估..." /></div>}

                        {dfPklError && <Alert message={dfPklError} type="error" style={{ marginTop: 16 }} />}

                        {dfPklResult && (
                             <Card title="DF 模型评估结果" style={{marginTop: 20}}>
                                <Row gutter={16}>
                                    <Col span={6}><Statistic title="准确率 (Accuracy)" value={dfPklResult.accuracy} precision={4} /></Col>
                                    <Col span={6}><Statistic title="精确率 (Precision)" value={dfPklResult.precision} precision={4} /></Col>
                                    <Col span={6}><Statistic title="召回率 (Recall)" value={dfPklResult.recall} precision={4} /></Col>
                                    <Col span={6}><Statistic title="F1 分数" value={dfPklResult.f1_score} precision={4} /></Col>
                                </Row>
                                <Title level={5} style={{marginTop: 20}}>混淆矩阵</Title>
                                <ConfusionMatrix data={dfPklResult.confusion_matrix} classNames={dfPklResult.class_names} />
                            </Card>
                        )}
                    </Space>
                </TabPane>

            </Tabs>
        </Space>
    );
};

export default IntegratedModelPage; 