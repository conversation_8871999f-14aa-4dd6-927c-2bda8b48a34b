
import React from 'react';
import { Card, Row, Col, Typography, Button } from 'antd';
import { Link } from 'react-router-dom';
import { ApiOutlined, ApartmentOutlined, CameraOutlined, ExperimentOutlined, SolutionOutlined, DashboardOutlined, SecurityScanOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const models = [
  {
    path: '/df',
    icon: <ApiOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
    title: 'DF 模型分析',
    description: '基于流量方向序列的深度学习模型，用于快速识别基础流量类别。'
  },
  {
    path: '/integrated',
    icon: <ApartmentOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
    title: '综合模型分析',
    description: '并行运行多个先进模型 (AppScanner, FS-Net, FA-Net, LexNet) 对流量进行全方位、多维度的综合研判。'
  },
  {
    path: '/yatc',
    icon: <CameraOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
    title: 'YaTC 模型分析',
    description: '一个专注于特定流量特征分析的模型，提供独特的视角。'
  },
  {
    path: '/et-bert',
    icon: <ExperimentOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
    title: 'ET-BERT 快速学习',
    description: '上传您自己的数据集，对强大的 ET-BERT 模型进行微调，以适应特定的识别任务。'
  },
  {
    path: '/et-bert-behavior',
    icon: <SolutionOutlined style={{ fontSize: '24px', color: '#eb2f96' }} />,
    title: 'ET-BERT 行为识别支持协议统计',
    description: '利用预训练的 ET-BERT 模型，对网络流量进行深层次的行为模式识别。'
  },
  {
    path: '/et-bert-traffic-classifier',
    icon: <SecurityScanOutlined style={{ fontSize: '24px', color: '#f5222d' }} />,
    title: 'ET-BERT 流量分类',
    description: '使用强大的 ET-BERT 模型对网络流量进行精确的类别判定。'
  }
];

const SystemOverviewPage = () => {
  return (
    <div>
      <Title level={2} style={{ marginBottom: '0px' }}>
        <DashboardOutlined /> 网络流量分析原型系统
      </Title>
      <Paragraph type="secondary" style={{ marginBottom: '24px' }}>
        欢迎使用本系统。这里集成了多种先进的网络流量分析模型，旨在为您提供全面、深入的流量洞察能力。
        请从下面的功能模块中选择一个开始您的分析任务。
      </Paragraph>
      
      <Row gutter={[24, 24]}>
        {models.map(model => (
          <Col xs={24} sm={12} lg={8} key={model.path}>
            <Card
              title={<span>{model.icon} &nbsp; {model.title}</span>}
              hoverable
              actions={[
                <Link to={model.path}>
                  <Button type="primary" key="analyze">
                    开始分析
                  </Button>
                </Link>
              ]}
              style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
              bodyStyle={{ flexGrow: 1 }}
            >
              <Paragraph>{model.description}</Paragraph>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default SystemOverviewPage; 