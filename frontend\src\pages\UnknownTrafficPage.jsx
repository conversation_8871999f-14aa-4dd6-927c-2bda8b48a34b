
import React, { useState, useEffect, useRef } from 'react';
import { Upload, Button, message, Card, Row, Col, Typography, Spin, Divider, Statistic, Tag } from 'antd';
import { UploadOutlined, InboxOutlined, FileTextOutlined, DownloadOutlined, SyncOutlined } from '@ant-design/icons';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

const { Dragger } = Upload;
const { Title, Text, Link } = Typography;

const POLLING_INTERVAL = 3000; // 3 seconds

const UnknownTrafficPage = () => {
  // --- State Management ---
  // Step 1: Feature Extraction
  const [pcapFile, setPcapFile] = useState(null);
  const [extractionTaskId, setExtractionTaskId] = useState(null);
  const [extractionStatus, setExtractionStatus] = useState(null);
  const [extractionResult, setExtractionResult] = useState(null);
  const extractionIntervalRef = useRef(null);

  // Step 2: Prediction
  const [tsvFile, setTsvFile] = useState(null);
  const [predictionTaskId, setPredictionTaskId] = useState(null);
  const [predictionStatus, setPredictionStatus] = useState(null);
  const [predictionResult, setPredictionResult] = useState(null);
  const predictionIntervalRef = useRef(null);

  // Step 3a: Contrastive Feature Extraction
  const [contrastiveFile, setContrastiveFile] = useState(null);
  const [contrastiveTaskId, setContrastiveTaskId] = useState(null);
  const [contrastiveStatus, setContrastiveStatus] = useState(null);
  const [contrastiveResult, setContrastiveResult] = useState(null);
  const contrastiveIntervalRef = useRef(null);

  // Step 3b: Clustering Analysis
  const [clusteringFile, setClusteringFile] = useState(null);
  const [clusteringTaskId, setClusteringTaskId] = useState(null);
  const [clusteringStatus, setClusteringStatus] = useState(null);
  const [clusteringResult, setClusteringResult] = useState(null);
  const clusteringIntervalRef = useRef(null);

  // --- Task Polling Logic ---
  const pollTaskStatus = async (taskId, setStatus, setResult, intervalRef) => {
    try {
      const response = await axios.get(`/api/unknown_traffic/results/${taskId}`);
      const { state, result } = response.data;

      if (state === 'SUCCESS' || state === 'FAILURE') {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        setResult(result);
        setStatus(result.status || state);
        if (state === 'SUCCESS') {
            message.success(result.message || '任务成功完成！');
        } else {
            message.error(result.error || '任务失败！');
        }
      } else { // PENDING or PROGRESS
        setStatus(result?.status || state);
      }
    } catch (error) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      const errorMsg = error.response?.data?.detail || '轮询任务状态失败。';
      message.error(errorMsg);
      setStatus('ERROR');
      setResult({ error: errorMsg });
    }
  };

  // Cleanup intervals on component unmount
  useEffect(() => {
    return () => {
      if (extractionIntervalRef.current) clearInterval(extractionIntervalRef.current);
      if (predictionIntervalRef.current) clearInterval(predictionIntervalRef.current);
      if (contrastiveIntervalRef.current) clearInterval(contrastiveIntervalRef.current);
      if (clusteringIntervalRef.current) clearInterval(clusteringIntervalRef.current);
    };
  }, []);

  // --- Handlers ---
  const handlePcapUpload = (file) => {
    if (file.name.endsWith('.zip')) {
      setPcapFile(file);
      setExtractionResult(null);
      setExtractionStatus(null);
      setExtractionTaskId(null);
    } else {
      message.error('请上传.zip格式的压缩文件。');
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleStartExtraction = async () => {
    if (!pcapFile) {
      message.warning('请先选择一个pcap压缩文件。');
      return;
    }
    const formData = new FormData();
    formData.append('file', pcapFile);

    setExtractionStatus('UPLOADING');
    try {
      const response = await axios.post('/api/unknown_traffic/extract_features', formData);
      const { task_id } = response.data;
      setExtractionTaskId(task_id);
      setExtractionStatus('STARTED');
      
      if (extractionIntervalRef.current) clearInterval(extractionIntervalRef.current);
      extractionIntervalRef.current = setInterval(() => {
        pollTaskStatus(task_id, setExtractionStatus, setExtractionResult, extractionIntervalRef);
      }, POLLING_INTERVAL);

    } catch (error) {
      const errorMsg = error.response?.data?.detail || '启动特征提取任务失败。';
      message.error(errorMsg);
      setExtractionStatus('ERROR');
      setExtractionResult({ error: errorMsg });
    }
  };

  const handleTsvUpload = (file) => {
    if (file.name.endsWith('.tsv')) {
        setTsvFile(file);
        setPredictionResult(null);
        setPredictionStatus(null);
        setPredictionTaskId(null);
    } else {
      message.error('请上传.tsv格式的特征文件。');
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleStartPrediction = async () => {
    if (!tsvFile) {
      message.warning('请先选择一个特征文件 (.tsv)。');
      return;
    }
    const formData = new FormData();
    formData.append('file', tsvFile);

    setPredictionStatus('UPLOADING');
    try {
      const response = await axios.post('/api/unknown_traffic/predict', formData);
      const { task_id } = response.data;
      setPredictionTaskId(task_id);
      setPredictionStatus('STARTED');

      if (predictionIntervalRef.current) clearInterval(predictionIntervalRef.current);
      predictionIntervalRef.current = setInterval(() => {
        pollTaskStatus(task_id, setPredictionStatus, setPredictionResult, predictionIntervalRef);
      }, POLLING_INTERVAL);

    } catch (error) {
        const errorMsg = error.response?.data?.detail || '启动模型分析任务失败。';
        message.error(errorMsg);
        setPredictionStatus('ERROR');
        setPredictionResult({ error: errorMsg });
    }
  };

  // Step 3a: Contrastive Feature Extraction handlers
  const handleContrastiveUpload = (file) => {
    if (file.name.endsWith('.tsv')) {
      setContrastiveFile(file);
      setContrastiveResult(null);
      setContrastiveStatus(null);
      setContrastiveTaskId(null);
    } else {
      message.error('请上传.tsv格式的预测结果文件。');
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleStartContrastiveExtraction = async () => {
    if (!contrastiveFile) {
      message.warning('请先选择一个预测结果文件 (.tsv)。');
      return;
    }
    const formData = new FormData();
    formData.append('file', contrastiveFile);

    setContrastiveStatus('UPLOADING');
    try {
      const response = await axios.post('/api/unknown_traffic/extract_contrastive_features', formData);
      const { task_id } = response.data;
      setContrastiveTaskId(task_id);
      setContrastiveStatus('STARTED');

      if (contrastiveIntervalRef.current) clearInterval(contrastiveIntervalRef.current);
      contrastiveIntervalRef.current = setInterval(() => {
        pollTaskStatus(task_id, setContrastiveStatus, setContrastiveResult, contrastiveIntervalRef);
      }, POLLING_INTERVAL);

    } catch (error) {
      const errorMsg = error.response?.data?.detail || '启动对比学习特征提取任务失败。';
      message.error(errorMsg);
      setContrastiveStatus('ERROR');
      setContrastiveResult({ error: errorMsg });
    }
  };

  // Step 3b: Clustering Analysis handlers
  const handleClusteringUpload = (file) => {
    if (file.name.endsWith('.tsv')) {
      setClusteringFile(file);
      setClusteringResult(null);
      setClusteringStatus(null);
      setClusteringTaskId(null);
    } else {
      message.error('请上传.tsv格式的特征文件。');
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleStartClustering = async () => {
    if (!clusteringFile) {
      message.warning('请先选择一个特征文件 (.tsv)。');
      return;
    }
    const formData = new FormData();
    formData.append('file', clusteringFile);

    setClusteringStatus('UPLOADING');
    try {
      const response = await axios.post('/api/unknown_traffic/perform_clustering', formData);
      const { task_id } = response.data;
      setClusteringTaskId(task_id);
      setClusteringStatus('STARTED');

      if (clusteringIntervalRef.current) clearInterval(clusteringIntervalRef.current);
      clusteringIntervalRef.current = setInterval(() => {
        pollTaskStatus(task_id, setClusteringStatus, setClusteringResult, clusteringIntervalRef);
      }, POLLING_INTERVAL);

    } catch (error) {
      const errorMsg = error.response?.data?.detail || '启动聚类分析任务失败。';
      message.error(errorMsg);
      setClusteringStatus('ERROR');
      setClusteringResult({ error: errorMsg });
    }
  };

  // --- Render Functions ---
  const isExtracting = extractionStatus && !['SUCCESS', 'FAILURE', 'ERROR'].includes(extractionStatus);
  const isPredicting = predictionStatus && !['SUCCESS', 'FAILURE', 'ERROR'].includes(predictionStatus);
  const isContrastiveExtracting = contrastiveStatus && !['SUCCESS', 'FAILURE', 'ERROR'].includes(contrastiveStatus);
  const isClustering = clusteringStatus && !['SUCCESS', 'FAILURE', 'ERROR'].includes(clusteringStatus);

  const renderConfusionMatrix = (matrix) => {
    if (!matrix) return null;
    return (
      <div style={{ textAlign: 'center' }}>
        <Title level={5} style={{ marginBottom: '20px' }}>混淆矩阵</Title>
        <table style={{ margin: 'auto', borderCollapse: 'collapse' }}>
          <thead>
            <tr>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}></th>
              <th colSpan="2" style={{ border: '1px solid #f0f0f0', padding: '10px' }}>预测值</th>
            </tr>
            <tr>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}>真实值</th>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}>已知 (0)</th>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}>未知 (1)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}>已知 (0)</th>
              <td style={{ border: '1px solid #f0f0f0', padding: '10px', background: '#e6f7ff' }}>{matrix.true_known_pred_known}</td>
              <td style={{ border: '1px solid #f0f0f0', padding: '10px' }}>{matrix.true_known_pred_unknown}</td>
            </tr>
            <tr>
              <th style={{ border: '1px solid #f0f0f0', padding: '10px' }}>未知 (1)</th>
              <td style={{ border: '1px solid #f0f0f0', padding: '10px' }}>{matrix.true_unknown_pred_known}</td>
              <td style={{ border: '1px solid #f0f0f0', padding: '10px', background: '#e6f7ff' }}>{matrix.true_unknown_pred_unknown}</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  };

  const renderClusteringResults = (result) => {
    if (!result) return null;

    // 调试信息
    console.log('Clustering result:', result);
    console.log('Scatter data:', result.scatter_data);

    // 生成聚类颜色
    const generateClusterColors = (nClusters) => {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
        '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
      ];

      const clusterColors = {};
      for (let i = 0; i < nClusters; i++) {
        clusterColors[i] = colors[i % colors.length];
      }
      clusterColors['-1'] = '#95A5A6'; // 噪声点用灰色
      return clusterColors;
    };

    const clusterColors = generateClusterColors(result.n_clusters);

    // 准备散点图数据
    const getScatterOption = () => {
      if (!result.scatter_data || result.scatter_data.length === 0) {
        return null;
      }

      // 按聚类分组数据
      const seriesData = {};
      result.scatter_data.forEach(point => {
        const clusterId = point.cluster_id.toString();
        if (!seriesData[clusterId]) {
          seriesData[clusterId] = [];
        }
        seriesData[clusterId].push({
          value: [point.x, point.y],
          itemStyle: {
            color: clusterColors[clusterId] || '#95A5A6'
          },
          // 存储额外信息用于tooltip
          sni: point.sni || 'N/A',
          trueLabel: point.true_label || 'Unknown',
          textPreview: point.text_preview || 'N/A',
          clusterId: clusterId
        });
      });

      const series = Object.entries(seriesData).map(([clusterId, data]) => ({
        name: clusterId === '-1' ? '噪声点' : `聚类 ${clusterId}`,
        type: 'scatter',
        data: data,
        symbolSize: 8,
        itemStyle: {
          color: clusterColors[clusterId]
        }
      }));

      return {
        title: {
          text: '聚类散点图',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const data = params.data;
            return `
              <div style="max-width: 300px;">
                <strong>${params.seriesName}</strong><br/>
                坐标: (${data.value[0].toFixed(3)}, ${data.value[1].toFixed(3)})<br/>
                <strong>SNI:</strong> ${data.sni}<br/>
              </div>
            `;
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          type: 'scroll'
        },
        xAxis: {
          type: 'value',
          name: 'UMAP维度1'
        },
        yAxis: {
          type: 'value',
          name: 'UMAP维度2'
        },
        series: series,
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          }
        },
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0
          },
          {
            type: 'inside',
            yAxisIndex: 0
          }
        ]
      };
    };

    const scatterOption = getScatterOption();

    return (
      <div>
        {/* 统计信息 */}
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic title="聚类数量" value={result.n_clusters} />
          </Col>
          <Col span={6}>
            <Statistic title="噪声点数量" value={result.n_noise} />
          </Col>
          <Col span={6}>
            <Statistic title="总样本数" value={result.total_samples} />
          </Col>
          <Col span={6}>
            {result.purity && (
              <Statistic title="聚类纯度" value={result.purity * 100} precision={2} suffix="%" />
            )}
          </Col>
        </Row>

        {/* 散点图 */}
        {scatterOption && (
          <div style={{ marginTop: '24px' }}>
            <ReactECharts
              option={scatterOption}
              style={{ height: '600px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
            />
          </div>
        )}

        {/* 聚类分布标签 */}
        {result.cluster_distribution && (
          <div style={{ marginTop: '16px' }}>
            <Title level={5}>聚类分布</Title>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {Object.entries(result.cluster_distribution).map(([clusterId, count]) => (
                <Tag
                  key={clusterId}
                  style={{
                    backgroundColor: clusterColors[clusterId],
                    color: '#fff',
                    border: 'none'
                  }}
                >
                  {clusterId === '-1' ? '噪声' : `聚类 ${clusterId}`}: {count} 样本
                </Tag>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>未知识别与聚类分析</Title>
      <Text type="secondary">完整的三步流程：特征提取 → 未知识别 → 聚类分析。经过测试，此系统可实现对15种及以上未知应用的识别和聚类，且性能达标</Text>
      
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} md={12}>
          <Card title="第一步：特征提取">
            <Dragger beforeUpload={handlePcapUpload} onRemove={() => setPcapFile(null)} maxCount={1} fileList={pcapFile ? [pcapFile] : []}>
              <p className="ant-upload-drag-icon"><InboxOutlined /></p>
              <p className="ant-upload-text">点击或拖拽包含pcap文件的ZIP压缩包到此区域</p>
            </Dragger>
            <Button type="primary" onClick={handleStartExtraction} disabled={!pcapFile || isExtracting} style={{ marginTop: 16 }} icon={<FileTextOutlined />}>
              {isExtracting ? '提取中...' : '开始提取特征'}
            </Button>
            {isExtracting && (
                <div style={{ marginTop: '16px' }}>
                    <Spin indicator={<SyncOutlined spin />} />
                    <Text strong style={{ marginLeft: '10px' }}>{extractionStatus}</Text>
                </div>
            )}
            {extractionResult && (
              <Card type="inner" style={{ marginTop: '16px' }} title="提取结果">
                {extractionResult.status === 'SUCCESS' ? (
                  <>
                    <Text strong>{extractionResult.message}</Text>
                    <br />
                    <Link href={extractionResult.download_url} target="_blank">
                      <Button icon={<DownloadOutlined />} style={{ marginTop: '8px' }}>
                        下载特征文件 ({extractionResult.filename})
                      </Button>
                    </Link>
                  </>
                ) : (
                  <Text type="danger">{extractionResult.error || '未知错误'}</Text>
                )}
              </Card>
            )}
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="第二步：模型分析">
            <Dragger beforeUpload={handleTsvUpload} onRemove={() => setTsvFile(null)} maxCount={1} fileList={tsvFile ? [tsvFile] : []}>
              <p className="ant-upload-drag-icon"><UploadOutlined /></p>
              <p className="ant-upload-text">点击或拖拽上一步生成的特征文件 (.tsv) 到此</p>
            </Dragger>
            <Button type="primary" onClick={handleStartPrediction} disabled={!tsvFile || isPredicting} style={{ marginTop: 16 }}>
              {isPredicting ? '分析中...' : '开始分析'}
            </Button>
             {isPredicting && (
                <div style={{ marginTop: '16px' }}>
                    <Spin indicator={<SyncOutlined spin />} />
                    <Text strong style={{ marginLeft: '10px' }}>{predictionStatus}</Text>
                </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 第三步：聚类分析 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} md={12}>
          <Card title="第三步A：对比学习特征提取">
            <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
              先进行5轮对比学习微调，然后提取64维高质量特征
            </Text>
            <Dragger
              beforeUpload={handleContrastiveUpload}
              onRemove={() => setContrastiveFile(null)}
              maxCount={1}
              fileList={contrastiveFile ? [contrastiveFile] : []}
            >
              <p className="ant-upload-drag-icon"><UploadOutlined /></p>
              <p className="ant-upload-text">上传第二步的预测结果文件 (.tsv)</p>
            </Dragger>
            <Button
              type="primary"
              onClick={handleStartContrastiveExtraction}
              disabled={!contrastiveFile || isContrastiveExtracting}
              style={{ marginTop: 16 }}
            >
              {isContrastiveExtracting ? '提取中...' : '提取对比学习特征'}
            </Button>
            {isContrastiveExtracting && (
              <div style={{ marginTop: '16px' }}>
                <Spin indicator={<SyncOutlined spin />} />
                <Text strong style={{ marginLeft: '10px' }}>{contrastiveStatus}</Text>
              </div>
            )}
            {contrastiveResult && (
              <Card type="inner" style={{ marginTop: '16px' }} title="特征提取结果">
                {contrastiveResult.status === 'SUCCESS' ? (
                  <>
                    <Text strong>{contrastiveResult.message}</Text>
                    <br />
                    <Text type="secondary">
                      完成5轮对比学习微调，提取了 {contrastiveResult.num_samples} 个样本的64维特征，
                      共 {contrastiveResult.num_classes} 个类别
                    </Text>
                    <br />
                    <Link href={`/api/unknown_traffic/download/${contrastiveResult.filename}`} target="_blank">
                      <Button icon={<DownloadOutlined />} style={{ marginTop: '8px' }}>
                        下载特征文件 ({contrastiveResult.filename})
                      </Button>
                    </Link>
                  </>
                ) : (
                  <Text type="danger">{contrastiveResult.error || '未知错误'}</Text>
                )}
              </Card>
            )}
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="第三步B：聚类分析">
            <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
              使用UMAP+HDBSCAN算法进行聚类分析 (min_cluster_size=25, umap_neighbors=5)
            </Text>
            <Dragger
              beforeUpload={handleClusteringUpload}
              onRemove={() => setClusteringFile(null)}
              maxCount={1}
              fileList={clusteringFile ? [clusteringFile] : []}
            >
              <p className="ant-upload-drag-icon"><UploadOutlined /></p>
              <p className="ant-upload-text">上传第三步A生成的特征文件 (.tsv)</p>
            </Dragger>
            <Button
              type="primary"
              onClick={handleStartClustering}
              disabled={!clusteringFile || isClustering}
              style={{ marginTop: 16 }}
            >
              {isClustering ? '聚类中...' : '开始聚类分析'}
            </Button>
            {isClustering && (
              <div style={{ marginTop: '16px' }}>
                <Spin indicator={<SyncOutlined spin />} />
                <Text strong style={{ marginLeft: '10px' }}>{clusteringStatus}</Text>
              </div>
            )}
            {clusteringResult && (
              <Card type="inner" style={{ marginTop: '16px' }} title="聚类结果">
                {clusteringResult.status === 'SUCCESS' ? (
                  <>
                    <Text strong>{clusteringResult.message}</Text>
                    <br />
                    <Link href={`/api/unknown_traffic/download/${clusteringResult.filename}`} target="_blank">
                      <Button icon={<DownloadOutlined />} style={{ marginTop: '8px' }}>
                        下载聚类结果 ({clusteringResult.filename})
                      </Button>
                    </Link>
                  </>
                ) : (
                  <Text type="danger">{clusteringResult.error || '未知错误'}</Text>
                )}
              </Card>
            )}
          </Card>
        </Col>
      </Row>

      {predictionResult && predictionResult.status === 'SUCCESS' && (
        <Card title="分析结果" style={{ marginTop: '24px' }}>
            <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="总体准确率" value={predictionResult.accuracy * 100} precision={2} suffix="%" />
                </Col>
                <Col span={6}>
                  <Statistic title="总样本数" value={predictionResult.total_samples} />
                </Col>
                <Col span={6}>
                  <Statistic title="漏报率 (FNR)" value={predictionResult.leakage_rate_fnr * 100} precision={2} suffix="%" />
                </Col>
                <Col span={6}>
                  <Statistic title="误报率 (FDR)" value={predictionResult.false_discovery_rate_fdr * 100} precision={2} suffix="%" />
                </Col>
            </Row>
            <Divider />
            <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  {renderConfusionMatrix(predictionResult.confusion_matrix)}
                </Col>
                <Col xs={24} md={12}>
                   <Title level={5}>未知流量样本</Title>
                   <Statistic title="模型识别出的未知样本数量" value={predictionResult.unknown_samples_found} />
                    {predictionResult.unknown_download_url && (
                        <Link href={predictionResult.unknown_download_url} target="_blank">
                            <Button icon={<DownloadOutlined />} style={{ marginTop: '16px' }}>
                                下载未知流量样本文件
                            </Button>
                        </Link>
                    )}
                    {!predictionResult.unknown_download_url && predictionResult.unknown_samples_found > 0 && (
                        <Tag color="warning" style={{ marginTop: '16px' }}>无法生成未知样本文件，请检查后端。</Tag>
                    )}
                     {!predictionResult.unknown_samples_found && (
                        <Tag color="success" style={{ marginTop: '16px' }}>未发现新的未知流量样本。</Tag>
                    )}
                </Col>
            </Row>
        </Card>
      )}
      {predictionResult && predictionResult.status === 'FAILURE' && (
        <Card title="分析结果" style={{ marginTop: '24px' }}>
           <Text type="danger">{predictionResult.error || '任务执行失败，请检查后端日志。'}</Text>
        </Card>
      )}

      {/* 聚类分析结果 */}
      {clusteringResult && clusteringResult.status === 'SUCCESS' && (
        <Card title="聚类分析结果" style={{ marginTop: '24px' }}>
          {renderClusteringResults(clusteringResult)}
        </Card>
      )}
      {clusteringResult && clusteringResult.status === 'FAILURE' && (
        <Card title="聚类分析结果" style={{ marginTop: '24px' }}>
          <Text type="danger">{clusteringResult.error || '聚类分析失败，请检查后端日志。'}</Text>
        </Card>
      )}
    </div>
  );
};

export default UnknownTrafficPage;