import React, { useState, useEffect } from 'react';
import { InboxOutlined, CameraOutlined, BarChartOutlined } from '@ant-design/icons';
import { message, Upload, Card, Table, Space, Spin, Typography, Alert, Image, Descriptions, Tabs, Button, List, Select, Row, Col, Statistic } from 'antd';
import axios from 'axios';
import '../App.css';

const { Dragger } = Upload;
const { TabPane } = Tabs;
const { Text } = Typography;

// 混淆矩阵组件
const ConfusionMatrix = ({ data, classNames }) => {
    const columns = [
        { title: '真实 \\ 预测', dataIndex: 'trueLabel', key: 'trueLabel', width: 120 },
        ...classNames.map((name) => ({
            title: name,
            dataIndex: name,
            key: name,
            align: 'center',
        }))
    ];
    const dataSource = data.map((row, i) => {
        const rowData = { key: i, trueLabel: classNames[i] };
        row.forEach((val, j) => {
            rowData[classNames[j]] = val;
        });
        return rowData;
    });

    return <Table columns={columns} dataSource={dataSource} pagination={false} bordered size="small" />;
};

const SingleFileAnalyzer = () => {
    const [analysisResult, setAnalysisResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const [taskId, setTaskId] = useState(null);
    const [pollingStatus, setPollingStatus] = useState('');

    useEffect(() => {
        let interval;
        if (taskId) {
            setPollingStatus('已提交分析，正在等待结果...');
            interval = setInterval(async () => {
                try {
                    const response = await axios.get(`/api/results/${taskId}`);
                    const { state, result } = response.data;
                    
                    if (state === 'SUCCESS') {
                        setAnalysisResult(result);
                        setLoading(false);
                        setTaskId(null);
                        clearInterval(interval);
                        message.success('分析完成！');
                    } else if (state === 'FAILURE') {
                        setAnalysisResult({ status: '失败', error: result?.error || '未知错误' });
                        setLoading(false);
                        setTaskId(null);
                        clearInterval(interval);
                        message.error(`分析失败: ${result?.error || '未知错误'}`);
                    } else {
                         setPollingStatus(response.data.status || state);
                    }
                } catch (error) {
                    console.error('轮询结果失败:', error);
                    setLoading(false);
                    setTaskId(null);
                    clearInterval(interval);
                    message.error('无法获取分析结果。');
                }
            }, 2000);
        }
        return () => clearInterval(interval);
    }, [taskId]);

    const handleUpload = async (options) => {
        const { file } = options;
        const formData = new FormData();
        formData.append('file', file);

        setLoading(true);
        setAnalysisResult(null);
        setPollingStatus('正在上传文件...');

        try {
            const response = await axios.post('/api/analyze/yatc', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setTaskId(response.data.task_id);
            message.success(`${file.name} 文件已提交分析，请稍候...`);
        } catch (error) {
            console.error('上传失败:', error);
            message.error(`${file.name} 文件上传失败。`);
            setLoading(false);
        }
    };

    const props = {
        name: 'file',
        multiple: false,
        customRequest: handleUpload,
        showUploadList: false,
    };
    
    const renderResult = () => {
        if (!analysisResult) return null;
        if (analysisResult.status === '失败') {
            return <Alert message={`分析失败: ${analysisResult.error}`} type="error" showIcon />;
        }
        const details = analysisResult.details || {};
        return (
             <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
                <div style={{ flex: 1 }}>
                    <Descriptions title="分析详情" bordered column={1}>
                        <Descriptions.Item label="模型名称">{details.model_name || 'N/A'}</Descriptions.Item>
                        <Descriptions.Item label="预测类别">{analysisResult.prediction}</Descriptions.Item>
                        <Descriptions.Item label="置信度">{`${(analysisResult.confidence * 100).toFixed(2)}%`}</Descriptions.Item>
                        <Descriptions.Item label="处理摘要">{details.info || 'N/A'}</Descriptions.Item>
                    </Descriptions>
                </div>
                {details.image_base64 && (
                    <div style={{ flex: 1, textAlign: 'center' }}>
                        <Typography.Title level={5}>流量特征图 (40x40)</Typography.Title>
                        <Image
                            width={200}
                            src={`data:image/png;base64,${details.image_base64}`}
                            alt="流量特征图"
                            style={{ border: '1px solid #d9d9d9', borderRadius: '2px' }}
                        />
                    </div>
                )}
            </div>
        );
    };

    return (
        <Space direction="vertical" size="large" style={{ width: '100%'}}>
            <Dragger {...props} disabled={loading} style={{ width: '700px', margin: '0 auto' }}>
                <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                <p className="ant-upload-text">点击或拖拽 PCAP 文件到此区域以上传</p>
                <p className="ant-upload-hint">支持单个文件的上传分析。</p>
            </Dragger>
            <Card title="分析结果" style={{ width: '700px', margin: '20px auto 0' }}>
                {loading ? (
                    <div style={{ textAlign: 'center' }}>
                        <Spin />
                        <p style={{ marginTop: 10 }}>{pollingStatus}</p>
                    </div>
                ) : renderResult()}
            </Card>
        </Space>
    );
};

const BatchFileEvaluator = () => {
    const [fileList, setFileList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [analysisResult, setAnalysisResult] = useState(null);
    
    // 我们需要一个类别列表，这里先硬编码，后续可以从API获取
    const classNames = [
        '勒索软件', '恶意文件', '扫描探测', '木马流量', 
        '致瘫攻击', '良性', '隐蔽传输', '高危漏洞'
    ];

    const handleFileChange = (info) => {
        // 使用函数式更新，确保我们总是基于最新的状态进行操作
        setFileList(prevList => {
            // 创建一个新列表来存储更新后的文件
            const newList = [...info.fileList];
            
            // 确保每个文件对象都有一个 'realLabel' 属性
            newList.forEach(file => {
                if (!file.realLabel) {
                    file.realLabel = classNames[5]; // 默认为 "良性"
                }
            });
            return newList;
        });
    };

    const handleLabelChange = (uid, value) => {
        setFileList(prevList => 
            prevList.map(file => 
                file.uid === uid ? { ...file, realLabel: value } : file
            )
        );
    };

    const handleUpload = async () => {
        const formData = new FormData();
        const labels = {};

        fileList.forEach(file => {
            formData.append('files', file.originFileObj || file);
            labels[file.name] = file.realLabel;
        });
        
        formData.append('labels', JSON.stringify(labels));
        
        setLoading(true);
        setAnalysisResult(null); // 清空旧结果

        try {
            const response = await axios.post('/api/analyze/yatc/batch', formData, {
                headers: {
                    // Content-Type 会由浏览器根据 FormData 自动设置
                },
            });
            // 开始轮询总任务ID
            // 注意: 这里的逻辑需要根据您的 get_task_results 端点的具体返回格式来调整
            // 我们假设它能处理链式任务的ID并返回最终结果
            const taskId = response.data.task_id;
            pollResults(taskId);

        } catch (error) {
            console.error('批量上传失败:', error);
            message.error('批量分析任务提交失败。');
            setLoading(false);
        }
    };
    
    const pollResults = (taskId) => {
        const interval = setInterval(async () => {
            try {
                const response = await axios.get(`/api/results/${taskId}`);
                const { state, result } = response.data; // 获取顶层的 state 和 result

                if (state === 'SUCCESS') {
                    // 实际的报告和摘要在嵌套的 result 对象中
                    setAnalysisResult({ summary: result.summary, report: result.report });
                    setLoading(false);
                    clearInterval(interval);
                    message.success('批量分析完成！');
                } else if (state === 'FAILURE') {
                    setLoading(false);
                    clearInterval(interval);
                    // 错误信息可能在 result 中，也可能在顶层
                    const errorMsg = result?.error || response.data.status || '未知错误';
                    message.error(`分析任务失败: ${errorMsg}`);
                }
            } catch (error) {
                setLoading(false);
                clearInterval(interval);
                message.error('无法获取分析结果。');
            }
        }, 3000); // 每3秒轮询一次
    };

    const props = {
        multiple: true,
        onChange: handleFileChange,
        beforeUpload: (file, fileList) => {
            // 通过返回 false 来阻止自动上传，我们将在点击按钮后手动处理
            return false;
        },
        fileList,
    };
    
    // 为文件列表自定义渲染
    const renderFileList = () => (
        <List
            header={<div>已选择文件列表</div>}
            bordered
            dataSource={fileList}
            renderItem={item => (
                <List.Item
                    actions={[
                        <Select
                            defaultValue={item.realLabel || classNames[5]}
                            style={{ width: 120 }}
                            onChange={(value) => handleLabelChange(item.uid, value)}
                        >
                            {classNames.map(name => <Select.Option key={name} value={name}>{name}</Select.Option>)}
                        </Select>
                    ]}
                >
                    {item.name}
                </List.Item>
            )}
        />
    );

    const reportColumns = [
        { title: '类别', dataIndex: 'category', key: 'category' },
        { title: '正确预测数', dataIndex: 'correct_predictions', key: 'correct_predictions' },
        { title: '总样本数', dataIndex: 'total_samples', key: 'total_samples' },
        { title: '准确率', dataIndex: 'accuracy', key: 'accuracy' },
    ];


    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Dragger {...props}>
                <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                <p className="ant-upload-text">点击或拖拽多个 PCAP 文件到此区域</p>
                <p className="ant-upload-hint">选择文件后，请在下方列表中为每个文件指定其真实的攻击类别。</p>
            </Dragger>

            {fileList.length > 0 && (
                <>
                    {renderFileList()}
                    <Button
                        type="primary"
                        onClick={handleUpload}
                        disabled={fileList.length === 0 || loading}
                        loading={loading}
                        style={{ marginTop: 16 }}
                    >
                        开始批量分析
                    </Button>
                </>
            )}

            {loading && !analysisResult && <div style={{textAlign: 'center'}}><Spin tip="正在分析，请稍候..." /></div>}

            {analysisResult && (
                <Card title="批量评估报告" style={{ marginTop: 20 }}>
                    <Descriptions bordered column={1} title="总体摘要">
                        <Descriptions.Item label="总分析文件数">{analysisResult.summary.total_files_analyzed}</Descriptions.Item>
                        <Descriptions.Item label="总正确预测数">{analysisResult.summary.total_correct_predictions}</Descriptions.Item>
                        <Descriptions.Item label="总体准确率">
                            <Typography.Text strong>{analysisResult.summary.overall_accuracy}</Typography.Text>
                        </Descriptions.Item>
                    </Descriptions>
                    <Table 
                        columns={reportColumns} 
                        dataSource={analysisResult.report} 
                        pagination={false} 
                        rowKey="key"
                        style={{ marginTop: 20 }}
                        title={() => '各类别详细统计'}
                    />
                </Card>
            )}
        </Space>
    );
};

// ZIP图像评估组件
const ZipImageEvaluator = () => {
    const [zipFile, setZipFile] = useState(null);
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState(null);
    const [error, setError] = useState('');

    const handleZipSubmit = async () => {
        if (!zipFile) {
            setError('请上传一个包含分类图像的ZIP文件。');
            return;
        }

        const formData = new FormData();
        formData.append('zip_file', zipFile);

        setLoading(true);
        setError('');
        setResult(null);

        try {
            const response = await axios.post('/api/evaluate/yatc_images', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            setResult(response.data);
            message.success('YaTC 图像评估成功！');
        } catch (err) {
            setError(err.response?.data?.detail || '评估失败，请检查文件或服务器日志。');
            message.error('YaTC 图像评估失败！');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Space direction="vertical" size="large" style={{width: '700px'}}>
            <Card title="上传ZIP图像文件">
                <Text strong>ZIP文件 (包含分类图像目录)</Text>
                <Upload
                    beforeUpload={file => {
                        setZipFile(file);
                        return false;
                    }}
                    showUploadList={false}
                    accept=".zip"
                >
                    <Button>选择 ZIP 文件</Button>
                </Upload>
                {zipFile && <Text style={{ marginLeft: 8 }}>{zipFile.name}</Text>}

                <div style={{ marginTop: 16 }}>
                    <Alert
                        message="ZIP文件格式说明"
                        description="ZIP文件应包含按类别命名的文件夹，每个文件夹内包含对应类别的PNG图像文件。支持的类别：勒索软件、恶意文件、扫描探测、木马流量、致瘫攻击、良性、隐蔽传输、高危漏洞。"
                        type="info"
                        showIcon
                    />
                </div>

                <Button
                    type="primary"
                    onClick={handleZipSubmit}
                    loading={loading}
                    style={{ marginTop: 16 }}
                    disabled={!zipFile}
                >
                    {loading ? '正在评估...' : '开始评估'}
                </Button>
            </Card>

            {loading && <div style={{textAlign: 'center', marginTop: 20}}><Spin tip="正在评估..." /></div>}

            {error && <Alert message={error} type="error" style={{ marginTop: 16 }} />}

            {result && (
                 <Card title="YaTC 图像评估结果" style={{marginTop: 20}}>
                    <Row gutter={16}>
                        <Col span={6}><Statistic title="准确率 (Accuracy)" value={result.accuracy} precision={4} /></Col>
                        <Col span={6}><Statistic title="精确率 (Precision)" value={result.precision} precision={4} /></Col>
                        <Col span={6}><Statistic title="召回率 (Recall)" value={result.recall} precision={4} /></Col>
                        <Col span={6}><Statistic title="F1 分数" value={result.f1_score} precision={4} /></Col>
                    </Row>
                    <Typography.Title level={5} style={{marginTop: 20}}>混淆矩阵</Typography.Title>
                    <ConfusionMatrix data={result.confusion_matrix} classNames={result.class_names} />
                </Card>
            )}
        </Space>
    );
};

const YaTCPage = () => {
    return (
        <div style={{ width: '100%' }}>
            <Typography.Title level={2}>
                <CameraOutlined /> YaTC 模型分析
            </Typography.Title>
            <Tabs defaultActiveKey="1" centered>
                <TabPane tab="单个文件分析" key="1">
                    <SingleFileAnalyzer />
                </TabPane>
                <TabPane tab="批量文件评估" key="2">
                    <BatchFileEvaluator />
                </TabPane>
                <TabPane tab={<span><BarChartOutlined /> ZIP图像评估</span>} key="3">
                    <ZipImageEvaluator />
                </TabPane>
            </Tabs>
        </div>
    );
};

export default YaTCPage; 